"""
Onnyx Wallet Module

This module provides the Wallet class for managing keys and signatures.
"""

import os
import base64
import hashlib
import logging
from typing import <PERSON>ple, Optional

from ecdsa import Signing<PERSON><PERSON>, Verifying<PERSON>ey, SECP256k1

# Set up logging
logger = logging.getLogger("onnyx.wallet.wallet")

class Wallet:
    """
    Wallet manages keys and signatures for the Onnyx blockchain.
    """
    
    def __init__(self):
        """Initialize the Wallet."""
        pass
    
    def generate_keypair(self) -> Tuple[str, str]:
        """
        Generate a new keypair.
        
        Returns:
            A tuple of (private_key, public_key) in hex format
        """
        # Generate a new private key
        sk = SigningKey.generate(curve=SECP256k1)
        
        # Get the corresponding public key
        vk = sk.get_verifying_key()
        
        # Convert to hex
        private_key = sk.to_string().hex()
        public_key = vk.to_string().hex()
        
        return private_key, public_key
    
    def generate_private_key(self) -> str:
        """
        Generate a new private key in PEM format.
        
        Returns:
            The private key in PEM format
        """
        # Generate a new private key
        sk = SigningKey.generate(curve=SECP256k1)
        
        # Convert to PEM
        private_key_pem = sk.to_pem().decode()
        
        return private_key_pem
    
    def get_public_key(self, private_key: str) -> str:
        """
        Get the public key corresponding to a private key.
        
        Args:
            private_key: The private key in hex format
        
        Returns:
            The public key in hex format
        """
        # Convert from hex
        sk = SigningKey.from_string(bytes.fromhex(private_key), curve=SECP256k1)
        
        # Get the corresponding public key
        vk = sk.get_verifying_key()
        
        # Convert to hex
        public_key = vk.to_string().hex()
        
        return public_key
    
    def sign_message(self, message: str, private_key: str) -> str:
        """
        Sign a message with a private key.
        
        Args:
            message: The message to sign
            private_key: The private key in hex format
        
        Returns:
            The signature in base64 format
        """
        # Convert from hex
        sk = SigningKey.from_string(bytes.fromhex(private_key), curve=SECP256k1)
        
        # Sign the message
        signature = sk.sign(message.encode())
        
        # Convert to base64
        signature_b64 = base64.b64encode(signature).decode()
        
        return signature_b64
    
    def verify_signature(self, message: str, signature: str, public_key: str) -> bool:
        """
        Verify a signature with a public key.
        
        Args:
            message: The message that was signed
            signature: The signature in base64 format
            public_key: The public key in hex format
        
        Returns:
            True if the signature is valid, False otherwise
        """
        try:
            # Convert from hex
            vk = VerifyingKey.from_string(bytes.fromhex(public_key), curve=SECP256k1)
            
            # Convert from base64
            signature_bytes = base64.b64decode(signature)
            
            # Verify the signature
            return vk.verify(signature_bytes, message.encode())
        except Exception as e:
            logger.error(f"Error verifying signature: {str(e)}")
            return False
    
    def create_address(self, public_key: str) -> str:
        """
        Create an address from a public key.
        
        Args:
            public_key: The public key in hex format
        
        Returns:
            The address in hex format
        """
        # Hash the public key
        h = hashlib.sha256(bytes.fromhex(public_key)).digest()
        h = hashlib.new("ripemd160", h).digest()
        
        # Convert to hex
        address = h.hex()
        
        return address
