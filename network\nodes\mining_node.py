"""
ONNYX P2P Mining Node
Specialized node for distributed mining with biblical compliance
"""

import asyncio
import json
import time
import logging
import uuid
from typing import Dict, List, Optional
from dataclasses import dataclass

from network.p2p.peer_manager import Peer<PERSON>anager, NodeType
from network.mining.p2p_miner import P2PMiner
from network.mining.mining_coordinator import MiningCoordinator
from network.discovery.bootstrap import DiscoveryClient
from network.sync.blockchain_sync import BlockchainSync

logger = logging.getLogger(__name__)

@dataclass
class MiningNodeConfig:
    miner_identity: str
    tribal_code: str
    mining_power: float
    port: int
    bootstrap_nodes: List[tuple]
    covenant_tier: int = 1
    sabbath_enforcement: bool = True

class P2PMiningNode:
    """
    P2P Mining Node that participates in distributed covenant mining
    """
    
    def __init__(self, config: MiningNodeConfig):
        self.config = config
        self.node_id = f"miner_{config.tribal_code.lower()}_{config.miner_identity[:8]}"
        self.running = False
        
        # Initialize P2P components
        self.peer_manager = PeerManager(
            node_id=self.node_id,
            node_type=NodeType.FULL_NODE,  # Mining nodes are full nodes
            port=config.port
        )
        
        # Initialize blockchain sync
        self.blockchain_sync = BlockchainSync(self.peer_manager)
        
        # Initialize discovery client
        self.discovery_client = DiscoveryClient(config.bootstrap_nodes)
        
        # Initialize mining components
        self.p2p_miner = None
        self.mining_coordinator = None
        
        # Mining node attributes
        self.miner_identity = config.miner_identity
        self.tribal_code = config.tribal_code
        self.mining_power = config.mining_power
        self.covenant_tier = config.covenant_tier
        self.sabbath_enforcement = config.sabbath_enforcement
        
        # Performance tracking
        self.blocks_mined = 0
        self.mining_attempts = 0
        self.total_rewards = 0.0
        self.uptime_start = int(time.time())
        
        logger.info(f"Initialized P2P Mining Node: {self.miner_identity} ({self.tribal_code})")
    
    async def start(self) -> bool:
        """Start the P2P mining node"""
        try:
            logger.info(f"Starting P2P Mining Node: {self.miner_identity}")
            
            # Start P2P server
            await self.peer_manager.start_server()
            
            # Register with bootstrap nodes
            peer_info = {
                "peer_id": self.node_id,
                "address": "127.0.0.1",  # In production, would be actual IP
                "port": self.config.port,
                "node_type": "full_node",
                "tribal_code": self.tribal_code,
                "voting_weight": 0,  # Miners don't vote directly
                "covenant_tier": self.covenant_tier,
                "reputation": 100,
                "mining_power": self.mining_power
            }
            
            registration_success = await self.discovery_client.register_with_bootstrap(peer_info)
            if not registration_success:
                logger.warning("Failed to register with bootstrap nodes")
            
            # Discover and connect to peers
            await self._discover_and_connect_peers()
            
            # Start blockchain synchronization
            await self.blockchain_sync.start_sync()
            
            # Initialize mining components
            await self._initialize_mining_components()
            
            # Register as miner with the network
            await self._register_with_mining_coordinator()
            
            # Start mining activities
            await self._start_mining_activities()
            
            self.running = True
            logger.info(f"P2P Mining Node {self.miner_identity} started successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start P2P mining node: {e}")
            return False
    
    async def stop(self):
        """Stop the P2P mining node"""
        try:
            self.running = False
            
            # Stop mining activities
            if self.p2p_miner:
                await self.p2p_miner.stop_mining()
            
            # Stop P2P server
            await self.peer_manager.stop_server()
            
            logger.info(f"P2P Mining Node {self.miner_identity} stopped")
        except Exception as e:
            logger.error(f"Error stopping P2P mining node: {e}")
    
    async def _discover_and_connect_peers(self):
        """Discover and connect to network peers"""
        try:
            # Discover peers, prioritizing tribal elders and other miners
            peers = await self.discovery_client.discover_peers(
                requester_id=self.node_id,
                node_type="full_node",
                max_peers=20
            )
            
            # Connect to discovered peers
            connection_count = 0
            elder_connections = 0
            miner_connections = 0
            
            for peer in peers:
                if peer['peer_id'] != self.node_id:
                    success = await self.peer_manager.connect_to_peer(
                        peer['address'], peer['port']
                    )
                    if success:
                        connection_count += 1
                        if peer.get('node_type') == 'tribal_elder':
                            elder_connections += 1
                        elif peer.get('mining_power', 0) > 0:
                            miner_connections += 1
            
            logger.info(f"Connected to {connection_count} peers ({elder_connections} elders, {miner_connections} miners)")
            
        except Exception as e:
            logger.error(f"Peer discovery error: {e}")
    
    async def _initialize_mining_components(self):
        """Initialize mining components"""
        try:
            # Initialize consensus engine (simplified for mining node)
            from network.consensus.proof_of_covenant import ProofOfCovenant
            consensus = ProofOfCovenant(self.peer_manager)
            
            # Initialize P2P miner
            self.p2p_miner = P2PMiner(
                peer_manager=self.peer_manager,
                consensus_engine=consensus,
                miner_identity=self.miner_identity,
                tribal_code=self.tribal_code
            )
            
            # Initialize mining coordinator (if this node will coordinate)
            tribal_elders = self.peer_manager.get_tribal_elders()
            if len(tribal_elders) >= 8:  # Only coordinate if enough elders
                self.mining_coordinator = MiningCoordinator(
                    peer_manager=self.peer_manager,
                    consensus_engine=consensus
                )
                await self.mining_coordinator.start_coordination()
            
            logger.info("Mining components initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing mining components: {e}")
            raise
    
    async def _register_with_mining_coordinator(self):
        """Register this node as a miner with the network coordinator"""
        try:
            if self.mining_coordinator:
                # Register with our own coordinator
                success = await self.mining_coordinator.register_miner(
                    miner_id=self.miner_identity,
                    tribal_code=self.tribal_code,
                    node_type="mining_node",
                    mining_power=self.mining_power
                )
                
                if success:
                    logger.info("Registered with local mining coordinator")
            else:
                # Send registration request to network
                message = self.peer_manager.NetworkMessage(
                    message_type="miner_registration",
                    sender_id=self.node_id,
                    recipient_id=None,  # Broadcast to find coordinator
                    data={
                        "miner_id": self.miner_identity,
                        "tribal_code": self.tribal_code,
                        "node_type": "mining_node",
                        "mining_power": self.mining_power,
                        "covenant_tier": self.covenant_tier
                    },
                    timestamp=int(time.time())
                )
                
                await self.peer_manager.broadcast_message(message)
                logger.info("Sent miner registration request to network")
            
        except Exception as e:
            logger.error(f"Error registering with mining coordinator: {e}")
    
    async def _start_mining_activities(self):
        """Start mining activities"""
        try:
            # Start P2P mining
            if self.p2p_miner:
                mining_started = await self.p2p_miner.start_mining()
                if mining_started:
                    logger.info("P2P mining started successfully")
                else:
                    logger.warning("Failed to start P2P mining")
            
            # Start background monitoring tasks
            asyncio.create_task(self._mining_performance_monitor())
            asyncio.create_task(self._network_health_monitor())
            asyncio.create_task(self._biblical_compliance_monitor())
            
        except Exception as e:
            logger.error(f"Error starting mining activities: {e}")
    
    async def _mining_performance_monitor(self):
        """Monitor mining performance and statistics"""
        while self.running:
            try:
                if self.p2p_miner:
                    stats = self.p2p_miner.get_mining_statistics()
                    
                    # Update local statistics
                    self.blocks_mined = stats["blocks_mined"]
                    self.mining_attempts = stats["proposals_submitted"]
                    self.total_rewards = stats["total_rewards_earned"]
                    
                    # Log performance periodically
                    if int(time.time()) % 600 == 0:  # Every 10 minutes
                        logger.info(f"Mining Performance - Blocks: {self.blocks_mined}, "
                                  f"Attempts: {self.mining_attempts}, "
                                  f"Rewards: {self.total_rewards:.2f}")
                
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                logger.error(f"Mining performance monitor error: {e}")
                await asyncio.sleep(60)
    
    async def _network_health_monitor(self):
        """Monitor network health and connectivity"""
        while self.running:
            try:
                # Check peer connections
                connected_peers = len(self.peer_manager.get_connected_peers())
                tribal_elders = len(self.peer_manager.get_tribal_elders())
                
                # Ensure minimum connectivity for mining
                if connected_peers < 5:
                    logger.warning(f"Low peer connectivity: {connected_peers} peers")
                    await self._discover_and_connect_peers()
                
                if tribal_elders < 8:
                    logger.warning(f"Insufficient tribal elders for consensus: {tribal_elders}")
                
                # Check blockchain sync status
                sync_status = self.blockchain_sync.get_sync_status()
                if sync_status["status"] != "synced":
                    logger.warning(f"Blockchain not synced: {sync_status['status']}")
                
                await asyncio.sleep(300)  # Check every 5 minutes
                
            except Exception as e:
                logger.error(f"Network health monitor error: {e}")
                await asyncio.sleep(300)
    
    async def _biblical_compliance_monitor(self):
        """Monitor biblical compliance requirements"""
        while self.running:
            try:
                if self.p2p_miner:
                    # Check Sabbath compliance
                    if self.sabbath_enforcement:
                        is_sabbath = await self._is_sabbath_period()
                        current_status = self.p2p_miner.mining_status
                        
                        if is_sabbath and current_status.value != "sabbath_rest":
                            logger.info("Sabbath period - stopping mining for rest")
                            await self.p2p_miner.stop_mining()
                        elif not is_sabbath and current_status.value == "sabbath_rest":
                            logger.info("Sabbath period ended - resuming mining")
                            await self.p2p_miner.start_mining()
                    
                    # Update compliance score
                    stats = self.p2p_miner.get_mining_statistics()
                    compliance_score = stats["biblical_compliance_score"]
                    
                    if compliance_score < 0.8:
                        logger.warning(f"Low biblical compliance score: {compliance_score}")
                
                await asyncio.sleep(600)  # Check every 10 minutes
                
            except Exception as e:
                logger.error(f"Biblical compliance monitor error: {e}")
                await asyncio.sleep(600)
    
    async def _is_sabbath_period(self) -> bool:
        """Check if it's currently Sabbath period"""
        import time
        day_of_week = time.gmtime().tm_wday
        return day_of_week == 5  # Saturday
    
    # Mining Control Methods
    async def start_mining(self) -> bool:
        """Manually start mining"""
        if self.p2p_miner:
            return await self.p2p_miner.start_mining()
        return False
    
    async def stop_mining(self):
        """Manually stop mining"""
        if self.p2p_miner:
            await self.p2p_miner.stop_mining()
    
    async def submit_mining_proposal(self, block_height: int) -> Optional[str]:
        """Submit a mining proposal for a specific block height"""
        if self.p2p_miner:
            proposal = await self.p2p_miner._create_mining_proposal(block_height)
            if proposal:
                await self.p2p_miner._submit_proposal_for_validation(proposal)
                return proposal.proposal_id
        return None
    
    # Public API Methods
    def get_node_status(self) -> dict:
        """Get comprehensive node status"""
        uptime = int(time.time()) - self.uptime_start
        
        status = {
            "node_id": self.node_id,
            "miner_identity": self.miner_identity,
            "tribal_code": self.tribal_code,
            "mining_power": self.mining_power,
            "covenant_tier": self.covenant_tier,
            "running": self.running,
            "uptime_seconds": uptime,
            "connected_peers": len(self.peer_manager.get_connected_peers()),
            "tribal_elders_connected": len(self.peer_manager.get_tribal_elders()),
            "blocks_mined": self.blocks_mined,
            "mining_attempts": self.mining_attempts,
            "total_rewards": self.total_rewards,
            "sync_status": self.blockchain_sync.get_sync_status(),
            "network_stats": self.peer_manager.get_network_stats()
        }
        
        # Add mining-specific status
        if self.p2p_miner:
            mining_stats = self.p2p_miner.get_mining_statistics()
            status.update({
                "mining_status": mining_stats["mining_status"],
                "biblical_compliance_score": mining_stats["biblical_compliance_score"],
                "active_proposals": mining_stats["active_proposals"],
                "approval_rate": mining_stats["approval_rate"]
            })
        
        # Add coordinator status if applicable
        if self.mining_coordinator:
            coordinator_stats = self.mining_coordinator.get_network_mining_status()
            status["coordinator_status"] = coordinator_stats
        
        return status
    
    def get_mining_statistics(self) -> dict:
        """Get detailed mining statistics"""
        base_stats = {
            "miner_identity": self.miner_identity,
            "tribal_code": self.tribal_code,
            "mining_power": self.mining_power,
            "blocks_mined": self.blocks_mined,
            "mining_attempts": self.mining_attempts,
            "total_rewards": self.total_rewards,
            "success_rate": self.blocks_mined / max(self.mining_attempts, 1),
            "average_reward_per_block": self.total_rewards / max(self.blocks_mined, 1),
            "uptime_hours": (int(time.time()) - self.uptime_start) / 3600
        }
        
        if self.p2p_miner:
            p2p_stats = self.p2p_miner.get_mining_statistics()
            base_stats.update(p2p_stats)
        
        return base_stats
    
    def get_mining_history(self) -> List[dict]:
        """Get mining history"""
        if self.p2p_miner:
            return self.p2p_miner.get_mining_history()
        return []
    
    def get_current_proposal(self) -> Optional[dict]:
        """Get current mining proposal"""
        if self.p2p_miner:
            return self.p2p_miner.get_current_proposal()
        return None
    
    def get_network_mining_status(self) -> dict:
        """Get network-wide mining status"""
        if self.mining_coordinator:
            return self.mining_coordinator.get_network_mining_status()
        return {"error": "No mining coordinator available"}

# Utility Functions
def create_mining_node_configs(count: int = 5) -> List[MiningNodeConfig]:
    """Create configurations for multiple mining nodes"""
    configs = []
    tribal_codes = ["JU", "LE", "EP", "BE", "SI", "MA", "IS", "ZE", "NA", "GA", "AS", "RE"]
    bootstrap_nodes = [("127.0.0.1", 8766)]
    
    for i in range(count):
        tribal_code = tribal_codes[i % len(tribal_codes)]
        
        config = MiningNodeConfig(
            miner_identity=f"miner_{tribal_code.lower()}_{uuid.uuid4().hex[:8]}",
            tribal_code=tribal_code,
            mining_power=1.0 + (i * 0.1),  # Varying mining power
            port=8900 + i,
            bootstrap_nodes=bootstrap_nodes,
            covenant_tier=1 + (i % 3),  # Tiers 1-3
            sabbath_enforcement=True
        )
        
        configs.append(config)
    
    return configs
