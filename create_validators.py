#!/usr/bin/env python3
"""
Quick script to create sample validators for testing
"""

import sys
import os
import time
import json

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

from shared.db.db import db

def create_sample_validators():
    """Create sample validators if none exist."""
    try:
        # Check if validators already exist
        existing = db.query("SELECT COUNT(*) as count FROM selas")
        if existing and existing[0]['count'] > 0:
            print(f"Found {existing[0]['count']} existing validators")
            return
            
        print("Creating sample validators...")
        
        # Create sample identities first
        identities = [
            ('validator_001', 'TechCorp Solutions', '<EMAIL>', 'us', 'United States'),
            ('validator_002', 'Covenant Agriculture Co-op', '<EMAIL>', 'ca', 'Canada')
        ]
        
        for identity_id, name, email, nation_code, nation_name in identities:
            try:
                db.execute('''
                    INSERT OR REPLACE INTO identities 
                    (identity_id, name, email, nation_code, nation_name, role_class, status, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', [identity_id, name, email, nation_code, nation_name, 'business', 'active', int(time.time())])
                print(f"Created identity: {name}")
            except Exception as e:
                print(f"Error creating identity {name}: {e}")
        
        # Create sample validators
        validators = [
            {
                'sela_id': 'sela_techcorp_001',
                'identity_id': 'validator_001',
                'name': 'TechCorp Solutions',
                'category': 'Technology',
                'description': 'Leading blockchain technology solutions provider',
                'metadata': json.dumps({'business_type': 'Technology Services', 'employees': 50})
            },
            {
                'sela_id': 'sela_agriculture_002', 
                'identity_id': 'validator_002',
                'name': 'Covenant Agriculture Co-op',
                'category': 'Agriculture',
                'description': 'Sustainable farming cooperative following biblical principles',
                'metadata': json.dumps({'business_type': 'Agricultural Cooperative', 'members': 25})
            }
        ]
        
        for validator in validators:
            try:
                db.execute('''
                    INSERT OR REPLACE INTO selas 
                    (sela_id, identity_id, name, category, description, stake_amount, 
                     stake_token_id, status, created_at, updated_at, metadata)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', [
                    validator['sela_id'], validator['identity_id'], validator['name'],
                    validator['category'], validator['description'], 1000,
                    'ONX', 'active', int(time.time()), int(time.time()), validator['metadata']
                ])
                print(f"Created validator: {validator['name']}")
            except Exception as e:
                print(f"Error creating validator {validator['name']}: {e}")
                
        print("Sample validators created successfully!")
        
        # Verify creation
        validators = db.query("SELECT * FROM selas")
        print(f"Total validators in database: {len(validators)}")
        for v in validators:
            print(f"  - {v.get('name', 'Unknown')} ({v.get('category', 'Unknown')})")
        
    except Exception as e:
        print(f"Error creating sample validators: {e}")

if __name__ == "__main__":
    create_sample_validators()
