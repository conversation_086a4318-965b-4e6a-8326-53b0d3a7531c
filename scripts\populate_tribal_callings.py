#!/usr/bin/env python3
"""
Populate Tribal Calling Restrictions Based on Genesis 49
Implements biblical role restrictions for covenant tribes according to their prophetic callings
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

from shared.db.db import db
import time

def populate_tribal_callings():
    """Populate tribal calling restrictions based on Genesis 49 prophecies."""
    print("🏛️ POPULATING TRIBAL CALLING RESTRICTIONS")
    print("=" * 50)
    print("Based on Genesis 49 prophecies and biblical callings")
    
    try:
        # Create the table if it doesn't exist
        db.execute('''
            CREATE TABLE IF NOT EXISTS tribal_calling_restrictions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                tribe_code TEXT NOT NULL,
                tribe_name TEXT NOT NULL,
                calling_category TEXT NOT NULL,
                role_name TEXT NOT NULL,
                role_description TEXT NOT NULL,
                biblical_reference TEXT NOT NULL,
                is_exclusive BOOLEAN DEFAULT TRUE,
                created_at TEXT,
                UNIQUE(tribe_code, role_name)
            )
        ''')
        
        # Clear existing data
        db.execute("DELETE FROM tribal_calling_restrictions")
        
        current_time = time.strftime('%Y-%m-%d %H:%M:%S')
        
        # Define tribal callings based on Genesis 49 and biblical precedent
        tribal_callings = [
            # DAN - "Dan shall judge his people" (Gen 49:16)
            {
                'tribe_code': 'DAN',
                'tribe_name': 'Dan',
                'calling_category': 'Judicial',
                'role_name': 'Judge/Mediator',
                'role_description': 'Legal arbitration, dispute resolution, covenant compliance oversight',
                'biblical_reference': 'Genesis 49:16 - "Dan shall judge his people as one of the tribes of Israel"',
                'is_exclusive': True
            },
            {
                'tribe_code': 'DAN',
                'tribe_name': 'Dan',
                'calling_category': 'Judicial',
                'role_name': 'Legal Arbitrator',
                'role_description': 'Resolve disputes between covenant members according to biblical law',
                'biblical_reference': 'Genesis 49:16',
                'is_exclusive': True
            },
            {
                'tribe_code': 'DAN',
                'tribe_name': 'Dan',
                'calling_category': 'Judicial',
                'role_name': 'Covenant Compliance Officer',
                'role_description': 'Ensure adherence to biblical economic and social principles',
                'biblical_reference': 'Genesis 49:16',
                'is_exclusive': True
            },
            
            # JUDAH - "The scepter shall not depart from Judah" (Gen 49:10)
            {
                'tribe_code': 'JUDAH',
                'tribe_name': 'Judah',
                'calling_category': 'Leadership',
                'role_name': 'Tribal Elder',
                'role_description': 'Leadership and governance of tribal communities',
                'biblical_reference': 'Genesis 49:10 - "The scepter shall not depart from Judah"',
                'is_exclusive': True
            },
            {
                'tribe_code': 'JUDAH',
                'tribe_name': 'Judah',
                'calling_category': 'Leadership',
                'role_name': 'Council Leadership',
                'role_description': 'Executive governance and decision-making authority',
                'biblical_reference': 'Genesis 49:10',
                'is_exclusive': True
            },
            {
                'tribe_code': 'JUDAH',
                'tribe_name': 'Judah',
                'calling_category': 'Leadership',
                'role_name': 'Royal Governance',
                'role_description': 'Highest levels of covenant community leadership',
                'biblical_reference': 'Genesis 49:10',
                'is_exclusive': True
            },
            
            # LEVI - Priesthood calling (Deut 33:10)
            {
                'tribe_code': 'LEVI',
                'tribe_name': 'Levi',
                'calling_category': 'Spiritual',
                'role_name': 'Covenant Teacher',
                'role_description': 'Teaching biblical principles and covenant law',
                'biblical_reference': 'Deuteronomy 33:10 - "They shall teach Jacob your judgments"',
                'is_exclusive': True
            },
            {
                'tribe_code': 'LEVI',
                'tribe_name': 'Levi',
                'calling_category': 'Spiritual',
                'role_name': 'Spiritual Counselor',
                'role_description': 'Providing spiritual guidance and biblical counseling',
                'biblical_reference': 'Deuteronomy 33:10',
                'is_exclusive': True
            },
            {
                'tribe_code': 'LEVI',
                'tribe_name': 'Levi',
                'calling_category': 'Spiritual',
                'role_name': 'Biblical Interpreter',
                'role_description': 'Interpreting scripture and applying biblical law',
                'biblical_reference': 'Deuteronomy 33:10',
                'is_exclusive': True
            },
            
            # BENJAMIN - "Benjamin is a ravenous wolf" (Gen 49:27)
            {
                'tribe_code': 'BENJAMIN',
                'tribe_name': 'Benjamin',
                'calling_category': 'Security',
                'role_name': 'Network Security',
                'role_description': 'Protecting the covenant blockchain network from attacks',
                'biblical_reference': 'Genesis 49:27 - "Benjamin is a ravenous wolf"',
                'is_exclusive': True
            },
            {
                'tribe_code': 'BENJAMIN',
                'tribe_name': 'Benjamin',
                'calling_category': 'Security',
                'role_name': 'Covenant Protection',
                'role_description': 'Defending covenant members and community assets',
                'biblical_reference': 'Genesis 49:27',
                'is_exclusive': True
            },
            {
                'tribe_code': 'BENJAMIN',
                'tribe_name': 'Benjamin',
                'calling_category': 'Security',
                'role_name': 'Defense Coordinator',
                'role_description': 'Coordinating community defense and security measures',
                'biblical_reference': 'Genesis 49:27',
                'is_exclusive': True
            },
            
            # ISSACHAR - "Understanding of the times" (1 Chr 12:32)
            {
                'tribe_code': 'ISSACHAR',
                'tribe_name': 'Issachar',
                'calling_category': 'Temporal',
                'role_name': 'Economic Timing Coordinator',
                'role_description': 'Managing economic cycles and market timing',
                'biblical_reference': '1 Chronicles 12:32 - "Men who had understanding of the times"',
                'is_exclusive': True
            },
            {
                'tribe_code': 'ISSACHAR',
                'tribe_name': 'Issachar',
                'calling_category': 'Temporal',
                'role_name': 'Sabbath/Yovel Coordinator',
                'role_description': 'Coordinating Sabbath and Jubilee cycle observances',
                'biblical_reference': '1 Chronicles 12:32',
                'is_exclusive': True
            },
            {
                'tribe_code': 'ISSACHAR',
                'tribe_name': 'Issachar',
                'calling_category': 'Temporal',
                'role_name': 'Seasonal Planner',
                'role_description': 'Planning community activities according to biblical seasons',
                'biblical_reference': '1 Chronicles 12:32',
                'is_exclusive': True
            },
            
            # ZEBULUN - "Zebulun shall dwell at the haven of the sea" (Gen 49:13)
            {
                'tribe_code': 'ZEBULUN',
                'tribe_name': 'Zebulun',
                'calling_category': 'Commerce',
                'role_name': 'Trade Coordinator',
                'role_description': 'Managing inter-community trade and commerce',
                'biblical_reference': 'Genesis 49:13 - "Zebulun shall dwell at the haven of the sea"',
                'is_exclusive': True
            },
            {
                'tribe_code': 'ZEBULUN',
                'tribe_name': 'Zebulun',
                'calling_category': 'Commerce',
                'role_name': 'Network Facilitator',
                'role_description': 'Facilitating connections between covenant communities',
                'biblical_reference': 'Genesis 49:13',
                'is_exclusive': True
            },
            
            # EPHRAIM - "Ephraim is a trained heifer" (Hos 10:11)
            {
                'tribe_code': 'EPHRAIM',
                'tribe_name': 'Ephraim',
                'calling_category': 'Agricultural',
                'role_name': 'Agricultural Coordinator',
                'role_description': 'Managing community food production and distribution',
                'biblical_reference': 'Hosea 10:11 - "Ephraim is a trained heifer"',
                'is_exclusive': True
            },
            {
                'tribe_code': 'EPHRAIM',
                'tribe_name': 'Ephraim',
                'calling_category': 'Agricultural',
                'role_name': 'Community Builder',
                'role_description': 'Building and maintaining covenant community infrastructure',
                'biblical_reference': 'Hosea 10:11',
                'is_exclusive': True
            },
            
            # MANASSEH - "His glory is like a firstborn bull" (Deut 33:17)
            {
                'tribe_code': 'MANASSEH',
                'tribe_name': 'Manasseh',
                'calling_category': 'Craftsmanship',
                'role_name': 'Master Craftsman',
                'role_description': 'Leading skilled trades and craftsmanship',
                'biblical_reference': 'Deuteronomy 33:17 - "His glory is like a firstborn bull"',
                'is_exclusive': True
            },
            {
                'tribe_code': 'MANASSEH',
                'tribe_name': 'Manasseh',
                'calling_category': 'Craftsmanship',
                'role_name': 'Commerce Leader',
                'role_description': 'Leading business and commercial enterprises',
                'biblical_reference': 'Deuteronomy 33:17',
                'is_exclusive': True
            }
        ]
        
        # Insert all tribal callings
        for calling in tribal_callings:
            try:
                db.execute('''
                    INSERT OR IGNORE INTO tribal_calling_restrictions
                    (tribe_code, tribe_name, calling_category, role_name, role_description, biblical_reference, is_exclusive, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    calling['tribe_code'],
                    calling['tribe_name'],
                    calling['calling_category'],
                    calling['role_name'],
                    calling['role_description'],
                    calling['biblical_reference'],
                    calling['is_exclusive'],
                    current_time
                ))
                print(f"   ✅ Added: {calling['tribe_name']} - {calling['role_name']}")
            except Exception as e:
                print(f"   ❌ Error adding {calling['tribe_name']} - {calling['role_name']}: {e}")
        
        # Verify the population
        total_callings = db.query_one("SELECT COUNT(*) as count FROM tribal_calling_restrictions")
        calling_count = total_callings['count'] if total_callings else 0
        
        print(f"\n🎉 Tribal calling restrictions populated!")
        print(f"📊 Total callings: {calling_count}")
        
        # Show summary by tribe
        tribes_summary = db.query("""
            SELECT tribe_name, COUNT(*) as role_count
            FROM tribal_calling_restrictions
            GROUP BY tribe_name
            ORDER BY tribe_name
        """)
        
        print("\n📋 Callings by Tribe:")
        for tribe in tribes_summary:
            print(f"   {tribe['tribe_name']}: {tribe['role_count']} exclusive roles")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = populate_tribal_callings()
    if success:
        print("\n✅ Tribal calling restrictions successfully populated!")
        print("🌟 Eden Mode Step 3 now supports biblical role restrictions.")
    else:
        print("\n❌ Population failed. Please check the error messages above.")
