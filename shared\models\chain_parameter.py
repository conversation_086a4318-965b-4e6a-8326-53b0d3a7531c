"""
Onnyx Chain Parameter Model

This module defines the ChainParameter model for the Onnyx blockchain.
"""

import json
import time
import logging
import sqlite3
from typing import Dict, Any, List, Optional, Union

from shared.config.config import onnyx_config
from shared.models.base_model import BaseModel

# Set up logging
logger = logging.getLogger("onnyx.models.chain_parameter")

class ChainParameter(BaseModel):
    """
    ChainParameter model for the Onnyx blockchain.
    
    A ChainParameter represents a configurable parameter in the Onnyx ecosystem.
    """
    
    TABLE_NAME = "chain_parameters"
    PRIMARY_KEY = "key"
    
    def __init__(
        self,
        key: str,
        value: Any,
        default_value: Any,
        description: str,
        category: str,
        last_updated: int,
        last_updated_by: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize a ChainParameter.
        
        Args:
            key: The parameter key
            value: The parameter value
            default_value: The default value for the parameter
            description: A description of the parameter
            category: The category of the parameter
            last_updated: The timestamp when the parameter was last updated
            last_updated_by: The identity ID of the last updater (optional)
            metadata: Additional metadata for the parameter (optional)
        """
        self.key = key
        self.value = value
        self.default_value = default_value
        self.description = description
        self.category = category
        self.last_updated = last_updated
        self.last_updated_by = last_updated_by
        self.metadata = metadata or {}
    
    @classmethod
    def create_table(cls) -> None:
        """Create the ChainParameter table if it doesn't exist."""
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()
        
        cursor.execute(f"""
        CREATE TABLE IF NOT EXISTS {cls.TABLE_NAME} (
            {cls.PRIMARY_KEY} TEXT PRIMARY KEY,
            value TEXT NOT NULL,
            default_value TEXT NOT NULL,
            description TEXT NOT NULL,
            category TEXT NOT NULL,
            last_updated INTEGER NOT NULL,
            last_updated_by TEXT,
            metadata TEXT
        )
        """)
        
        conn.commit()
        conn.close()
    
    @classmethod
    def create(
        cls,
        key: str,
        value: Any,
        default_value: Any,
        description: str,
        category: str,
        last_updated: Optional[int] = None,
        last_updated_by: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> "ChainParameter":
        """
        Create a new ChainParameter.
        
        Args:
            key: The parameter key
            value: The parameter value
            default_value: The default value for the parameter
            description: A description of the parameter
            category: The category of the parameter
            last_updated: The timestamp when the parameter was last updated (optional)
            last_updated_by: The identity ID of the last updater (optional)
            metadata: Additional metadata for the parameter (optional)
        
        Returns:
            The created ChainParameter
        """
        # Create the table if it doesn't exist
        cls.create_table()
        
        # Create the ChainParameter
        parameter = cls(
            key=key,
            value=value,
            default_value=default_value,
            description=description,
            category=category,
            last_updated=last_updated or int(time.time()),
            last_updated_by=last_updated_by,
            metadata=metadata
        )
        
        # Save the ChainParameter to the database
        parameter.save()
        
        return parameter
    
    def save(self) -> None:
        """Save the ChainParameter to the database."""
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()
        
        cursor.execute(f"""
        INSERT OR REPLACE INTO {self.TABLE_NAME} (
            {self.PRIMARY_KEY},
            value,
            default_value,
            description,
            category,
            last_updated,
            last_updated_by,
            metadata
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            self.key,
            json.dumps(self.value),
            json.dumps(self.default_value),
            self.description,
            self.category,
            self.last_updated,
            self.last_updated_by,
            json.dumps(self.metadata)
        ))
        
        conn.commit()
        conn.close()
    
    @classmethod
    def get_by_id(cls, key: str) -> Optional["ChainParameter"]:
        """
        Get a ChainParameter by key.
        
        Args:
            key: The parameter key
        
        Returns:
            The ChainParameter, or None if not found
        """
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()
        
        cursor.execute(f"""
        SELECT
            {cls.PRIMARY_KEY},
            value,
            default_value,
            description,
            category,
            last_updated,
            last_updated_by,
            metadata
        FROM {cls.TABLE_NAME}
        WHERE {cls.PRIMARY_KEY} = ?
        """, (key,))
        
        row = cursor.fetchone()
        conn.close()
        
        if row:
            return cls(
                key=row[0],
                value=json.loads(row[1]),
                default_value=json.loads(row[2]),
                description=row[3],
                category=row[4],
                last_updated=row[5],
                last_updated_by=row[6],
                metadata=json.loads(row[7]) if row[7] else {}
            )
        
        return None
    
    @classmethod
    def get_all(cls) -> List["ChainParameter"]:
        """
        Get all ChainParameters.
        
        Returns:
            A list of all ChainParameters
        """
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()
        
        cursor.execute(f"""
        SELECT
            {cls.PRIMARY_KEY},
            value,
            default_value,
            description,
            category,
            last_updated,
            last_updated_by,
            metadata
        FROM {cls.TABLE_NAME}
        """)
        
        rows = cursor.fetchall()
        conn.close()
        
        return [
            cls(
                key=row[0],
                value=json.loads(row[1]),
                default_value=json.loads(row[2]),
                description=row[3],
                category=row[4],
                last_updated=row[5],
                last_updated_by=row[6],
                metadata=json.loads(row[7]) if row[7] else {}
            )
            for row in rows
        ]
    
    @classmethod
    def find_by_category(cls, category: str) -> List["ChainParameter"]:
        """
        Find ChainParameters by category.
        
        Args:
            category: The category to search for
        
        Returns:
            A list of ChainParameters in the category
        """
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()
        
        cursor.execute(f"""
        SELECT
            {cls.PRIMARY_KEY},
            value,
            default_value,
            description,
            category,
            last_updated,
            last_updated_by,
            metadata
        FROM {cls.TABLE_NAME}
        WHERE category = ?
        """, (category,))
        
        rows = cursor.fetchall()
        conn.close()
        
        return [
            cls(
                key=row[0],
                value=json.loads(row[1]),
                default_value=json.loads(row[2]),
                description=row[3],
                category=row[4],
                last_updated=row[5],
                last_updated_by=row[6],
                metadata=json.loads(row[7]) if row[7] else {}
            )
            for row in rows
        ]
    
    def reset(self) -> None:
        """Reset the parameter to its default value."""
        self.value = self.default_value
        self.last_updated = int(time.time())
        self.save()
    
    def update(self, value: Any, updated_by: Optional[str] = None) -> None:
        """
        Update the parameter value.
        
        Args:
            value: The new value
            updated_by: The identity ID of the updater (optional)
        """
        self.value = value
        self.last_updated = int(time.time())
        if updated_by:
            self.last_updated_by = updated_by
        self.save()
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the ChainParameter to a dictionary.
        
        Returns:
            The ChainParameter as a dictionary
        """
        return {
            "key": self.key,
            "value": self.value,
            "default_value": self.default_value,
            "description": self.description,
            "category": self.category,
            "last_updated": self.last_updated,
            "last_updated_by": self.last_updated_by,
            "metadata": self.metadata
        }
