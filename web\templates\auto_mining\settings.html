{% extends "base.html" %}

{% block title %}Auto-Mining Settings - Onnyx Platform{% endblock %}

{% block head %}
<style>
    .settings-section {
        background: var(--glass-bg);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid var(--glass-border);
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 2rem;
        transition: all 0.3s ease;
    }
    
    .settings-section:hover {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(0, 212, 255, 0.3);
    }
    
    .setting-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .setting-item:last-child {
        border-bottom: none;
    }
    
    .setting-label {
        flex: 1;
    }
    
    .setting-title {
        font-family: 'Orbitron', monospace;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 0.25rem;
    }
    
    .setting-description {
        font-size: 0.875rem;
        color: var(--text-tertiary);
        line-height: 1.4;
    }
    
    .setting-control {
        margin-left: 2rem;
    }
    
    .status-indicator {
        display: inline-flex;
        align-items: center;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 500;
        font-family: 'Orbitron', monospace;
    }
    
    .status-running {
        background: rgba(34, 197, 94, 0.2);
        color: #22c55e;
        border: 1px solid rgba(34, 197, 94, 0.3);
    }
    
    .status-stopped {
        background: rgba(107, 114, 128, 0.2);
        color: #9ca3af;
        border: 1px solid rgba(107, 114, 128, 0.3);
    }
    
    .config-display {
        background: rgba(0, 0, 0, 0.3);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 1rem;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 0.875rem;
        color: var(--text-secondary);
        white-space: pre-wrap;
        overflow-x: auto;
    }
    
    .action-button {
        background: var(--glass-bg);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid var(--glass-border);
        border-radius: 8px;
        padding: 0.75rem 1.5rem;
        color: var(--text-primary);
        font-family: 'Orbitron', monospace;
        font-weight: 500;
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
    }
    
    .action-button:hover {
        background: rgba(255, 255, 255, 0.1);
        border-color: var(--cyber-cyan);
        color: var(--cyber-cyan);
        box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);
    }
    
    .action-button.danger:hover {
        border-color: #ef4444;
        color: #ef4444;
        box-shadow: 0 0 15px rgba(239, 68, 68, 0.3);
    }
    
    .action-button.success:hover {
        border-color: #22c55e;
        color: #22c55e;
        box-shadow: 0 0 15px rgba(34, 197, 94, 0.3);
    }
</style>
{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Page Header -->
    <div class="mb-8">
        <div class="flex items-center space-x-4 mb-4">
            <a href="{{ url_for('auto_mining.dashboard') }}" 
               class="glass-button px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300">
                ← Back to Dashboard
            </a>
        </div>
        
        <h1 class="text-4xl font-orbitron font-bold text-cyber-cyan mb-2">
            ⚙️ Auto-Mining Settings
        </h1>
        <p class="text-xl text-text-secondary">
            Configure global auto-mining system settings and preferences
        </p>
    </div>

    <!-- System Status -->
    <div class="settings-section">
        <h2 class="text-2xl font-orbitron font-bold text-cyber-cyan mb-6">
            🔧 System Status
        </h2>
        
        <div class="setting-item">
            <div class="setting-label">
                <div class="setting-title">Auto-Mining System</div>
                <div class="setting-description">
                    Current status of the auto-mining management system
                </div>
            </div>
            <div class="setting-control">
                {% if mining_status.get('running', False) %}
                <span class="status-indicator status-running">
                    🟢 Running
                </span>
                {% else %}
                <span class="status-indicator status-stopped">
                    ⚪ Stopped
                </span>
                {% endif %}
            </div>
        </div>
        
        <div class="setting-item">
            <div class="setting-label">
                <div class="setting-title">Active Validators</div>
                <div class="setting-description">
                    Number of validators currently configured for auto-mining
                </div>
            </div>
            <div class="setting-control">
                <span class="text-cyber-cyan font-orbitron font-bold">
                    {{ mining_status.get('total_validators', 0) }}
                </span>
            </div>
        </div>
        
        <div class="setting-item">
            <div class="setting-label">
                <div class="setting-title">Active Miners</div>
                <div class="setting-description">
                    Number of validators currently mining
                </div>
            </div>
            <div class="setting-control">
                <span class="text-green-400 font-orbitron font-bold">
                    {{ mining_status.get('active_miners', 0) }}
                </span>
            </div>
        </div>
    </div>

    <!-- System Controls -->
    <div class="settings-section">
        <h2 class="text-2xl font-orbitron font-bold text-cyber-purple mb-6">
            🎮 System Controls
        </h2>
        
        <div class="setting-item">
            <div class="setting-label">
                <div class="setting-title">Start/Stop System</div>
                <div class="setting-description">
                    Control the auto-mining system operation
                </div>
            </div>
            <div class="setting-control">
                {% if mining_status.get('running', False) %}
                <button onclick="stopAutoMining()" class="action-button danger">
                    ⏹️ Stop System
                </button>
                {% else %}
                <button onclick="startAutoMining()" class="action-button success">
                    🚀 Start System
                </button>
                {% endif %}
            </div>
        </div>
        
        <div class="setting-item">
            <div class="setting-label">
                <div class="setting-title">Restart System</div>
                <div class="setting-description">
                    Restart the auto-mining system to apply configuration changes
                </div>
            </div>
            <div class="setting-control">
                <button onclick="restartAutoMining()" class="action-button">
                    🔄 Restart System
                </button>
            </div>
        </div>
        
        <div class="setting-item">
            <div class="setting-label">
                <div class="setting-title">Refresh Status</div>
                <div class="setting-description">
                    Get the latest system status and validator information
                </div>
            </div>
            <div class="setting-control">
                <button onclick="refreshStatus()" class="action-button">
                    🔄 Refresh
                </button>
            </div>
        </div>
    </div>

    <!-- Configuration Information -->
    <div class="settings-section">
        <h2 class="text-2xl font-orbitron font-bold text-cyber-blue mb-6">
            📋 Configuration Information
        </h2>
        
        <div class="setting-item">
            <div class="setting-label">
                <div class="setting-title">Configuration File</div>
                <div class="setting-description">
                    Location of the auto-mining configuration file
                </div>
            </div>
            <div class="setting-control">
                <code class="text-cyber-cyan font-mono text-sm">
                    auto_mining_config.json
                </code>
            </div>
        </div>
        
        <div class="setting-item">
            <div class="setting-label">
                <div class="setting-title">Log File</div>
                <div class="setting-description">
                    Location of the auto-mining system logs
                </div>
            </div>
            <div class="setting-control">
                <code class="text-cyber-cyan font-mono text-sm">
                    auto_mining.log
                </code>
            </div>
        </div>
        
        <div class="setting-item">
            <div class="setting-label">
                <div class="setting-title">Default Mining Interval</div>
                <div class="setting-description">
                    Default time between mining attempts for new validators
                </div>
            </div>
            <div class="setting-control">
                <span class="text-cyber-purple font-orbitron font-bold">
                    10 seconds
                </span>
            </div>
        </div>
        
        <div class="setting-item">
            <div class="setting-label">
                <div class="setting-title">Auto-Restart</div>
                <div class="setting-description">
                    Automatically restart failed mining processes
                </div>
            </div>
            <div class="setting-control">
                <span class="status-indicator status-running">
                    ✅ Enabled
                </span>
            </div>
        </div>
        
        <div class="setting-item">
            <div class="setting-label">
                <div class="setting-title">Max Restart Attempts</div>
                <div class="setting-description">
                    Maximum number of restart attempts before giving up
                </div>
            </div>
            <div class="setting-control">
                <span class="text-orange-400 font-orbitron font-bold">
                    5 attempts
                </span>
            </div>
        </div>
    </div>

    <!-- System Information -->
    <div class="settings-section">
        <h2 class="text-2xl font-orbitron font-bold text-green-400 mb-6">
            ℹ️ System Information
        </h2>
        
        <div class="setting-item">
            <div class="setting-label">
                <div class="setting-title">Auto-Mining Manager Version</div>
                <div class="setting-description">
                    Current version of the auto-mining management system
                </div>
            </div>
            <div class="setting-control">
                <span class="text-cyber-cyan font-orbitron font-bold">
                    v1.0.0
                </span>
            </div>
        </div>
        
        <div class="setting-item">
            <div class="setting-label">
                <div class="setting-title">Python Version</div>
                <div class="setting-description">
                    Python interpreter version used by the system
                </div>
            </div>
            <div class="setting-control">
                <span class="text-cyber-purple font-mono">
                    Python 3.x
                </span>
            </div>
        </div>
        
        <div class="setting-item">
            <div class="setting-label">
                <div class="setting-title">Platform</div>
                <div class="setting-description">
                    Operating system platform
                </div>
            </div>
            <div class="setting-control">
                <span class="text-cyber-blue font-mono">
                    ONNYX Platform
                </span>
            </div>
        </div>
    </div>

    <!-- Advanced Settings -->
    <div class="settings-section">
        <h2 class="text-2xl font-orbitron font-bold text-orange-400 mb-6">
            🔬 Advanced Settings
        </h2>
        
        <div class="setting-item">
            <div class="setting-label">
                <div class="setting-title">View Configuration</div>
                <div class="setting-description">
                    Display the current auto-mining configuration in JSON format
                </div>
            </div>
            <div class="setting-control">
                <button onclick="showConfiguration()" class="action-button">
                    👁️ View Config
                </button>
            </div>
        </div>
        
        <div class="setting-item">
            <div class="setting-label">
                <div class="setting-title">View Logs</div>
                <div class="setting-description">
                    Display recent auto-mining system logs
                </div>
            </div>
            <div class="setting-control">
                <button onclick="showLogs()" class="action-button">
                    📄 View Logs
                </button>
            </div>
        </div>
        
        <div class="setting-item">
            <div class="setting-label">
                <div class="setting-title">Reset Configuration</div>
                <div class="setting-description">
                    Reset all auto-mining settings to default values
                </div>
            </div>
            <div class="setting-control">
                <button onclick="resetConfiguration()" class="action-button danger">
                    🔄 Reset Config
                </button>
            </div>
        </div>
    </div>

    <!-- Configuration Display Area -->
    <div id="config-display" class="settings-section" style="display: none;">
        <h3 class="text-xl font-orbitron font-bold text-cyber-cyan mb-4">
            Configuration Data
        </h3>
        <div class="config-display" id="config-content">
            Loading configuration...
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex space-x-4">
        <a href="{{ url_for('auto_mining.dashboard') }}" 
           class="glass-button-primary px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
            🤖 Back to Dashboard
        </a>
        
        <a href="{{ url_for('auto_mining.performance') }}" 
           class="glass-button px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
            📊 View Performance
        </a>
        
        <a href="{{ url_for('dashboard.overview') }}" 
           class="glass-button px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
            🏠 Main Dashboard
        </a>
    </div>
</div>

<script>
function startAutoMining() {
    if (confirm('Start the auto-mining system?')) {
        fetch('/auto-mining/api/start', { method: 'POST' })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Auto-mining system started successfully');
                    location.reload();
                } else {
                    alert('Failed to start auto-mining: ' + data.error);
                }
            })
            .catch(error => {
                alert('Error starting auto-mining: ' + error);
            });
    }
}

function stopAutoMining() {
    if (confirm('Stop the auto-mining system? This will stop all active miners.')) {
        fetch('/auto-mining/api/stop', { method: 'POST' })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Auto-mining system stopped successfully');
                    location.reload();
                } else {
                    alert('Failed to stop auto-mining: ' + data.error);
                }
            })
            .catch(error => {
                alert('Error stopping auto-mining: ' + error);
            });
    }
}

function restartAutoMining() {
    if (confirm('Restart the auto-mining system? This will briefly interrupt mining operations.')) {
        // Stop first, then start
        fetch('/auto-mining/api/stop', { method: 'POST' })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Wait a moment, then start
                    setTimeout(() => {
                        fetch('/auto-mining/api/start', { method: 'POST' })
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    alert('Auto-mining system restarted successfully');
                                    location.reload();
                                } else {
                                    alert('Failed to restart auto-mining: ' + data.error);
                                }
                            });
                    }, 2000);
                } else {
                    alert('Failed to stop auto-mining for restart: ' + data.error);
                }
            })
            .catch(error => {
                alert('Error restarting auto-mining: ' + error);
            });
    }
}

function refreshStatus() {
    location.reload();
}

function showConfiguration() {
    const configDisplay = document.getElementById('config-display');
    const configContent = document.getElementById('config-content');
    
    if (configDisplay.style.display === 'none') {
        fetch('/auto-mining/api/status')
            .then(response => response.json())
            .then(data => {
                configContent.textContent = JSON.stringify(data, null, 2);
                configDisplay.style.display = 'block';
            })
            .catch(error => {
                configContent.textContent = 'Error loading configuration: ' + error;
                configDisplay.style.display = 'block';
            });
    } else {
        configDisplay.style.display = 'none';
    }
}

function showLogs() {
    alert('Log viewing functionality would be implemented here.\nFor now, check the auto_mining.log file directly.');
}

function resetConfiguration() {
    if (confirm('Reset all auto-mining configuration to defaults? This cannot be undone.')) {
        alert('Configuration reset functionality would be implemented here.\nThis would restore all settings to their default values.');
    }
}
</script>
{% endblock %}
