{% extends "base.html" %}

{% block title %}Biblical Economic Principles - Education{% endblock %}

{% block head %}
<style>
    .principle-section {
        scroll-margin-top: 100px;
    }
    
    .verse-card {
        background: linear-gradient(135deg, rgba(0, 255, 247, 0.1) 0%, rgba(154, 0, 255, 0.1) 100%);
        border-left: 4px solid #00fff7;
        transition: all 0.3s ease;
    }
    
    .verse-card:hover {
        transform: translateX(5px);
        box-shadow: 0 8px 25px rgba(0, 255, 247, 0.2);
    }
    
    .implementation-card {
        background: linear-gradient(135deg, rgba(154, 0, 255, 0.1) 0%, rgba(0, 102, 255, 0.1) 100%);
        border: 1px solid rgba(154, 0, 255, 0.3);
    }
    
    .stats-highlight {
        background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 165, 0, 0.1) 100%);
        border: 1px solid rgba(255, 215, 0, 0.3);
    }
    
    .navigation-sidebar {
        position: sticky;
        top: 100px;
        height: fit-content;
    }
    
    .nav-link {
        transition: all 0.3s ease;
        border-left: 3px solid transparent;
        padding-left: 1rem;
    }
    
    .nav-link:hover, .nav-link.active {
        border-left-color: #00fff7;
        background: rgba(0, 255, 247, 0.1);
    }
    
    .principle-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
        display: block;
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-onyx-black">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="text-center mb-12">
            <h1 class="text-4xl md:text-6xl font-orbitron font-bold mb-6">
                <span class="bg-gradient-to-r from-cyber-cyan via-cyber-purple to-cyber-blue bg-clip-text text-transparent">
                    Biblical Economic Principles
                </span>
            </h1>
            <p class="text-xl text-gray-300 mb-8 max-w-4xl mx-auto">
                Discover how ancient biblical wisdom guides modern blockchain economics, 
                creating a more just and equitable financial system.
            </p>
            
            <!-- Live Stats -->
            <div class="stats-highlight glass-card p-6 max-w-4xl mx-auto">
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                    <div>
                        <p class="text-2xl font-bold text-cyber-cyan">{{ "%.0f"|format(stats.gleaning_pool_balance) }}</p>
                        <p class="text-sm text-gray-400">ONX in Gleaning Pool</p>
                    </div>
                    <div>
                        <p class="text-2xl font-bold text-cyber-purple">{{ stats.active_loans }}</p>
                        <p class="text-sm text-gray-400">Interest-Free Loans</p>
                    </div>
                    <div>
                        <p class="text-2xl font-bold text-yellow-400">{{ stats.sabbath_observers }}</p>
                        <p class="text-sm text-gray-400">Sabbath Observers</p>
                    </div>
                    <div>
                        <p class="text-2xl font-bold text-cyber-blue">{{ stats.total_deeds }}</p>
                        <p class="text-sm text-gray-400">Righteous Deeds</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <!-- Navigation Sidebar -->
            <div class="lg:col-span-1">
                <div class="navigation-sidebar glass-card p-6">
                    <h3 class="text-lg font-semibold mb-4">Biblical Principles</h3>
                    <nav class="space-y-2">
                        <a href="#jubilee" class="nav-link block py-2 text-sm hover:text-cyber-cyan">🔄 Jubilee Reset</a>
                        <a href="#rewards" class="nav-link block py-2 text-sm hover:text-cyber-cyan">⭐ Righteous Rewards</a>
                        <a href="#gleaning" class="nav-link block py-2 text-sm hover:text-cyber-cyan">🌾 Gleaning Pool</a>
                        <a href="#lending" class="nav-link block py-2 text-sm hover:text-cyber-cyan">🤝 Interest-Free Lending</a>
                        <a href="#firstfruits" class="nav-link block py-2 text-sm hover:text-cyber-cyan">🎁 Firstfruits Offerings</a>
                        <a href="#concentration" class="nav-link block py-2 text-sm hover:text-cyber-cyan">⚖️ Anti-Concentration</a>
                        <a href="#classification" class="nav-link block py-2 text-sm hover:text-cyber-cyan">🏷️ Token Classification</a>
                        <a href="#wages" class="nav-link block py-2 text-sm hover:text-cyber-cyan">💰 Fair Wages</a>
                        <a href="#sabbath" class="nav-link block py-2 text-sm hover:text-cyber-cyan">🕊️ Sabbath Rest</a>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="lg:col-span-3 space-y-12">
                <!-- Jubilee Reset -->
                <section id="jubilee" class="principle-section">
                    <div class="glass-card p-8">
                        <span class="principle-icon">🔄</span>
                        <h2 class="text-3xl font-bold mb-6 text-cyber-cyan">Jubilee-Based Circulatory Reset</h2>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h3 class="text-xl font-semibold mb-4">Biblical Foundation</h3>
                                <div class="verse-card p-4 mb-4">
                                    <p class="italic text-gray-300 mb-2">
                                        "And you shall consecrate the fiftieth year, and proclaim liberty throughout the land to all its inhabitants. It shall be a jubilee for you, when each of you shall return to his property and each of you shall return to his clan."
                                    </p>
                                    <p class="text-sm text-cyber-cyan font-semibold">- Leviticus 25:10</p>
                                </div>
                                <p class="text-gray-300">
                                    The Jubilee year represented a complete economic reset, where debts were forgiven, 
                                    slaves were freed, and land was returned to original families. This prevented 
                                    permanent economic inequality and ensured everyone had a fresh start.
                                </p>
                            </div>
                            
                            <div>
                                <h3 class="text-xl font-semibold mb-4">Blockchain Implementation</h3>
                                <div class="implementation-card p-4">
                                    <ul class="space-y-2 text-gray-300">
                                        <li>• <strong>Wealth Redistribution:</strong> Periodic rebalancing of extreme wealth concentration</li>
                                        <li>• <strong>Debt Forgiveness:</strong> Automatic loan forgiveness after partial repayment</li>
                                        <li>• <strong>Fresh Starts:</strong> Dormant account reactivation and balance restoration</li>
                                        <li>• <strong>Community Pools:</strong> Redistribution through gleaning and jubilee pools</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
                
                <!-- Righteous Rewards -->
                <section id="rewards" class="principle-section">
                    <div class="glass-card p-8">
                        <span class="principle-icon">⭐</span>
                        <h2 class="text-3xl font-bold mb-6 text-cyber-purple">Tiered Mining Rewards</h2>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h3 class="text-xl font-semibold mb-4">Biblical Foundation</h3>
                                <div class="verse-card p-4 mb-4">
                                    <p class="italic text-gray-300 mb-2">
                                        "The righteous will flourish like a palm tree, they will grow like a cedar of Lebanon."
                                    </p>
                                    <p class="text-sm text-cyber-cyan font-semibold">- Psalm 92:12</p>
                                </div>
                                <p class="text-gray-300">
                                    Scripture teaches that righteousness leads to blessing and prosperity. 
                                    Those who serve the community and act justly should be rewarded accordingly.
                                </p>
                            </div>
                            
                            <div>
                                <h3 class="text-xl font-semibold mb-4">Blockchain Implementation</h3>
                                <div class="implementation-card p-4">
                                    <ul class="space-y-2 text-gray-300">
                                        <li>• <strong>Deed Scoring:</strong> Track community service and righteous acts</li>
                                        <li>• <strong>Reward Bonuses:</strong> Up to 10% bonus for high deed scores</li>
                                        <li>• <strong>Community Service:</strong> Mutual aid and charity increase rewards</li>
                                        <li>• <strong>Sabbath Observance:</strong> Faithful rest-keeping earns bonuses</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
                
                <!-- Gleaning Pool -->
                <section id="gleaning" class="principle-section">
                    <div class="glass-card p-8">
                        <span class="principle-icon">🌾</span>
                        <h2 class="text-3xl font-bold mb-6 text-cyber-cyan">Gleaning Pool for Community Support</h2>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h3 class="text-xl font-semibold mb-4">Biblical Foundation</h3>
                                <div class="verse-card p-4 mb-4">
                                    <p class="italic text-gray-300 mb-2">
                                        "When you reap the harvest of your land, do not reap to the very edges of your field or gather the gleanings of your harvest. Leave them for the poor and for the foreigner residing among you."
                                    </p>
                                    <p class="text-sm text-cyber-cyan font-semibold">- Leviticus 19:9-10</p>
                                </div>
                                <p class="text-gray-300">
                                    The gleaning laws required landowners to leave portions of their harvest 
                                    for the poor and foreigners, ensuring no one went without basic needs.
                                </p>
                            </div>
                            
                            <div>
                                <h3 class="text-xl font-semibold mb-4">Blockchain Implementation</h3>
                                <div class="implementation-card p-4">
                                    <ul class="space-y-2 text-gray-300">
                                        <li>• <strong>Automatic Allocation:</strong> 2% of mining rewards go to gleaning pool</li>
                                        <li>• <strong>Need-Based Access:</strong> Those below threshold can claim support</li>
                                        <li>• <strong>Community Safety Net:</strong> Ensures basic economic security</li>
                                        <li>• <strong>Transparent Distribution:</strong> All claims recorded on blockchain</li>
                                    </ul>
                                </div>
                                
                                <div class="stats-highlight p-4 mt-4">
                                    <p class="text-center">
                                        <span class="text-2xl font-bold text-cyber-cyan">{{ "%.2f"|format(stats.gleaning_pool_balance) }}</span>
                                        <span class="text-sm text-gray-400 block">ONX Currently Available</span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
                
                <!-- Interest-Free Lending -->
                <section id="lending" class="principle-section">
                    <div class="glass-card p-8">
                        <span class="principle-icon">🤝</span>
                        <h2 class="text-3xl font-bold mb-6 text-cyber-blue">Anti-Usury Lending System</h2>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h3 class="text-xl font-semibold mb-4">Biblical Foundation</h3>
                                <div class="verse-card p-4 mb-4">
                                    <p class="italic text-gray-300 mb-2">
                                        "If you lend money to one of my people among you who is needy, do not treat it like a business deal; charge no interest."
                                    </p>
                                    <p class="text-sm text-cyber-cyan font-semibold">- Exodus 22:25</p>
                                </div>
                                <p class="text-gray-300">
                                    Biblical law prohibited charging interest on loans to fellow believers, 
                                    especially those in need. This prevented exploitation and encouraged mutual aid.
                                </p>
                            </div>
                            
                            <div>
                                <h3 class="text-xl font-semibold mb-4">Blockchain Implementation</h3>
                                <div class="implementation-card p-4">
                                    <ul class="space-y-2 text-gray-300">
                                        <li>• <strong>Zero Interest:</strong> All loans are completely interest-free</li>
                                        <li>• <strong>Automatic Forgiveness:</strong> 80% repayment triggers full forgiveness</li>
                                        <li>• <strong>Grace Periods:</strong> Extended time for repayment without penalty</li>
                                        <li>• <strong>Community Lending:</strong> Peer-to-peer mutual aid system</li>
                                    </ul>
                                </div>
                                
                                <div class="stats-highlight p-4 mt-4">
                                    <p class="text-center">
                                        <span class="text-2xl font-bold text-cyber-blue">{{ stats.active_loans }}</span>
                                        <span class="text-sm text-gray-400 block">Active Interest-Free Loans</span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
                
                <!-- Sabbath Rest -->
                <section id="sabbath" class="principle-section">
                    <div class="glass-card p-8">
                        <span class="principle-icon">🕊️</span>
                        <h2 class="text-3xl font-bold mb-6 text-yellow-400">Sabbath Enforcement and Rest</h2>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h3 class="text-xl font-semibold mb-4">Biblical Foundation</h3>
                                <div class="verse-card p-4 mb-4">
                                    <p class="italic text-gray-300 mb-2">
                                        "Remember the Sabbath day, to keep it holy. Six days you shall labor and do all your work, but the seventh day is a Sabbath to the Lord your God."
                                    </p>
                                    <p class="text-sm text-cyber-cyan font-semibold">- Exodus 20:8-11</p>
                                </div>
                                <p class="text-gray-300">
                                    The Sabbath commandment establishes a rhythm of work and rest, 
                                    recognizing that humans need regular periods of restoration and spiritual focus.
                                </p>
                            </div>
                            
                            <div>
                                <h3 class="text-xl font-semibold mb-4">Blockchain Implementation</h3>
                                <div class="implementation-card p-4">
                                    <ul class="space-y-2 text-gray-300">
                                        <li>• <strong>Mining Pause:</strong> No mining rewards during Sabbath periods</li>
                                        <li>• <strong>Observer Bonuses:</strong> Faithful Sabbath keepers receive reward bonuses</li>
                                        <li>• <strong>Community Rest:</strong> Encourages collective spiritual practice</li>
                                        <li>• <strong>Work-Life Balance:</strong> Prevents exploitation through mandatory rest</li>
                                    </ul>
                                </div>
                                
                                <div class="{% if stats.is_sabbath %}stats-highlight{% else %}implementation-card{% endif %} p-4 mt-4">
                                    <p class="text-center">
                                        {% if stats.is_sabbath %}
                                        <span class="text-2xl">🕊️</span>
                                        <span class="text-lg font-bold text-yellow-400 block">Sabbath Period Active</span>
                                        <span class="text-sm text-gray-400 block">Time for Rest and Reflection</span>
                                        {% else %}
                                        <span class="text-2xl">⏰</span>
                                        <span class="text-lg font-bold text-gray-300 block">Work Period Active</span>
                                        <span class="text-sm text-gray-400 block">Mining and Commerce Enabled</span>
                                        {% endif %}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
                
                <!-- Call to Action -->
                <section class="glass-card p-8 text-center">
                    <h2 class="text-3xl font-bold mb-6">Experience Biblical Economics</h2>
                    <p class="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
                        Join a blockchain platform that values justice, community, and spiritual principles. 
                        See how ancient wisdom can guide modern economic systems.
                    </p>
                    
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        {% if current_user %}
                        <a href="{{ url_for('tokenomics.dashboard') }}" 
                           class="px-8 py-4 bg-gradient-to-r from-cyber-cyan to-cyber-blue text-white font-semibold rounded-xl hover:from-cyber-cyan/90 hover:to-cyber-blue/90 transition-all duration-300 transform hover:scale-105">
                            <span class="mr-2">📊</span>View My Dashboard
                        </a>
                        <a href="{{ url_for('tokenomics.calculator') }}" 
                           class="px-8 py-4 border-2 border-cyber-purple text-cyber-purple font-semibold rounded-xl hover:bg-cyber-purple hover:text-white transition-all duration-300">
                            <span class="mr-2">🧮</span>Calculate Rewards
                        </a>
                        {% else %}
                        <a href="{{ url_for('register_choice') }}" 
                           class="px-8 py-4 bg-gradient-to-r from-cyber-cyan to-cyber-blue text-white font-semibold rounded-xl hover:from-cyber-cyan/90 hover:to-cyber-blue/90 transition-all duration-300 transform hover:scale-105">
                            <span class="mr-2">✨</span>Verify Your Identity
                        </a>
                        <a href="{{ url_for('tokenomics.calculator') }}" 
                           class="px-8 py-4 border-2 border-cyber-purple text-cyber-purple font-semibold rounded-xl hover:bg-cyber-purple hover:text-white transition-all duration-300">
                            <span class="mr-2">🧮</span>Try Calculator
                        </a>
                        {% endif %}
                    </div>
                </section>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Smooth scrolling for navigation links
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
                
                // Update active state
                navLinks.forEach(l => l.classList.remove('active'));
                this.classList.add('active');
            }
        });
    });
    
    // Update active navigation on scroll
    const sections = document.querySelectorAll('.principle-section');
    
    window.addEventListener('scroll', function() {
        let current = '';
        
        sections.forEach(section => {
            const sectionTop = section.offsetTop - 150;
            if (window.pageYOffset >= sectionTop) {
                current = section.getAttribute('id');
            }
        });
        
        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === '#' + current) {
                link.classList.add('active');
            }
        });
    });
});
</script>
{% endblock %}
