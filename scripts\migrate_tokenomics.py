#!/usr/bin/env python3
"""
Biblical Tokenomics Database Migration Script

This script migrates existing Onnyx databases to support biblical tokenomics features.
It adds new tables and fields required for jubilee resets, gleaning pools, deed tracking,
anti-usury lending, and Sabbath enforcement.
"""

import os
import sys
import logging
import time

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from shared.db.db import db

logger = logging.getLogger("onnyx.migration.tokenomics")

def migrate_identities_table():
    """Add tokenomics fields to identities table."""
    try:
        # Check if tokenomics fields already exist
        conn = db.get_connection()
        cursor = conn.cursor()
        cursor.execute("PRAGMA table_info(identities)")
        columns = [column[1] for column in cursor.fetchall()]
        conn.close()

        migrations_needed = []

        if 'last_active_timestamp' not in columns:
            migrations_needed.append("ALTER TABLE identities ADD COLUMN last_active_timestamp INTEGER DEFAULT 0")

        if 'last_transaction_height' not in columns:
            migrations_needed.append("ALTER TABLE identities ADD COLUMN last_transaction_height INTEGER DEFAULT 0")

        if 'deeds_score' not in columns:
            migrations_needed.append("ALTER TABLE identities ADD COLUMN deeds_score REAL DEFAULT 0.0")

        if 'sabbath_observer' not in columns:
            migrations_needed.append("ALTER TABLE identities ADD COLUMN sabbath_observer BOOLEAN DEFAULT 0")

        for migration in migrations_needed:
            db.execute(migration)
            logger.info(f"Applied migration: {migration}")

        if migrations_needed:
            logger.info("Successfully migrated identities table for tokenomics")
        else:
            logger.info("Identities table already has tokenomics fields")

    except Exception as e:
        logger.error(f"Error migrating identities table: {e}")
        raise

def create_tokenomics_tables():
    """Create new tokenomics tables."""

    tables = {
        'jubilee_pools': """
            CREATE TABLE IF NOT EXISTS jubilee_pools (
                pool_id TEXT PRIMARY KEY,
                pool_type TEXT NOT NULL,
                total_amount REAL NOT NULL DEFAULT 0.0,
                token_id TEXT NOT NULL DEFAULT 'ONX',
                created_at INTEGER NOT NULL,
                last_distribution INTEGER DEFAULT 0,
                metadata TEXT NOT NULL DEFAULT '{}'
            )
        """,

        'dormant_accounts': """
            CREATE TABLE IF NOT EXISTS dormant_accounts (
                identity_id TEXT PRIMARY KEY,
                last_activity INTEGER NOT NULL,
                dormant_since INTEGER NOT NULL,
                reclaimed_amount REAL DEFAULT 0.0,
                status TEXT DEFAULT 'DORMANT',
                FOREIGN KEY (identity_id) REFERENCES identities(identity_id)
            )
        """,

        'deeds_ledger': """
            CREATE TABLE IF NOT EXISTS deeds_ledger (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                identity_id TEXT NOT NULL,
                deed_type TEXT NOT NULL,
                deed_value REAL NOT NULL,
                description TEXT,
                timestamp INTEGER NOT NULL,
                block_height INTEGER,
                FOREIGN KEY (identity_id) REFERENCES identities(identity_id)
            )
        """,

        'loans': """
            CREATE TABLE IF NOT EXISTS loans (
                loan_id TEXT PRIMARY KEY,
                lender_id TEXT NOT NULL,
                borrower_id TEXT NOT NULL,
                amount REAL NOT NULL,
                token_id TEXT NOT NULL DEFAULT 'ONX',
                grace_blocks INTEGER NOT NULL,
                forgiveness_threshold REAL DEFAULT 0.8,
                amount_paid REAL DEFAULT 0.0,
                status TEXT DEFAULT 'ACTIVE',
                created_at INTEGER NOT NULL,
                created_block INTEGER NOT NULL,
                FOREIGN KEY (lender_id) REFERENCES identities(identity_id),
                FOREIGN KEY (borrower_id) REFERENCES identities(identity_id)
            )
        """,

        'token_classes': """
            CREATE TABLE IF NOT EXISTS token_classes (
                token_id TEXT PRIMARY KEY,
                class_type TEXT NOT NULL,
                class_metadata TEXT DEFAULT '{}',
                assigned_at INTEGER NOT NULL,
                FOREIGN KEY (token_id) REFERENCES tokens(token_id)
            )
        """,

        'sabbath_periods': """
            CREATE TABLE IF NOT EXISTS sabbath_periods (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                start_timestamp INTEGER NOT NULL,
                end_timestamp INTEGER NOT NULL,
                block_height_start INTEGER,
                block_height_end INTEGER,
                observers_count INTEGER DEFAULT 0
            )
        """
    }

    try:
        for table_name, create_sql in tables.items():
            db.execute(create_sql)
            logger.info(f"Created/verified table: {table_name}")

        logger.info("Successfully created all tokenomics tables")

    except Exception as e:
        logger.error(f"Error creating tokenomics tables: {e}")
        raise

def create_tokenomics_indexes():
    """Create indexes for tokenomics tables."""

    indexes = [
        "CREATE INDEX IF NOT EXISTS idx_dormant_accounts_status ON dormant_accounts(status)",
        "CREATE INDEX IF NOT EXISTS idx_deeds_ledger_identity ON deeds_ledger(identity_id)",
        "CREATE INDEX IF NOT EXISTS idx_deeds_ledger_type ON deeds_ledger(deed_type)",
        "CREATE INDEX IF NOT EXISTS idx_loans_borrower ON loans(borrower_id)",
        "CREATE INDEX IF NOT EXISTS idx_loans_lender ON loans(lender_id)",
        "CREATE INDEX IF NOT EXISTS idx_loans_status ON loans(status)",
        "CREATE INDEX IF NOT EXISTS idx_sabbath_periods_timestamps ON sabbath_periods(start_timestamp, end_timestamp)"
    ]

    try:
        for index_sql in indexes:
            db.execute(index_sql)
            logger.info(f"Created index: {index_sql.split('ON')[1].split('(')[0].strip()}")

        logger.info("Successfully created all tokenomics indexes")

    except Exception as e:
        logger.error(f"Error creating tokenomics indexes: {e}")
        raise

def initialize_gleaning_pool():
    """Initialize the gleaning pool with zero balance."""
    try:
        current_time = int(time.time())

        # Check if gleaning pool already exists
        existing_pool = db.query_one("""
            SELECT pool_id FROM jubilee_pools
            WHERE pool_id = 'GLEANS_POOL' AND pool_type = 'GLEANING'
        """)

        if not existing_pool:
            db.execute("""
                INSERT INTO jubilee_pools (pool_id, pool_type, total_amount, token_id, created_at, last_distribution)
                VALUES ('GLEANS_POOL', 'GLEANING', 0.0, 'ONX', ?, ?)
            """, (current_time, current_time))

            logger.info("Initialized gleaning pool")
        else:
            logger.info("Gleaning pool already exists")

    except Exception as e:
        logger.error(f"Error initializing gleaning pool: {e}")
        raise

def update_chain_parameters():
    """Update chain parameters with tokenomics settings."""
    try:
        # Import chain parameters
        from shared.config.chain_parameters import chain_parameters

        # Add tokenomics parameters if they don't exist
        tokenomics_params = {
            "min_block_reward": 2,
            "max_block_reward": 200,
            "gleaning_pool_percentage": 0.02,
            "jubilee_interval_blocks": 50400,
            "dormancy_threshold_blocks": 7200,
            "dormancy_reclaim_percentage": 0.1,
            "deed_score_multiplier": 0.1,
            "firstfruits_etzem_reward": 2,
            "sabbath_deed_bonus": 0.2,
            "loan_grace_blocks": 14400,
            "loan_forgiveness_threshold": 0.8,
            "concentration_threshold": 1000000,
            "concentration_penalty_rate": 0.1,
            "sabbath_start_day": 5,
            "sabbath_start_hour": 18,
            "sabbath_duration_hours": 25
        }

        current_params = chain_parameters.all()
        new_params = {}

        for key, value in tokenomics_params.items():
            if key not in current_params:
                new_params[key] = value

        if new_params:
            chain_parameters.update(new_params)
            logger.info(f"Added {len(new_params)} new tokenomics parameters")
        else:
            logger.info("All tokenomics parameters already exist")

    except Exception as e:
        logger.error(f"Error updating chain parameters: {e}")
        raise

def run_migration():
    """Run the complete tokenomics migration."""
    try:
        logger.info("Starting biblical tokenomics migration...")

        # Step 1: Migrate identities table
        logger.info("Step 1: Migrating identities table...")
        migrate_identities_table()

        # Step 2: Create new tokenomics tables
        logger.info("Step 2: Creating tokenomics tables...")
        create_tokenomics_tables()

        # Step 3: Create indexes
        logger.info("Step 3: Creating indexes...")
        create_tokenomics_indexes()

        # Step 4: Initialize gleaning pool
        logger.info("Step 4: Initializing gleaning pool...")
        initialize_gleaning_pool()

        # Step 5: Update chain parameters
        logger.info("Step 5: Updating chain parameters...")
        update_chain_parameters()

        logger.info("Biblical tokenomics migration completed successfully!")

    except Exception as e:
        logger.error(f"Migration failed: {e}")
        raise

if __name__ == "__main__":
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    try:
        run_migration()
        print("✅ Biblical tokenomics migration completed successfully!")
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        sys.exit(1)
