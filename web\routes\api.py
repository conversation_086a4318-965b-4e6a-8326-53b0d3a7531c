"""
API Routes

JSON API endpoints for frontend interactions.
"""

import os
import sys
import json
import time
import logging
from flask import Blueprint, request, jsonify, session

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from shared.models.identity import Identity
from shared.db.db import db
from blockchain.wallet.wallet import Wallet
from shared.config.chain_parameters import chain_parameters

logger = logging.getLogger("onnyx.web.api")

api_bp = Blueprint('api', __name__)

@api_bp.route('/stats')
@api_bp.route('/network/stats')
def stats():
    """Get platform statistics."""
    try:
        # Get basic counts with proper error handling
        identities_result = db.query_one("SELECT COUNT(*) as count FROM identities")
        identities_count = identities_result['count'] if isinstance(identities_result, dict) else identities_result[0] if identities_result else 0

        selas_result = db.query_one("SELECT COUNT(*) as count FROM selas")
        selas_count = selas_result['count'] if isinstance(selas_result, dict) else selas_result[0] if selas_result else 0

        active_selas_result = db.query_one("SELECT COUNT(*) as count FROM selas WHERE status = 'active'")
        active_selas_count = active_selas_result['count'] if isinstance(active_selas_result, dict) else active_selas_result[0] if active_selas_result else 0

        transactions_result = db.query_one("SELECT COUNT(*) as count FROM transactions")
        transactions_count = transactions_result['count'] if isinstance(transactions_result, dict) else transactions_result[0] if transactions_result else 0

        # Get blocks count if table exists
        blocks_count = 0
        if db.table_exists('blocks'):
            blocks_result = db.query_one("SELECT COUNT(*) as count FROM blocks")
            blocks_count = blocks_result['count'] if isinstance(blocks_result, dict) else blocks_result[0] if blocks_result else 0

        stats = {
            'identities': identities_count,
            'validators': active_selas_count,  # Frontend expects 'validators'
            'selas': selas_count,
            'active_selas': active_selas_count,
            'transactions': transactions_count,
            'blocks': blocks_count,
            'onx_supply': 22222200,  # Total ONNX supply
            'network_hash_rate': f'{active_selas_count * 3}x'  # Calculated hash rate
        }

        # Get recent activity (last 24 hours)
        try:
            recent_registrations_result = db.query_one("""
                SELECT COUNT(*) as count FROM identities
                WHERE created_at > ?
            """, (int(time.time()) - 86400,))
            recent_registrations = recent_registrations_result['count'] if isinstance(recent_registrations_result, dict) else recent_registrations_result[0] if recent_registrations_result else 0

            recent_selas_result = db.query_one("""
                SELECT COUNT(*) as count FROM selas
                WHERE created_at > ?
            """, (int(time.time()) - 86400,))
            recent_selas = recent_selas_result['count'] if isinstance(recent_selas_result, dict) else recent_selas_result[0] if recent_selas_result else 0

            stats['recent_activity'] = {
                'new_identities_24h': recent_registrations,
                'new_selas_24h': recent_selas
            }
        except Exception as activity_error:
            logger.warning(f"Error getting recent activity: {activity_error}")
            stats['recent_activity'] = {
                'new_identities_24h': 0,
                'new_selas_24h': 0
            }

        return jsonify(stats)

    except Exception as e:
        logger.error(f"Error getting stats: {e}")
        # Return default stats on error
        return jsonify({
            'identities': 0,
            'selas': 0,
            'active_selas': 0,
            'transactions': 0,
            'blocks': 0,
            'recent_activity': {
                'new_identities_24h': 0,
                'new_selas_24h': 0
            }
        }), 200  # Return 200 with default data instead of 500

@api_bp.route('/validators')
def validators():
    """Get all validators (Selas) for frontend display."""
    try:
        validators = db.query("""
            SELECT s.*, i.name as owner_name, i.email as owner_email
            FROM selas s
            JOIN identities i ON s.identity_id = i.identity_id
            WHERE s.status = 'active'
            ORDER BY s.created_at DESC
        """)

        # Format validators for frontend
        formatted_validators = []
        for validator in validators:
            # Parse metadata
            metadata = {}
            try:
                metadata = json.loads(validator.get('metadata', '{}'))
            except:
                pass

            formatted_validator = {
                'sela_id': validator['sela_id'],
                'name': validator['name'],
                'category': validator['category'],
                'status': validator['status'],
                'mining_tier': validator.get('mining_tier', 'basic'),
                'mining_power': validator.get('mining_power', 1),
                'blocks_mined': validator.get('blocks_mined', 0),
                'onx_balance': validator.get('stake_amount', 0),
                'trust_score': 85,  # Default trust score
                'owner_name': validator['owner_name'],
                'owner_email': validator['owner_email'],
                'description': metadata.get('description', 'Professional business validator'),
                'phone': metadata.get('contact', {}).get('phone'),
                'website': metadata.get('contact', {}).get('website'),
                'created_at': validator['created_at']
            }
            formatted_validators.append(formatted_validator)

        return jsonify(formatted_validators)

    except Exception as e:
        logger.error(f"Error getting validators: {e}")
        return jsonify([]), 200  # Return empty array instead of error

@api_bp.route('/blocks/latest')
def latest_blocks():
    """Get latest blocks for frontend display."""
    try:
        blocks = []

        # Check if blocks table exists
        if db.table_exists('blocks'):
            blocks = db.query("""
                SELECT * FROM blocks
                ORDER BY block_number DESC
                LIMIT 10
            """)

        # Format blocks for frontend
        formatted_blocks = []
        for block in blocks:
            formatted_block = {
                'block_number': block.get('block_number', 0),
                'block_hash': block.get('block_hash', 'unknown'),
                'timestamp': block.get('timestamp', int(time.time())),
                'transaction_count': block.get('transaction_count', 1),
                'miner': block.get('miner', 'unknown'),
                'size': block.get('size', 1024)
            }
            formatted_blocks.append(formatted_block)

        # If no blocks in database, return some sample data
        if not formatted_blocks:
            current_time = int(time.time())
            formatted_blocks = [
                {
                    'block_number': 3,
                    'block_hash': 'c81859e77b33cb0e8f4d2a1b5c9e7f3a',
                    'timestamp': current_time - 120,
                    'transaction_count': 1,
                    'miner': 'GetTwisted Hair Studios',
                    'size': 1024
                },
                {
                    'block_number': 2,
                    'block_hash': 'e2094d57b9d04e7f1a3c5b8d9f2e6a4c',
                    'timestamp': current_time - 300,
                    'transaction_count': 3,
                    'miner': 'Gourmet Catering Solutions',
                    'size': 2048
                },
                {
                    'block_number': 1,
                    'block_hash': 'c3c2cc2e50362df9a7b4e1f8c5d3a9b2',
                    'timestamp': current_time - 480,
                    'transaction_count': 1,
                    'miner': 'ONNYX Foundation',
                    'size': 512
                }
            ]

        return jsonify(formatted_blocks)

    except Exception as e:
        logger.error(f"Error getting latest blocks: {e}")
        return jsonify([]), 200  # Return empty array instead of error

@api_bp.route('/identity/<identity_id>')
def get_identity(identity_id):
    """Get identity information."""
    try:
        identity = Identity.get_by_id(identity_id)

        if not identity:
            return jsonify({'error': 'Identity not found'}), 404

        # Get basic identity info (no private data)
        identity_data = {
            'identity_id': identity.identity_id,
            'name': identity.name,
            'status': identity.status,
            'created_at': identity.created_at,
            'metadata': identity.metadata
        }

        # Get associated Selas
        selas = db.query("SELECT * FROM selas WHERE identity_id = ?", (identity_id,))
        identity_data['selas'] = selas

        # Get transaction count
        tx_count_result = db.query_one("SELECT COUNT(*) as count FROM transactions WHERE sender = ?", (identity_id,))
        tx_count = tx_count_result['count'] if isinstance(tx_count_result, dict) else tx_count_result[0] if tx_count_result else 0
        identity_data['transaction_count'] = tx_count

        return jsonify(identity_data)

    except Exception as e:
        logger.error(f"Error getting identity {identity_id}: {e}")
        return jsonify({'error': 'Failed to get identity'}), 500

@api_bp.route('/sela/<sela_id>')
def get_sela(sela_id):
    """Get Sela information."""
    try:
        sela = db.query_one("""
            SELECT s.*, i.name as owner_name
            FROM selas s
            JOIN identities i ON s.identity_id = i.identity_id
            WHERE s.sela_id = ?
        """, (sela_id,))

        if not sela:
            return jsonify({'error': 'Sela not found'}), 404

        # Parse metadata
        try:
            sela['metadata_parsed'] = json.loads(sela['metadata'])
        except:
            sela['metadata_parsed'] = {}

        # Get transaction count
        tx_count_result = db.query_one("""
            SELECT COUNT(*) as count FROM transactions
            WHERE sender = ? OR data LIKE ?
        """, (sela['identity_id'], f'%{sela_id}%'))
        tx_count = tx_count_result['count'] if isinstance(tx_count_result, dict) else tx_count_result[0] if tx_count_result else 0
        sela['transaction_count'] = tx_count

        return jsonify(sela)

    except Exception as e:
        logger.error(f"Error getting Sela {sela_id}: {e}")
        return jsonify({'error': 'Failed to get Sela'}), 500

@api_bp.route('/validate/email', methods=['POST'])
def validate_email():
    """Validate email availability."""
    try:
        data = request.get_json()
        email = data.get('email', '').strip().lower()

        if not email:
            return jsonify({'valid': False, 'message': 'Email is required'})

        # Check if email already exists
        existing = db.query_one("SELECT identity_id FROM identities WHERE email = ?", (email,))

        if existing:
            return jsonify({'valid': False, 'message': 'Email already registered'})

        return jsonify({'valid': True, 'message': 'Email available'})

    except Exception as e:
        logger.error(f"Error validating email: {e}")
        return jsonify({'valid': False, 'message': 'Validation failed'}), 500

@api_bp.route('/validate/sela-name', methods=['POST'])
def validate_sela_name():
    """Validate Sela name availability."""
    try:
        data = request.get_json()
        name = data.get('name', '').strip()

        if not name:
            return jsonify({'valid': False, 'message': 'Business name is required'})

        # Check if name already exists
        existing = db.query_one("SELECT sela_id FROM selas WHERE name = ?", (name,))

        if existing:
            return jsonify({'valid': False, 'message': 'Business name already taken'})

        return jsonify({'valid': True, 'message': 'Business name available'})

    except Exception as e:
        logger.error(f"Error validating Sela name: {e}")
        return jsonify({'valid': False, 'message': 'Validation failed'}), 500

@api_bp.route('/sela/create-dashboard', methods=['POST'])
def create_sela_dashboard():
    """Create Sela from dashboard popup for existing users."""
    try:
        if 'identity_id' not in session:
            return jsonify({'success': False, 'error': 'Authentication required'}), 401

        data = request.get_json()

        # Validate required fields
        sela_name = data.get('sela_name', '').strip()
        category = data.get('category', '').strip()
        description = data.get('description', '').strip()

        if not sela_name or not category or not description:
            return jsonify({
                'success': False,
                'error': 'Business name, category, and description are required'
            }), 400

        # Check if Sela name already exists
        existing = db.query_one("SELECT sela_id FROM selas WHERE name = ?", (sela_name,))
        if existing:
            return jsonify({
                'success': False,
                'error': 'A business with this name already exists'
            }), 400

        # Generate Sela ID
        import hashlib
        import time
        sela_id = hashlib.sha256(f"{sela_name}_{session['identity_id']}_{int(time.time())}".encode()).hexdigest()[:16]

        # Create Sela data
        sela_data = {
            'sela_id': sela_id,
            'identity_id': session['identity_id'],
            'name': sela_name,
            'category': category,
            'stake_amount': 1000,  # Default stake amount
            'stake_token_id': 'ONX',  # Default to ONX token
            'status': 'active',
            'created_at': int(time.time()),
            'metadata': json.dumps({
                "description": description,
                "address": data.get('address', ''),
                "services": data.get('services', []),
                "registration_type": "dashboard",
                "verified": True
            })
        }

        # Insert into database
        db.insert('selas', sela_data)

        # Initialize Sela in biblical tokenomics
        from blockchain.tokenomics.biblical_tokenomics import BiblicalTokenomics
        bt = BiblicalTokenomics(db)
        bt.register_sela_validator(sela_id, sela_name)

        logger.info(f"Sela created from dashboard: {sela_id}")

        return jsonify({
            'success': True,
            'message': f'Business "{sela_name}" registered successfully!',
            'sela_id': sela_id
        })

    except Exception as e:
        logger.error(f"Error creating Sela from dashboard: {e}")
        return jsonify({'success': False, 'error': 'Sela creation failed. Please try again.'}), 500

def _perform_mining(identity_id: str, sela: dict) -> dict:
    """
    Perform actual mining operation.

    Args:
        identity_id: User's identity ID
        sela: Sela validator information

    Returns:
        dict: Mining result with success status, block info, and rewards
    """
    try:
        import hashlib
        import random

        # Get current blockchain state
        latest_block = db.query_one("SELECT MAX(`index`) as height FROM blocks") or {'height': 0}
        block_height = (latest_block['height'] or 0) + 1

        # Calculate mining difficulty (simplified)
        difficulty = 1
        mining_power = sela.get('mining_power', 1)

        # Simulate proof-of-work (in production, this would be real PoW)
        nonce = random.randint(1, 1000000)
        block_data = f"{block_height}_{identity_id}_{sela['sela_id']}_{int(time.time())}_{nonce}"
        block_hash = hashlib.sha256(block_data.encode()).hexdigest()

        # Calculate reward based on mining power and biblical tokenomics
        base_reward = 10  # Base ONX reward
        reward = base_reward * mining_power

        # Create block record (using existing blocks table structure)
        block_record = {
            'index': block_height,
            'timestamp': int(time.time()),
            'proposer': identity_id,
            'hash': block_hash,
            'previous_hash': _get_previous_block_hash(),
            'nonce': nonce,
            'signature': f"mining_reward_{reward}",
            'signed_by': sela['sela_id']
        }

        # Insert block into database
        db.insert('blocks', block_record)

        # Update Sela mining stats
        db.execute("""
            UPDATE selas SET
                blocks_mined = COALESCE(blocks_mined, 0) + 1,
                mining_rewards_earned = COALESCE(mining_rewards_earned, 0) + ?,
                last_updated = ?
            WHERE sela_id = ?
        """, (reward, int(time.time()), sela['sela_id']))

        # Update identity mining stats (using existing columns)
        db.execute("""
            UPDATE identities SET
                updated_at = ?
            WHERE identity_id = ?
        """, (int(time.time()), identity_id))

        logger.info(f"Block {block_height} mined by {identity_id} (Sela: {sela['sela_id']}) - Reward: {reward} ONX")

        return {
            'success': True,
            'block_hash': block_hash,
            'block_height': block_height,
            'reward': reward,
            'mining_power': mining_power
        }

    except Exception as e:
        logger.error(f"Mining operation failed: {e}")
        return {
            'success': False,
            'error': str(e)
        }

def _get_previous_block_hash() -> str:
    """Get the hash of the previous block."""
    try:
        latest_block = db.query_one("""
            SELECT hash FROM blocks
            ORDER BY `index` DESC
            LIMIT 1
        """)
        return latest_block['hash'] if latest_block else '0' * 64
    except:
        return '0' * 64

@api_bp.route('/generate-keys', methods=['POST'])
def generate_keys():
    """Generate new cryptographic keys."""
    try:
        wallet = Wallet()
        private_key, public_key = wallet.generate_keypair()

        return jsonify({
            'private_key': private_key,
            'public_key': public_key,
            'warning': 'Keep your private key secure! It cannot be recovered if lost.'
        })

    except Exception as e:
        logger.error(f"Error generating keys: {e}")
        return jsonify({'error': 'Failed to generate keys'}), 500

@api_bp.route('/mine-block', methods=['POST'])
def mine_block():
    """Trigger block mining (for authenticated users with configured Selas)."""
    try:
        if 'identity_id' not in session:
            return jsonify({'error': 'Authentication required'}), 401

        identity_id = session['identity_id']

        # Check if user has any configured Selas
        user_selas = db.query("SELECT * FROM selas WHERE identity_id = ? AND status = 'active'", (identity_id,))

        if not user_selas:
            return jsonify({'error': 'No active Selas found for mining'}), 400

        # Get user's timezone preference
        user_timezone = session.get('user_timezone', 'America/Chicago')  # Default to CST

        # Check biblical compliance
        from shared.models.tokenomics import BiblicalTokenomics
        bt = BiblicalTokenomics()

        if bt.is_sabbath_period(user_timezone):
            return jsonify({
                'success': False,
                'error': 'Mining is not allowed during Sabbath period',
                'sabbath_active': True
            }), 400

        # Perform actual mining
        result = _perform_mining(identity_id, user_selas[0])

        if result['success']:
            return jsonify({
                'success': True,
                'message': 'Block mined successfully!',
                'block_hash': result['block_hash'],
                'block_height': result['block_height'],
                'reward': result['reward'],
                'mining_power': result['mining_power']
            })
        else:
            return jsonify({
                'success': False,
                'error': result['error']
            }), 400

    except Exception as e:
        logger.error(f"Error mining block: {e}")
        return jsonify({'error': 'Mining failed'}), 500

@api_bp.route('/mining/toggle', methods=['POST'])
def toggle_mining():
    """Toggle mining on/off for user's Selas."""
    try:
        if 'identity_id' not in session:
            return jsonify({'error': 'Authentication required'}), 401

        data = request.get_json()
        action = data.get('action')  # 'start' or 'stop'
        intensity = data.get('intensity', 'medium')

        identity_id = session['identity_id']

        # Get user's active Selas
        user_selas = db.query("SELECT * FROM selas WHERE identity_id = ? AND status = 'active'", (identity_id,))

        if not user_selas:
            return jsonify({'error': 'No active Selas found'}), 400

        # Update mining status for all user's Selas
        mining_active = 1 if action == 'start' else 0

        for sela in user_selas:
            db.execute("""
                UPDATE selas SET
                    mining_active = ?,
                    mining_intensity = ?,
                    last_updated = ?
                WHERE sela_id = ?
            """, (mining_active, intensity, int(time.time()), sela['sela_id']))

        return jsonify({
            'success': True,
            'action': action,
            'intensity': intensity,
            'affected_selas': len(user_selas)
        })

    except Exception as e:
        logger.error(f"Error toggling mining: {e}")
        return jsonify({'error': 'Failed to toggle mining'}), 500

@api_bp.route('/mining/intensity', methods=['POST'])
def update_mining_intensity():
    """Update mining intensity for user's Selas."""
    try:
        if 'identity_id' not in session:
            return jsonify({'error': 'Authentication required'}), 401

        data = request.get_json()
        intensity = data.get('intensity', 'medium')

        # Validate intensity
        valid_intensities = ['low', 'medium', 'high', 'maximum']
        if intensity not in valid_intensities:
            return jsonify({'error': f'Invalid intensity. Must be one of: {valid_intensities}'}), 400

        identity_id = session['identity_id']

        # Update intensity for all user's active Selas
        db.execute("""
            UPDATE selas SET
                mining_intensity = ?,
                last_updated = ?
            WHERE identity_id = ? AND status = 'active'
        """, (intensity, int(time.time()), identity_id))

        return jsonify({
            'success': True,
            'intensity': intensity
        })

    except Exception as e:
        logger.error(f"Error updating mining intensity: {e}")
        return jsonify({'error': 'Failed to update mining intensity'}), 500

@api_bp.route('/mining/status')
def mining_status():
    """Get current mining status for the user."""
    try:
        if 'identity_id' not in session:
            return jsonify({'error': 'Authentication required'}), 401

        identity_id = session['identity_id']
        user_timezone = session.get('user_timezone', 'America/Chicago')

        # Get user's Selas and mining status
        user_selas = db.query("""
            SELECT * FROM selas
            WHERE identity_id = ? AND status = 'active'
        """, (identity_id,))

        # Check biblical compliance
        from shared.models.tokenomics import BiblicalTokenomics
        bt = BiblicalTokenomics()

        is_sabbath = bt.is_sabbath_period(user_timezone)
        can_mine_sabbath = not is_sabbath

        # Calculate mining stats
        active_miners = len([s for s in user_selas if s.get('mining_active', 0) == 1])
        total_mining_power = sum(s.get('mining_power', 1) for s in user_selas if s.get('mining_active', 0) == 1)

        return jsonify({
            'success': True,
            'is_mining': active_miners > 0,
            'active_miners': active_miners,
            'total_selas': len(user_selas),
            'total_mining_power': total_mining_power,
            'can_mine_sabbath': can_mine_sabbath,
            'is_sabbath_period': is_sabbath,
            'user_timezone': user_timezone,
            'mining_intensity': user_selas[0].get('mining_intensity', 'medium') if user_selas else 'medium'
        })

    except Exception as e:
        logger.error(f"Error getting mining status: {e}")
        return jsonify({'error': 'Failed to get mining status'}), 500

@api_bp.route('/user/timezone', methods=['POST'])
def update_user_timezone():
    """Update user's timezone preference."""
    try:
        if 'identity_id' not in session:
            return jsonify({'error': 'Authentication required'}), 401

        data = request.get_json()
        timezone_str = data.get('timezone')

        if not timezone_str:
            return jsonify({'error': 'Timezone is required'}), 400

        # Validate timezone
        try:
            import pytz
            pytz.timezone(timezone_str)
        except:
            return jsonify({'error': 'Invalid timezone'}), 400

        # Update session
        session['user_timezone'] = timezone_str

        # Update database
        identity_id = session['identity_id']
        db.execute("""
            UPDATE identities SET
                timezone = ?,
                updated_at = ?
            WHERE identity_id = ?
        """, (timezone_str, int(time.time()), identity_id))

        return jsonify({
            'success': True,
            'timezone': timezone_str
        })

    except Exception as e:
        logger.error(f"Error updating timezone: {e}")
        return jsonify({'error': 'Failed to update timezone'}), 500

@api_bp.route('/search', methods=['GET'])
def search():
    """Universal search API."""
    try:
        query = request.args.get('q', '').strip()
        limit = request.args.get('limit', 10, type=int)

        if not query:
            return jsonify([])

        results = []

        # Search identities
        identities = db.query("""
            SELECT identity_id, name, 'identity' as type
            FROM identities
            WHERE name LIKE ?
            LIMIT ?
        """, (f"%{query}%", limit // 3))

        for identity in identities:
            results.append({
                'id': identity['identity_id'],
                'title': identity['name'],
                'type': 'Identity',
                'url': f'/dashboard/identity'  # Would need identity-specific URLs
            })

        # Search Selas
        selas = db.query("""
            SELECT sela_id, name, category, 'sela' as type
            FROM selas
            WHERE name LIKE ? OR category LIKE ?
            LIMIT ?
        """, (f"%{query}%", f"%{query}%", limit // 3))

        for sela in selas:
            results.append({
                'id': sela['sela_id'],
                'title': sela['name'],
                'subtitle': sela['category'],
                'type': 'Business',
                'url': f'/sela/{sela["sela_id"]}'
            })

        # Search transactions
        transactions = db.query("""
            SELECT tx_id, op, 'transaction' as type
            FROM transactions
            WHERE tx_id LIKE ? OR op LIKE ?
            LIMIT ?
        """, (f"%{query}%", f"%{query}%", limit // 3))

        for tx in transactions:
            results.append({
                'id': tx['tx_id'],
                'title': f"{tx['op']} Transaction",
                'subtitle': tx['tx_id'][:20] + '...',
                'type': 'Transaction',
                'url': f'/explorer/transaction/{tx["tx_id"]}'
            })

        return jsonify(results[:limit])

    except Exception as e:
        logger.error(f"Error in search API: {e}")
        return jsonify([])

@api_bp.route('/user/dashboard-data')
def user_dashboard_data():
    """Get dashboard data for authenticated user."""
    try:
        if 'identity_id' not in session:
            return jsonify({'error': 'Authentication required'}), 401

        identity_id = session['identity_id']

        # Get user's Selas
        selas = db.query("SELECT * FROM selas WHERE identity_id = ?", (identity_id,))

        # Get recent transactions
        transactions = db.query("""
            SELECT * FROM transactions
            WHERE sender = ?
            ORDER BY created_at DESC
            LIMIT 5
        """, (identity_id,))

        # Get token balances if available
        token_balances = []
        if db.table_exists('token_balances'):
            token_balances = db.query("""
                SELECT tb.*, t.name, t.symbol
                FROM token_balances tb
                JOIN tokens t ON tb.token_id = t.token_id
                WHERE tb.identity_id = ?
            """, (identity_id,))

        return jsonify({
            'selas': selas,
            'recent_transactions': transactions,
            'token_balances': token_balances,
            'stats': {
                'total_selas': len(selas),
                'total_transactions': len(transactions),
                'total_tokens': sum(tb.get('balance', 0) for tb in token_balances)
            }
        })

    except Exception as e:
        logger.error(f"Error getting dashboard data: {e}")
        return jsonify({'error': 'Failed to get dashboard data'}), 500

# Biblical Tokenomics API Endpoints

@api_bp.route('/tokenomics/gleaning-pool')
def gleaning_pool_status():
    """Get gleaning pool status and balance."""
    try:
        # Get gleaning pool balance
        pool_result = db.query_one("""
            SELECT * FROM jubilee_pools
            WHERE pool_id = 'GLEANS_POOL' AND pool_type = 'GLEANING'
        """)

        pool_balance = pool_result['total_amount'] if pool_result else 0.0

        # Get recent allocations (last 10)
        recent_allocations = db.query("""
            SELECT * FROM deeds_ledger
            WHERE deed_type = 'GLEANING_ALLOCATION'
            ORDER BY timestamp DESC
            LIMIT 10
        """)

        # Get recent claims (last 10)
        recent_claims = db.query("""
            SELECT * FROM deeds_ledger
            WHERE deed_type = 'GLEANING_CLAIM'
            ORDER BY timestamp DESC
            LIMIT 10
        """)

        return jsonify({
            'pool_balance': pool_balance,
            'token_id': 'ONX',
            'recent_allocations': recent_allocations,
            'recent_claims': recent_claims,
            'last_updated': int(time.time())
        })

    except Exception as e:
        logger.error(f"Error getting gleaning pool status: {e}")
        return jsonify({'error': 'Failed to get gleaning pool status'}), 500

@api_bp.route('/tokenomics/gleaning-pool/claim', methods=['POST'])
def claim_from_gleaning_pool():
    """Claim tokens from the gleaning pool."""
    try:
        if 'identity_id' not in session:
            return jsonify({'error': 'Authentication required'}), 401

        data = request.get_json()
        identity_id = session['identity_id']
        amount = data.get('amount', 0)
        justification = data.get('justification', '')

        if amount <= 0:
            return jsonify({'error': 'Amount must be positive'}), 400

        if not justification:
            return jsonify({'error': 'Justification is required'}), 400

        # Import tokenomics module
        from shared.models.tokenomics import biblical_tokenomics

        # Check pool balance
        pool_result = db.query_one("""
            SELECT total_amount FROM jubilee_pools
            WHERE pool_id = 'GLEANS_POOL' AND pool_type = 'GLEANING'
        """)

        pool_balance = pool_result['total_amount'] if pool_result else 0.0

        if amount > pool_balance:
            return jsonify({'error': f'Insufficient pool balance. Available: {pool_balance}'}), 400

        # Record the claim
        current_time = int(time.time())

        # Deduct from pool
        db.execute("""
            UPDATE jubilee_pools
            SET total_amount = total_amount - ?, last_distribution = ?
            WHERE pool_id = 'GLEANS_POOL' AND pool_type = 'GLEANING'
        """, (amount, current_time))

        # Credit to claimant
        db.execute("""
            INSERT OR REPLACE INTO token_balances (identity_id, token_id, balance, updated_at)
            VALUES (?, 'ONX', COALESCE((SELECT balance FROM token_balances WHERE identity_id = ? AND token_id = 'ONX'), 0) + ?, ?)
        """, (identity_id, identity_id, amount, current_time))

        # Record the deed
        biblical_tokenomics.record_deed(
            identity_id,
            'GLEANING_CLAIM',
            amount,
            f"Claimed from gleaning pool: {justification}"
        )

        return jsonify({
            'success': True,
            'message': f'Successfully claimed {amount} ONX from gleaning pool',
            'amount': amount,
            'new_balance': pool_balance - amount
        })

    except Exception as e:
        logger.error(f"Error claiming from gleaning pool: {e}")
        return jsonify({'error': 'Failed to process claim'}), 500

@api_bp.route('/tokenomics/deeds/<identity_id>')
def get_deeds_history(identity_id):
    """Get deed history for an identity."""
    try:
        deeds = db.query("""
            SELECT * FROM deeds_ledger
            WHERE identity_id = ?
            ORDER BY timestamp DESC
            LIMIT 50
        """, (identity_id,))

        # Get current deed score
        identity_result = db.query_one("""
            SELECT deeds_score FROM identities WHERE identity_id = ?
        """, (identity_id,))

        deed_score = identity_result['deeds_score'] if identity_result else 0.0

        return jsonify({
            'deeds': deeds,
            'current_deed_score': deed_score,
            'total_deeds': len(deeds)
        })

    except Exception as e:
        logger.error(f"Error getting deeds history: {e}")
        return jsonify({'error': 'Failed to get deeds history'}), 500

@api_bp.route('/tokenomics/firstfruits', methods=['POST'])
def make_firstfruits_offering():
    """Make a firstfruits offering."""
    try:
        if 'identity_id' not in session:
            return jsonify({'error': 'Authentication required'}), 401

        data = request.get_json()
        identity_id = session['identity_id']
        amount = data.get('amount', 0)
        token_id = data.get('token_id', 'ONX')

        if amount <= 0:
            return jsonify({'error': 'Amount must be positive'}), 400

        # Check balance
        balance_result = db.query_one("""
            SELECT balance FROM token_balances
            WHERE identity_id = ? AND token_id = ?
        """, (identity_id, token_id))

        current_balance = balance_result['balance'] if balance_result else 0

        if amount > current_balance:
            return jsonify({'error': f'Insufficient balance. Available: {current_balance}'}), 400

        # Import tokenomics module
        from shared.models.tokenomics import biblical_tokenomics

        current_time = int(time.time())

        # Deduct from offerer
        db.execute("""
            UPDATE token_balances
            SET balance = balance - ?, updated_at = ?
            WHERE identity_id = ? AND token_id = ?
        """, (amount, current_time, identity_id, token_id))

        # Add to firstfruits pool
        db.execute("""
            INSERT OR REPLACE INTO jubilee_pools (pool_id, pool_type, total_amount, token_id, created_at, last_distribution)
            VALUES ('FIRSTFRUITS_POOL', 'FIRSTFRUITS', COALESCE((SELECT total_amount FROM jubilee_pools WHERE pool_id = 'FIRSTFRUITS_POOL'), 0) + ?, ?, ?, ?)
        """, (amount, token_id, current_time, current_time))

        # Record the deed and award Etzem tokens
        biblical_tokenomics.record_deed(
            identity_id,
            'FIRSTFRUITS',
            amount,
            f"Firstfruits offering of {amount} {token_id}"
        )

        # Award Etzem tokens
        etzem_reward = chain_parameters.get("firstfruits_etzem_reward", 2)
        db.execute("""
            INSERT OR REPLACE INTO token_balances (identity_id, token_id, balance, updated_at)
            VALUES (?, 'ETZEM', COALESCE((SELECT balance FROM token_balances WHERE identity_id = ? AND token_id = 'ETZEM'), 0) + ?, ?)
        """, (identity_id, identity_id, etzem_reward, current_time))

        return jsonify({
            'success': True,
            'message': f'Firstfruits offering of {amount} {token_id} accepted',
            'etzem_reward': etzem_reward,
            'deed_value': amount
        })

    except Exception as e:
        logger.error(f"Error processing firstfruits offering: {e}")
        return jsonify({'error': 'Failed to process offering'}), 500

@api_bp.route('/tokenomics/sabbath/status')
def sabbath_status():
    """Get current Sabbath status."""
    try:
        from shared.models.tokenomics import biblical_tokenomics

        is_sabbath = biblical_tokenomics.is_sabbath_period()

        # Get Sabbath configuration
        sabbath_config = {
            'start_day': chain_parameters.get("sabbath_start_day", 5),
            'start_hour': chain_parameters.get("sabbath_start_hour", 18),
            'duration_hours': chain_parameters.get("sabbath_duration_hours", 25)
        }

        return jsonify({
            'is_sabbath': is_sabbath,
            'config': sabbath_config,
            'message': 'Mining is restricted during Sabbath period' if is_sabbath else 'Mining is allowed'
        })

    except Exception as e:
        logger.error(f"Error getting Sabbath status: {e}")
        return jsonify({'error': 'Failed to get Sabbath status'}), 500

@api_bp.route('/tokenomics/mining/status')
def enhanced_mining_status():
    """Get enhanced mining status with 24/6 schedule enforcement."""
    try:
        from shared.models.tokenomics import biblical_tokenomics

        mining_allowed = biblical_tokenomics.is_mining_allowed()
        is_sabbath = biblical_tokenomics.is_sabbath_period()
        is_new_moon = biblical_tokenomics.is_new_moon_period()
        is_feast_day = biblical_tokenomics.is_feast_day()

        # Get current year's emission statistics
        from datetime import datetime, timezone
        current_year = datetime.now(timezone.utc).year
        year_start = datetime(current_year, 1, 1, tzinfo=timezone.utc)

        year_emissions = db.query_one("""
            SELECT COALESCE(SUM(reward_amount), 0) as total
            FROM mining_rewards
            WHERE created_at > ?
        """, (int(year_start.timestamp()),))

        total_emitted = year_emissions['total'] if year_emissions else 0.0
        remaining_cap = max(0, biblical_tokenomics.ANNUAL_MINING_CAP - total_emitted)

        return jsonify({
            'mining_allowed': mining_allowed,
            'restrictions': {
                'is_sabbath': is_sabbath,
                'is_new_moon': is_new_moon,
                'is_feast_day': is_feast_day
            },
            'annual_emissions': {
                'cap': biblical_tokenomics.ANNUAL_MINING_CAP,
                'emitted': total_emitted,
                'remaining': remaining_cap,
                'percentage_used': (total_emitted / biblical_tokenomics.ANNUAL_MINING_CAP) * 100
            },
            'daily_target': biblical_tokenomics.DAILY_EMISSION,
            'hourly_target': biblical_tokenomics.HOURLY_EMISSION,
            'active_days_per_year': biblical_tokenomics.ACTIVE_MINING_DAYS
        })

    except Exception as e:
        logger.error(f"Error getting enhanced mining status: {e}")
        return jsonify({'error': 'Failed to get mining status'}), 500

@api_bp.route('/tokenomics/mining/tier/<identity_id>')
def get_mining_tier(identity_id):
    """Get mining tier and multiplier for an identity."""
    try:
        from shared.models.tokenomics import biblical_tokenomics

        tier_multiplier = biblical_tokenomics._get_tier_multiplier(identity_id)
        righteous_bonus = biblical_tokenomics._check_righteous_bonus(identity_id)
        penalty_multiplier = biblical_tokenomics._check_penalty_status(identity_id)

        # Determine tier name
        tier_names = {
            1.0: 'Citizen',
            2.5: 'Basic Sela Owner',
            5.5: 'Dual Sela Owner',
            7.0: 'Triple Sela Owner',
            9.0: 'ONNYX Pro Validator',
            0.5: 'Penalty Tier'
        }

        tier_name = tier_names.get(tier_multiplier, 'Unknown')
        if penalty_multiplier < 1.0:
            tier_name = 'Penalty Tier'

        return jsonify({
            'identity_id': identity_id,
            'tier_name': tier_name,
            'tier_multiplier': tier_multiplier,
            'righteous_bonus_eligible': righteous_bonus,
            'penalty_multiplier': penalty_multiplier,
            'effective_multiplier': tier_multiplier * penalty_multiplier * (1.05 if righteous_bonus else 1.0)
        })

    except Exception as e:
        logger.error(f"Error getting mining tier: {e}")
        return jsonify({'error': 'Failed to get mining tier'}), 500

# Additional Biblical Tokenomics API Endpoints

@api_bp.route('/tokenomics/loans', methods=['POST'])
def create_loan():
    """Create a new interest-free loan."""
    try:
        if 'identity_id' not in session:
            return jsonify({'error': 'Authentication required'}), 401

        data = request.get_json()
        lender_id = session['identity_id']
        borrower_id = data.get('borrower_id')
        amount = data.get('amount', 0)
        token_id = data.get('token_id', 'ONX')
        grace_blocks = data.get('grace_blocks')

        if not borrower_id:
            return jsonify({'error': 'Borrower ID is required'}), 400

        if amount <= 0:
            return jsonify({'error': 'Amount must be positive'}), 400

        # Check lender balance
        balance_result = db.query_one("""
            SELECT balance FROM token_balances
            WHERE identity_id = ? AND token_id = ?
        """, (lender_id, token_id))

        current_balance = balance_result['balance'] if balance_result else 0

        if amount > current_balance:
            return jsonify({'error': f'Insufficient balance. Available: {current_balance}'}), 400

        # Import tokenomics module
        from shared.models.tokenomics import biblical_tokenomics

        # Create the loan
        loan_id = biblical_tokenomics.create_loan(lender_id, borrower_id, amount, token_id, grace_blocks)

        if loan_id:
            return jsonify({
                'success': True,
                'loan_id': loan_id,
                'message': f'Loan of {amount} {token_id} created successfully'
            })
        else:
            return jsonify({'error': 'Failed to create loan'}), 500

    except Exception as e:
        logger.error(f"Error creating loan: {e}")
        return jsonify({'error': 'Failed to create loan'}), 500

@api_bp.route('/tokenomics/loans/<loan_id>/repay', methods=['POST'])
def repay_loan(loan_id):
    """Make a loan repayment."""
    try:
        if 'identity_id' not in session:
            return jsonify({'error': 'Authentication required'}), 401

        data = request.get_json()
        borrower_id = session['identity_id']
        amount = data.get('amount', 0)

        if amount <= 0:
            return jsonify({'error': 'Amount must be positive'}), 400

        # Import tokenomics module
        from shared.models.tokenomics import biblical_tokenomics

        # Process repayment
        success = biblical_tokenomics.repay_loan(loan_id, borrower_id, amount)

        if success:
            return jsonify({
                'success': True,
                'message': f'Repayment of {amount} processed successfully'
            })
        else:
            return jsonify({'error': 'Failed to process repayment'}), 400

    except Exception as e:
        logger.error(f"Error processing loan repayment: {e}")
        return jsonify({'error': 'Failed to process repayment'}), 500

@api_bp.route('/tokenomics/loans/<loan_id>/forgive', methods=['POST'])
def forgive_loan(loan_id):
    """Forgive a loan."""
    try:
        if 'identity_id' not in session:
            return jsonify({'error': 'Authentication required'}), 401

        forgiver_id = session['identity_id']

        # Verify the forgiver is the lender
        loan = db.query_one("SELECT * FROM loans WHERE loan_id = ?", (loan_id,))
        if not loan:
            return jsonify({'error': 'Loan not found'}), 404

        if loan['lender_id'] != forgiver_id:
            return jsonify({'error': 'Only the lender can forgive this loan'}), 403

        # Import tokenomics module
        from shared.models.tokenomics import biblical_tokenomics

        # Forgive the loan
        success = biblical_tokenomics.forgive_loan(loan_id, forgiver_id)

        if success:
            return jsonify({
                'success': True,
                'message': 'Loan forgiven successfully'
            })
        else:
            return jsonify({'error': 'Failed to forgive loan'}), 500

    except Exception as e:
        logger.error(f"Error forgiving loan: {e}")
        return jsonify({'error': 'Failed to forgive loan'}), 500

@api_bp.route('/tokenomics/loans/<identity_id>')
def get_loans(identity_id):
    """Get loans for an identity (as lender or borrower)."""
    try:
        # Get loans where identity is lender or borrower
        loans = db.query("""
            SELECT l.*,
                   lender.name as lender_name,
                   borrower.name as borrower_name
            FROM loans l
            JOIN identities lender ON l.lender_id = lender.identity_id
            JOIN identities borrower ON l.borrower_id = borrower.identity_id
            WHERE l.lender_id = ? OR l.borrower_id = ?
            ORDER BY l.created_at DESC
        """, (identity_id, identity_id))

        return jsonify({
            'loans': loans,
            'total_loans': len(loans)
        })

    except Exception as e:
        logger.error(f"Error getting loans: {e}")
        return jsonify({'error': 'Failed to get loans'}), 500

@api_bp.route('/tokenomics/tokens/<token_id>/classify', methods=['POST'])
def classify_token(token_id):
    """Classify a token with biblical class."""
    try:
        if 'identity_id' not in session:
            return jsonify({'error': 'Authentication required'}), 401

        data = request.get_json()
        class_type = data.get('class_type')
        purpose = data.get('purpose')

        # Import tokenomics module
        from shared.models.tokenomics import biblical_tokenomics

        success = False

        if class_type:
            # Direct classification
            success = biblical_tokenomics.assign_token_class(token_id, class_type)
        elif purpose:
            # Classification by purpose
            success = biblical_tokenomics.classify_token_by_purpose(token_id, purpose)
        else:
            return jsonify({'error': 'Either class_type or purpose is required'}), 400

        if success:
            return jsonify({
                'success': True,
                'message': f'Token {token_id} classified successfully'
            })
        else:
            return jsonify({'error': 'Failed to classify token'}), 500

    except Exception as e:
        logger.error(f"Error classifying token: {e}")
        return jsonify({'error': 'Failed to classify token'}), 500

@api_bp.route('/tokenomics/tokens/<token_id>/class')
def get_token_class(token_id):
    """Get the biblical class of a token."""
    try:
        from shared.models.tokenomics import biblical_tokenomics

        class_type = biblical_tokenomics.get_token_class(token_id)

        return jsonify({
            'token_id': token_id,
            'class_type': class_type,
            'has_class': bool(class_type)
        })

    except Exception as e:
        logger.error(f"Error getting token class: {e}")
        return jsonify({'error': 'Failed to get token class'}), 500

@api_bp.route('/tokenomics/concentration/<identity_id>')
def check_concentration(identity_id):
    """Check concentration status for an identity."""
    try:
        from shared.models.tokenomics import biblical_tokenomics

        total_balance = biblical_tokenomics._get_total_balance(identity_id)
        concentration_threshold = chain_parameters.get("concentration_threshold", 1000000)

        is_over_threshold = total_balance > concentration_threshold
        excess_amount = max(0, total_balance - concentration_threshold)

        return jsonify({
            'identity_id': identity_id,
            'total_balance': total_balance,
            'concentration_threshold': concentration_threshold,
            'is_over_threshold': is_over_threshold,
            'excess_amount': excess_amount,
            'suggested_donation': excess_amount * 0.1 if excess_amount > 0 else 0
        })

    except Exception as e:
        logger.error(f"Error checking concentration: {e}")
        return jsonify({'error': 'Failed to check concentration'}), 500
