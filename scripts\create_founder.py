#!/usr/bin/env python3
"""
ONNYX Genesis Founder Creation Script
Creates the founder identity with maximum privileges for mainnet launch.
"""

import sys
import os
import uuid
from datetime import datetime, timezone

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from shared.db.db import db
from shared.models.tokenomics import biblical_tokenomics

def create_founder_identity():
    """Create the founder identity with maximum privileges."""
    try:
        print("🛡️ Creating ONNYX Founder Identity")
        print("=" * 50)

        # Generate founder identity
        founder_id = str(uuid.uuid4())
        timestamp = int(datetime.now(timezone.utc).timestamp())

        # Founder details
        founder_data = {
            'identity_id': founder_id,
            'name': 'ONNYX Founder',
            'email': '<EMAIL>',
            'role': 'FOUNDER',
            'nation_name': 'Judah',  # Using nation_name for covenant tribe
            'nation_code': 'US',
            'verification_level': 3,  # Maximum verification level
            'etzem_score': 1000,  # Highest possible score
            'status': 'active',
            'covenant_accepted': True,
            'protection_tier': 'TIER_3',
            'vault_status': 'active',
            'sabbath_observer': True,
            'created_at': timestamp,
            'updated_at': timestamp
        }

        # Insert founder identity
        db.execute("""
            INSERT INTO identities (
                identity_id, name, email, public_key, role, nation_name, nation_code,
                verification_level, etzem_score, status, covenant_accepted,
                protection_tier, vault_status, sabbath_observer, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            founder_data['identity_id'],
            founder_data['name'],
            founder_data['email'],
            f"founder_public_key_{founder_id[:8]}",  # Genesis public key
            founder_data['role'],
            founder_data['nation_name'],
            founder_data['nation_code'],
            founder_data['verification_level'],
            founder_data['etzem_score'],
            founder_data['status'],
            founder_data['covenant_accepted'],
            founder_data['protection_tier'],
            founder_data['vault_status'],
            founder_data['sabbath_observer'],
            founder_data['created_at'],
            founder_data['updated_at']
        ))

        # Create founder token balance (10,000 ONX)
        db.execute("""
            INSERT INTO token_balances (
                token_id, identity_id, balance, updated_at
            ) VALUES (?, ?, ?, ?)
        """, ('ONX', founder_id, 10000, timestamp))

        # Record deed for founder creation
        db.execute("""
            INSERT INTO deeds_ledger (
                identity_id, deed_type, deed_value, description,
                timestamp, block_height
            ) VALUES (?, ?, ?, ?, ?, ?)
        """, (
            founder_id,
            "FOUNDER_GENESIS",
            1000.0,  # High deed value for founder
            "Genesis founder identity created with covenant leadership privileges - Genesis 49:10",
            timestamp,
            0  # Genesis block height
        ))

        print(f"✅ Founder Identity Created:")
        print(f"   Identity ID: {founder_id}")
        print(f"   Name: {founder_data['name']}")
        print(f"   Role: {founder_data['role']}")
        print(f"   Covenant Tribe: {founder_data['nation_name']}")
        print(f"   Verification Level: {founder_data['verification_level']} (Maximum)")
        print(f"   Etzem Score: {founder_data['etzem_score']} (Highest)")
        print(f"   Initial ONX Balance: 10,000 ONX")
        print(f"   Governance Privileges: Full council voting rights")

        return founder_id

    except Exception as e:
        print(f"❌ Failed to create founder identity: {e}")
        return None

def verify_founder_creation(founder_id):
    """Verify the founder was created correctly."""
    try:
        print("\n🔍 Verifying Founder Creation...")

        # Check founder identity
        founder = db.query_one("SELECT * FROM identities WHERE identity_id = ?", (founder_id,))
        if not founder:
            print("❌ Founder identity not found")
            return False

        # Check founder balance
        balance = db.query_one("SELECT balance FROM token_balances WHERE identity_id = ? AND token_id = 'ONX'", (founder_id,))
        if not balance or balance['balance'] != 10000:
            print("❌ Founder balance incorrect")
            return False

        # Check deed record
        deed = db.query_one("SELECT * FROM deeds_ledger WHERE identity_id = ? AND deed_type = 'FOUNDER_GENESIS'", (founder_id,))
        if not deed:
            print("❌ Founder deed not recorded")
            return False

        print("✅ Founder verification successful:")
        print(f"   Identity: {founder['name']} ({founder['role']})")
        print(f"   Tribe: {founder['nation_name']}")
        print(f"   Verification Level: {founder['verification_level']}")
        print(f"   Etzem Score: {founder['etzem_score']}")
        print(f"   ONX Balance: {balance['balance']:,}")
        print(f"   Deed Recorded: {deed['deed_type']}")

        return True

    except Exception as e:
        print(f"❌ Founder verification failed: {e}")
        return False

def main():
    """Main function."""
    print("🚀 ONNYX Genesis Founder Creation")
    print("=" * 50)

    # Create founder identity
    founder_id = create_founder_identity()
    if not founder_id:
        print("❌ Founder creation failed")
        return False

    # Verify creation
    if not verify_founder_creation(founder_id):
        print("❌ Founder verification failed")
        return False

    print("\n🎉 Founder Creation Complete!")
    print("=" * 50)
    print("✅ ONNYX Founder identity created successfully")
    print("✅ Maximum privileges assigned")
    print("✅ Ready for system validator creation")
    print(f"\n📋 Founder ID for next step: {founder_id}")

    return founder_id

if __name__ == "__main__":
    founder_id = main()
    if not founder_id:
        sys.exit(1)
