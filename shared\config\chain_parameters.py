"""
Onnyx Chain Parameters Module

This module provides the ChainParameters class for managing chain parameters.
"""

import os
import json
import logging
from typing import Dict, Any, Optional

# Set up logging
logger = logging.getLogger("onnyx.config.chain_parameters")

# Default chain parameters
DEFAULTS = {
    "block_reward": 10,
    "reward_token": "ONX",
    "quorum_percent": 50,
    "vote_pass_ratio": 0.6,
    "mint_cap_factor": 1.0,
    "min_etzem_score": 30,
    "validator_badges": ["VALIDATOR_ELIGIBLE_BADGE"],
    "council_badges": ["COUNCIL_ELIGIBLE_BADGE"],
    "guardian_badges": ["GUARDIAN_ELIGIBLE_BADGE"],
    "proposal_badges": ["PROPOSAL_ELIGIBLE_BADGE"],
    "validator_rotation_interval": 3600,
    "scroll_voting_period": 604800,  # 7 days in seconds
    "scroll_implementation_delay": 86400,  # 1 day in seconds
    "max_token_supply": 1000000000,
    "min_stake_amount": 100,
    "stake_lock_period": 2592000,  # 30 days in seconds
    "max_mempool_size": 1000,
    "max_block_size": 1000000,
    "target_block_time": 60,  # seconds
    "difficulty_adjustment_period": 100,  # blocks

    # Biblical Sabbath Configuration
    "sabbath_start_day": 5,  # Friday (0=Monday, 6=Sunday)
    "sabbath_start_hour": 18,  # 6 PM
    "sabbath_duration_hours": 25,  # 25 hours (Friday 6 PM to Saturday 7 PM)
    "sabbath_timezone": "UTC",  # Default timezone for Sabbath calculations
    "sabbath_timezone_offset": 0,  # UTC offset in hours

    # Mining Configuration
    "mining_enabled": True,
    "mining_difficulty": 1,
    "mining_reward_base": 10,
    "mining_intensity_levels": ["low", "medium", "high", "maximum"],
    "max_transaction_size": 100000,
    "max_transactions_per_block": 1000,

    # Biblical Tokenomics Parameters
    "min_block_reward": 2,
    "max_block_reward": 200,
    "gleaning_pool_percentage": 0.02,  # 2% of mining rewards
    "jubilee_interval_blocks": 50400,  # ~35 days at 60s blocks (7 years = 50400 blocks)
    "dormancy_threshold_blocks": 7200,  # ~5 days of inactivity
    "dormancy_reclaim_percentage": 0.1,  # 10% of dormant balance reclaimed
    "deed_score_multiplier": 0.1,  # Max 10% bonus from deeds
    "firstfruits_etzem_reward": 2,  # Etzem tokens per firstfruits offering
    "sabbath_deed_bonus": 0.2,  # Deed score bonus for sabbath observance
    "loan_grace_blocks": 14400,  # ~10 days grace period
    "loan_forgiveness_threshold": 0.8,  # Forgive after 80% repayment
    "concentration_threshold": 1000000,  # Max balance before concentration penalties
    "concentration_penalty_rate": 0.1  # Reduce rewards to 10% when over threshold
}

class ChainParameters:
    """
    ChainParameters manages the chain parameters for the Onnyx blockchain.
    """

    def __init__(self, path: str = "data/chain_params.json"):
        """
        Initialize the ChainParameters.

        Args:
            path: Path to the chain parameters JSON file
        """
        self.path = path
        self.params = self._load()

    def _load(self) -> Dict[str, Any]:
        """
        Load the chain parameters from the file.

        Returns:
            The chain parameters
        """
        try:
            if os.path.exists(self.path) and os.path.getsize(self.path) > 0:
                with open(self.path, "r") as f:
                    loaded_params = json.load(f)
                    # Merge with defaults to ensure all parameters are present
                    params = DEFAULTS.copy()
                    params.update(loaded_params)
                    logger.info(f"Loaded chain parameters from {self.path}")
                    return params
            else:
                # Save the default parameters
                self._save(DEFAULTS)
                logger.info(f"Created default chain parameters at {self.path}")
                return DEFAULTS.copy()
        except Exception as e:
            logger.error(f"Error loading chain parameters: {str(e)}")
            return DEFAULTS.copy()

    def _save(self, params: Dict[str, Any]):
        """
        Save the chain parameters to the file.

        Args:
            params: The chain parameters to save
        """
        try:
            # Ensure the directory exists
            os.makedirs(os.path.dirname(self.path), exist_ok=True)

            with open(self.path, "w") as f:
                json.dump(params, f, indent=2)
                logger.info(f"Saved chain parameters to {self.path}")
        except Exception as e:
            logger.error(f"Error saving chain parameters: {str(e)}")

    def get(self, key: str, default: Any = None) -> Any:
        """
        Get a chain parameter.

        Args:
            key: The parameter key
            default: The default value to return if the key is not found

        Returns:
            The parameter value
        """
        return self.params.get(key, default if default is not None else DEFAULTS.get(key))

    def set(self, key: str, value: Any):
        """
        Set a chain parameter.

        Args:
            key: The parameter key
            value: The parameter value
        """
        self.params[key] = value
        self._save(self.params)
        logger.info(f"Set chain parameter {key} to {value}")

    def update(self, params: Dict[str, Any]):
        """
        Update multiple chain parameters.

        Args:
            params: The parameters to update
        """
        self.params.update(params)
        self._save(self.params)
        logger.info(f"Updated chain parameters: {list(params.keys())}")

    def reset(self, key: str):
        """
        Reset a chain parameter to its default value.

        Args:
            key: The parameter key
        """
        if key in DEFAULTS:
            self.params[key] = DEFAULTS[key]
            self._save(self.params)
            logger.info(f"Reset chain parameter {key} to default value {DEFAULTS[key]}")
        else:
            logger.warning(f"No default value for chain parameter {key}")

    def reset_all(self):
        """Reset all chain parameters to their default values."""
        self.params = DEFAULTS.copy()
        self._save(self.params)
        logger.info("Reset all chain parameters to default values")

    def all(self) -> Dict[str, Any]:
        """
        Get all chain parameters.

        Returns:
            All chain parameters
        """
        return self.params.copy()

# Create a global instance of the ChainParameters
chain_parameters = ChainParameters()
