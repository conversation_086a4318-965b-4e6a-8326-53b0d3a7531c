# ONNYX Backend Documentation

This directory contains essential documentation for the ONNYX blockchain platform backend.

## 📚 Documentation Index

### Core Documents

#### [ONNYX Code of Ethics and Foundational Principles](ONNYX_CODE_OF_ETHICS.md)
**Biblical foundation and ethical framework for all ONNYX development**
- Scripture-based development principles
- Independence from corrupt worldly systems
- Practical implementation guidelines
- Partnership and governance standards

#### [Deployment Guide](DEPLOYMENT.md)
**Complete production deployment documentation**
- Single deployment strategy
- Local development setup
- Production configuration
- Auto-mining system setup
- Monitoring and troubleshooting

#### [Business Onboarding Guide](BUSINESS_ONBOARDING_GUIDE.md)
**Step-by-step guide for business validator registration**
- Identity creation process
- Sela validator setup
- Mining tier assignment
- Auto-mining configuration

#### [Complete User Journey](COMPLETE_USER_JOURNEY.md)
**End-to-end user experience documentation**
- New user onboarding flow
- Business registration process
- Mining operations workflow
- Platform feature overview

## 🎯 Current Platform State

### Production Status
- **Live Deployment**: https://onnyx-backend.onrender.com
- **Phase 1 Complete**: Genesis identity and real business validators operational
- **Auto-Mining Active**: Tiered reward system functioning
- **Database**: Production SQLite with real transaction data

### Active Components
- **Identity Management**: Cryptographic registration and authentication
- **Sela Validator Network**: 3 active business validators
- **Mining System**: Hybrid Proof-of-Trust + Performance Boost
- **Token Economy**: ONNX token with 22,222,200 total supply
- **Web Interface**: Unified Flask application with Onyx Stone theme

## 🔧 Technical Architecture

### Backend Structure
```
onnyx-backend/
├── web/           # Flask application (single source of truth)
├── shared/        # Core models, database, utilities
├── blockchain/    # Consensus, mining, validation
└── docs/          # Essential documentation
```

### Key Technologies
- **Backend**: Flask (Python 3.9+)
- **Database**: SQLite with production schema
- **Frontend**: Integrated Jinja2 templates + Tailwind CSS
- **Mining**: Background daemon processes
- **Authentication**: Session-based with cryptographic keys

## 📖 Biblical Foundation

All ONNYX development is guided by biblical principles:

*"But seek first the kingdom of God and his righteousness, and all these things will be added to you."* - Matthew 6:33

### Core Values
- **Truth and Transparency**: Honest communication and open processes
- **Justice and Fairness**: Anti-usury principles and equitable rewards
- **Stewardship**: Responsible resource management
- **Community**: Mutual aid and collaborative governance
- **Independence**: Separation from corrupt worldly systems

## 🚀 Getting Started

### For Developers
1. Read the [Code of Ethics](ONNYX_CODE_OF_ETHICS.md) to understand our biblical foundation
2. Follow the [Deployment Guide](DEPLOYMENT.md) for local setup
3. Review the platform architecture in the main [README](../README.md)

### For Business Owners
1. Visit https://onnyx-backend.onrender.com
2. Follow the [Business Onboarding Guide](BUSINESS_ONBOARDING_GUIDE.md)
3. Review the [Complete User Journey](COMPLETE_USER_JOURNEY.md)

### For Users
1. Start with identity registration at the live platform
2. Explore the validator network and blockchain explorer
3. Consider registering your business as a validator

## 📞 Support

- **Platform**: https://onnyx-backend.onrender.com
- **Health Check**: `/health` endpoint
- **API Documentation**: `/api/docs` (when available)
- **Issues**: Repository issue tracker

## 🔄 Updates

This documentation reflects the current state of the ONNYX platform as of Phase 1 Production Launch. Updates will be made as new features are developed and deployed.

---

**ONNYX Backend Documentation** - Technical guidance rooted in biblical principles.

*"Commit your work to the Lord, and your plans will be established."* - Proverbs 16:3
