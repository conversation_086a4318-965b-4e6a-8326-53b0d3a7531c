"""
ONNYX Proof-of-Covenant Consensus
Biblical governance-based consensus mechanism for the ONNYX blockchain
"""

import asyncio
import json
import time
import hashlib
import logging
from typing import Dict, List, Optional, Set
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class VoteType(Enum):
    APPROVE = "approve"
    REJECT = "reject"
    ABSTAIN = "abstain"

class ProposalStatus(Enum):
    PROPOSED = "proposed"
    VOTING = "voting"
    APPROVED = "approved"
    REJECTED = "rejected"
    EXECUTED = "executed"

@dataclass
class CovenantVote:
    voter_id: str
    tribal_code: str
    vote_type: VoteType
    voting_weight: int
    timestamp: int
    signature: str
    biblical_justification: Optional[str] = None

@dataclass
class BlockProposal:
    proposal_id: str
    proposer_id: str
    block_height: int
    block_hash: str
    transactions: List[dict]
    timestamp: int
    biblical_compliance_score: float
    votes: Dict[str, CovenantVote]
    status: ProposalStatus = ProposalStatus.PROPOSED
    
    def get_vote_tally(self) -> Dict[str, int]:
        """Calculate vote tally with tribal weights"""
        tally = {"approve": 0, "reject": 0, "abstain": 0}
        for vote in self.votes.values():
            tally[vote.vote_type.value] += vote.voting_weight
        return tally
    
    def is_approved(self, required_majority: float = 0.67) -> bool:
        """Check if proposal meets biblical 2/3 majority requirement"""
        tally = self.get_vote_tally()
        total_votes = sum(tally.values())
        if total_votes == 0:
            return False
        
        approval_ratio = tally["approve"] / total_votes
        return approval_ratio >= required_majority

@dataclass
class VoiceScrollProposal:
    scroll_id: str
    proposer_id: str
    title: str
    content: dict
    biblical_references: List[str]
    voting_requirements: dict
    votes: Dict[str, CovenantVote]
    status: ProposalStatus = ProposalStatus.PROPOSED
    created_at: int = 0
    voting_deadline: int = 0
    
    def __post_init__(self):
        if self.created_at == 0:
            self.created_at = int(time.time())
        if self.voting_deadline == 0:
            # Default 7-day voting period (biblical week)
            self.voting_deadline = self.created_at + (7 * 24 * 60 * 60)

class ProofOfCovenant:
    """
    Proof-of-Covenant consensus mechanism implementing biblical governance principles
    """
    
    def __init__(self, peer_manager):
        self.peer_manager = peer_manager
        self.active_proposals: Dict[str, BlockProposal] = {}
        self.voice_scroll_proposals: Dict[str, VoiceScrollProposal] = {}
        self.consensus_history: List[dict] = []
        
        # Biblical consensus parameters
        self.required_elder_participation = 0.67  # 2/3 majority
        self.required_approval_ratio = 0.67  # 2/3 approval
        self.voting_period = 300  # 5 minutes for block proposals
        self.voice_scroll_voting_period = 7 * 24 * 60 * 60  # 7 days
        
        # Tribal voting weights (from Genesis)
        self.tribal_weights = {
            "JU": 2,  # Judah - Royal tribe
            "LE": 2,  # Levi - Priestly tribe  
            "EP": 2,  # Ephraim - Fruitful tribe
            "BE": 1, "SI": 1, "MA": 1, "IS": 1,
            "ZE": 1, "NA": 1, "GA": 1, "AS": 1, "RE": 1
        }
        
        # Register consensus message handlers
        self._register_consensus_handlers()
    
    def _register_consensus_handlers(self):
        """Register consensus-specific message handlers"""
        handlers = {
            'block_proposal': self._handle_block_proposal,
            'block_vote': self._handle_block_vote,
            'voice_scroll_proposal': self._handle_voice_scroll_proposal,
            'voice_scroll_vote': self._handle_voice_scroll_vote,
            'consensus_query': self._handle_consensus_query
        }
        
        for msg_type, handler in handlers.items():
            self.peer_manager.register_message_handler(msg_type, handler)
    
    async def propose_block(self, block_data: dict) -> str:
        """Propose a new block for consensus"""
        proposal_id = hashlib.sha256(
            f"{block_data['block_hash']}{time.time()}".encode()
        ).hexdigest()[:16]
        
        # Calculate biblical compliance score
        compliance_score = self._calculate_biblical_compliance(block_data)
        
        proposal = BlockProposal(
            proposal_id=proposal_id,
            proposer_id=self.peer_manager.node_id,
            block_height=block_data['block_height'],
            block_hash=block_data['block_hash'],
            transactions=block_data.get('transactions', []),
            timestamp=int(time.time()),
            biblical_compliance_score=compliance_score,
            votes={}
        )
        
        self.active_proposals[proposal_id] = proposal
        
        # Broadcast proposal to tribal elders
        await self._broadcast_block_proposal(proposal)
        
        # Start voting timer
        asyncio.create_task(self._voting_timeout(proposal_id, self.voting_period))
        
        logger.info(f"Block proposal {proposal_id} created with compliance score {compliance_score}")
        return proposal_id
    
    async def propose_voice_scroll(self, scroll_data: dict) -> str:
        """Propose a new voice scroll for governance"""
        scroll_id = scroll_data.get('scroll_id') or f"SCROLL_{int(time.time())}"
        
        proposal = VoiceScrollProposal(
            scroll_id=scroll_id,
            proposer_id=self.peer_manager.node_id,
            title=scroll_data['title'],
            content=scroll_data['content'],
            biblical_references=scroll_data.get('biblical_references', []),
            voting_requirements=scroll_data.get('voting_requirements', {}),
            votes={}
        )
        
        self.voice_scroll_proposals[scroll_id] = proposal
        
        # Broadcast to all tribal elders
        await self._broadcast_voice_scroll_proposal(proposal)
        
        # Start voting timer (7 days)
        asyncio.create_task(self._voting_timeout(scroll_id, self.voice_scroll_voting_period, is_voice_scroll=True))
        
        logger.info(f"Voice scroll proposal {scroll_id} created: {proposal.title}")
        return scroll_id
    
    async def cast_vote(self, proposal_id: str, vote_type: VoteType, 
                       voter_id: str, tribal_code: str, 
                       signature: str, justification: str = None) -> bool:
        """Cast a vote on a proposal"""
        
        # Verify voter is a tribal elder
        tribal_elders = self.peer_manager.get_tribal_elders()
        voter_peer = next((p for p in tribal_elders if p.peer_id == voter_id), None)
        
        if not voter_peer:
            logger.warning(f"Vote rejected: {voter_id} is not a tribal elder")
            return False
        
        # Determine voting weight
        voting_weight = self.tribal_weights.get(tribal_code, 1)
        
        vote = CovenantVote(
            voter_id=voter_id,
            tribal_code=tribal_code,
            vote_type=vote_type,
            voting_weight=voting_weight,
            timestamp=int(time.time()),
            signature=signature,
            biblical_justification=justification
        )
        
        # Add vote to appropriate proposal
        if proposal_id in self.active_proposals:
            self.active_proposals[proposal_id].votes[voter_id] = vote
            await self._check_block_consensus(proposal_id)
        elif proposal_id in self.voice_scroll_proposals:
            self.voice_scroll_proposals[proposal_id].votes[voter_id] = vote
            await self._check_voice_scroll_consensus(proposal_id)
        else:
            logger.warning(f"Vote rejected: Unknown proposal {proposal_id}")
            return False
        
        # Broadcast vote to network
        await self._broadcast_vote(proposal_id, vote)
        
        logger.info(f"Vote cast by {tribal_code} elder on proposal {proposal_id}: {vote_type.value}")
        return True
    
    async def _check_block_consensus(self, proposal_id: str):
        """Check if block proposal has reached consensus"""
        proposal = self.active_proposals.get(proposal_id)
        if not proposal:
            return
        
        # Check if we have enough participation
        tribal_elders = self.peer_manager.get_tribal_elders()
        total_elder_weight = sum(self.tribal_weights.get(elder.tribal_code, 1) 
                               for elder in tribal_elders)
        
        voted_weight = sum(vote.voting_weight for vote in proposal.votes.values())
        participation_ratio = voted_weight / max(total_elder_weight, 1)
        
        if participation_ratio >= self.required_elder_participation:
            if proposal.is_approved(self.required_approval_ratio):
                proposal.status = ProposalStatus.APPROVED
                await self._execute_block_proposal(proposal)
                logger.info(f"Block proposal {proposal_id} APPROVED by covenant consensus")
            else:
                proposal.status = ProposalStatus.REJECTED
                logger.info(f"Block proposal {proposal_id} REJECTED by covenant consensus")
            
            # Broadcast consensus result
            await self._broadcast_consensus_result(proposal_id, proposal.status)
    
    async def _check_voice_scroll_consensus(self, scroll_id: str):
        """Check if voice scroll proposal has reached consensus"""
        proposal = self.voice_scroll_proposals.get(scroll_id)
        if not proposal:
            return
        
        # Voice scrolls require higher participation and approval
        tribal_elders = self.peer_manager.get_tribal_elders()
        total_elder_weight = sum(self.tribal_weights.get(elder.tribal_code, 1) 
                               for elder in tribal_elders)
        
        voted_weight = sum(vote.voting_weight for vote in proposal.votes.values())
        participation_ratio = voted_weight / max(total_elder_weight, 1)
        
        # Require 75% participation for voice scrolls
        if participation_ratio >= 0.75:
            tally = {}
            for vote in proposal.votes.values():
                vote_type = vote.vote_type.value
                tally[vote_type] = tally.get(vote_type, 0) + vote.voting_weight
            
            total_votes = sum(tally.values())
            approval_ratio = tally.get("approve", 0) / max(total_votes, 1)
            
            if approval_ratio >= 0.75:  # 3/4 majority for voice scrolls
                proposal.status = ProposalStatus.APPROVED
                await self._execute_voice_scroll_proposal(proposal)
                logger.info(f"Voice scroll {scroll_id} APPROVED by covenant consensus")
            else:
                proposal.status = ProposalStatus.REJECTED
                logger.info(f"Voice scroll {scroll_id} REJECTED by covenant consensus")
            
            # Broadcast consensus result
            await self._broadcast_consensus_result(scroll_id, proposal.status, is_voice_scroll=True)
    
    def _calculate_biblical_compliance(self, block_data: dict) -> float:
        """Calculate biblical compliance score for a block"""
        score = 1.0
        transactions = block_data.get('transactions', [])
        
        for tx in transactions:
            tx_data = tx.get('data', {})
            
            # Check for usury violations
            if tx.get('op') == 'OP_LEND' and tx_data.get('interest_rate', 0) > 0:
                score -= 0.2  # Penalty for usury
            
            # Check for Sabbath violations
            if self._is_sabbath_violation(tx):
                score -= 0.1
            
            # Check for gleaning pool compliance
            if tx.get('op') == 'OP_HARVEST' and not tx_data.get('gleaning_reserved'):
                score -= 0.1
            
            # Bonus for covenant-compliant transactions
            if tx.get('op') in ['OP_GLEANING_DISTRIBUTE', 'OP_YOVEL_REDISTRIBUTE']:
                score += 0.1
        
        return max(0.0, min(1.0, score))
    
    def _is_sabbath_violation(self, transaction: dict) -> bool:
        """Check if transaction violates Sabbath principles"""
        # Simplified Sabbath check - in production would be more sophisticated
        tx_time = transaction.get('timestamp', 0)
        day_of_week = time.gmtime(tx_time).tm_wday
        
        # Saturday (6) is Sabbath - restrict certain operations
        if day_of_week == 5:  # Saturday (0=Monday)
            forbidden_ops = ['OP_MINE', 'OP_TRADE', 'OP_BUSINESS']
            if transaction.get('op') in forbidden_ops:
                return True
        
        return False
    
    # Message Handlers
    async def _handle_block_proposal(self, message):
        """Handle incoming block proposals"""
        data = message.data
        proposal_id = data['proposal_id']
        
        # Store proposal for voting
        proposal = BlockProposal(
            proposal_id=proposal_id,
            proposer_id=message.sender_id,
            block_height=data['block_height'],
            block_hash=data['block_hash'],
            transactions=data['transactions'],
            timestamp=data['timestamp'],
            biblical_compliance_score=data['biblical_compliance_score'],
            votes={}
        )
        
        self.active_proposals[proposal_id] = proposal
        logger.info(f"Received block proposal {proposal_id} from {message.sender_id}")
    
    async def _handle_block_vote(self, message):
        """Handle incoming block votes"""
        data = message.data
        proposal_id = data['proposal_id']
        
        vote = CovenantVote(
            voter_id=message.sender_id,
            tribal_code=data['tribal_code'],
            vote_type=VoteType(data['vote_type']),
            voting_weight=data['voting_weight'],
            timestamp=data['timestamp'],
            signature=data['signature'],
            biblical_justification=data.get('justification')
        )
        
        if proposal_id in self.active_proposals:
            self.active_proposals[proposal_id].votes[message.sender_id] = vote
            await self._check_block_consensus(proposal_id)
    
    async def _handle_voice_scroll_proposal(self, message):
        """Handle incoming voice scroll proposals"""
        data = message.data
        
        proposal = VoiceScrollProposal(
            scroll_id=data['scroll_id'],
            proposer_id=message.sender_id,
            title=data['title'],
            content=data['content'],
            biblical_references=data['biblical_references'],
            voting_requirements=data['voting_requirements'],
            votes={},
            created_at=data['created_at'],
            voting_deadline=data['voting_deadline']
        )
        
        self.voice_scroll_proposals[data['scroll_id']] = proposal
        logger.info(f"Received voice scroll proposal {data['scroll_id']}: {data['title']}")
    
    async def _handle_voice_scroll_vote(self, message):
        """Handle incoming voice scroll votes"""
        data = message.data
        scroll_id = data['scroll_id']
        
        vote = CovenantVote(
            voter_id=message.sender_id,
            tribal_code=data['tribal_code'],
            vote_type=VoteType(data['vote_type']),
            voting_weight=data['voting_weight'],
            timestamp=data['timestamp'],
            signature=data['signature'],
            biblical_justification=data.get('justification')
        )
        
        if scroll_id in self.voice_scroll_proposals:
            self.voice_scroll_proposals[scroll_id].votes[message.sender_id] = vote
            await self._check_voice_scroll_consensus(scroll_id)
    
    async def _handle_consensus_query(self, message):
        """Handle consensus status queries"""
        # Return current consensus state
        response_data = {
            "active_proposals": len(self.active_proposals),
            "voice_scroll_proposals": len(self.voice_scroll_proposals),
            "network_health": self.peer_manager.get_network_stats()["network_health"]
        }
        
        response = self.peer_manager.NetworkMessage(
            message_type="consensus_status",
            sender_id=self.peer_manager.node_id,
            recipient_id=message.sender_id,
            data=response_data,
            timestamp=int(time.time())
        )
        
        await self.peer_manager.send_to_peer(message.sender_id, response)
    
    # Broadcasting Methods
    async def _broadcast_block_proposal(self, proposal: BlockProposal):
        """Broadcast block proposal to tribal elders"""
        message = self.peer_manager.NetworkMessage(
            message_type="block_proposal",
            sender_id=self.peer_manager.node_id,
            recipient_id=None,
            data={
                "proposal_id": proposal.proposal_id,
                "block_height": proposal.block_height,
                "block_hash": proposal.block_hash,
                "transactions": proposal.transactions,
                "timestamp": proposal.timestamp,
                "biblical_compliance_score": proposal.biblical_compliance_score
            },
            timestamp=int(time.time())
        )
        
        await self.peer_manager.broadcast_message(message)
    
    async def _broadcast_voice_scroll_proposal(self, proposal: VoiceScrollProposal):
        """Broadcast voice scroll proposal to tribal elders"""
        message = self.peer_manager.NetworkMessage(
            message_type="voice_scroll_proposal",
            sender_id=self.peer_manager.node_id,
            recipient_id=None,
            data={
                "scroll_id": proposal.scroll_id,
                "title": proposal.title,
                "content": proposal.content,
                "biblical_references": proposal.biblical_references,
                "voting_requirements": proposal.voting_requirements,
                "created_at": proposal.created_at,
                "voting_deadline": proposal.voting_deadline
            },
            timestamp=int(time.time())
        )
        
        await self.peer_manager.broadcast_message(message)
    
    async def _broadcast_vote(self, proposal_id: str, vote: CovenantVote):
        """Broadcast vote to network"""
        message = self.peer_manager.NetworkMessage(
            message_type="block_vote" if proposal_id in self.active_proposals else "voice_scroll_vote",
            sender_id=self.peer_manager.node_id,
            recipient_id=None,
            data={
                "proposal_id" if proposal_id in self.active_proposals else "scroll_id": proposal_id,
                "tribal_code": vote.tribal_code,
                "vote_type": vote.vote_type.value,
                "voting_weight": vote.voting_weight,
                "timestamp": vote.timestamp,
                "signature": vote.signature,
                "justification": vote.biblical_justification
            },
            timestamp=int(time.time())
        )
        
        await self.peer_manager.broadcast_message(message)
    
    async def _broadcast_consensus_result(self, proposal_id: str, status: ProposalStatus, is_voice_scroll: bool = False):
        """Broadcast consensus result to network"""
        message = self.peer_manager.NetworkMessage(
            message_type="consensus_result",
            sender_id=self.peer_manager.node_id,
            recipient_id=None,
            data={
                "proposal_id": proposal_id,
                "status": status.value,
                "is_voice_scroll": is_voice_scroll,
                "timestamp": int(time.time())
            },
            timestamp=int(time.time())
        )
        
        await self.peer_manager.broadcast_message(message)
    
    # Execution Methods
    async def _execute_block_proposal(self, proposal: BlockProposal):
        """Execute an approved block proposal"""
        # This would integrate with the blockchain to add the block
        logger.info(f"Executing approved block proposal {proposal.proposal_id}")
        proposal.status = ProposalStatus.EXECUTED
        
        # Record in consensus history
        self.consensus_history.append({
            "type": "block",
            "proposal_id": proposal.proposal_id,
            "block_height": proposal.block_height,
            "approved_at": int(time.time()),
            "vote_tally": proposal.get_vote_tally()
        })
    
    async def _execute_voice_scroll_proposal(self, proposal: VoiceScrollProposal):
        """Execute an approved voice scroll proposal"""
        # This would integrate with the governance system
        logger.info(f"Executing approved voice scroll {proposal.scroll_id}")
        proposal.status = ProposalStatus.EXECUTED
        
        # Record in consensus history
        self.consensus_history.append({
            "type": "voice_scroll",
            "scroll_id": proposal.scroll_id,
            "title": proposal.title,
            "approved_at": int(time.time()),
            "vote_tally": {vote.vote_type.value: sum(v.voting_weight for v in proposal.votes.values() 
                                                   if v.vote_type == vote.vote_type) 
                          for vote in proposal.votes.values()}
        })
    
    async def _voting_timeout(self, proposal_id: str, timeout_seconds: int, is_voice_scroll: bool = False):
        """Handle voting timeout for proposals"""
        await asyncio.sleep(timeout_seconds)
        
        if is_voice_scroll:
            proposal = self.voice_scroll_proposals.get(proposal_id)
            if proposal and proposal.status == ProposalStatus.PROPOSED:
                proposal.status = ProposalStatus.REJECTED
                logger.info(f"Voice scroll {proposal_id} voting timed out - REJECTED")
        else:
            proposal = self.active_proposals.get(proposal_id)
            if proposal and proposal.status == ProposalStatus.PROPOSED:
                proposal.status = ProposalStatus.REJECTED
                logger.info(f"Block proposal {proposal_id} voting timed out - REJECTED")
    
    # Utility Methods
    def get_consensus_stats(self) -> dict:
        """Get consensus statistics"""
        return {
            "active_block_proposals": len([p for p in self.active_proposals.values() 
                                         if p.status == ProposalStatus.PROPOSED]),
            "active_voice_scrolls": len([p for p in self.voice_scroll_proposals.values() 
                                       if p.status == ProposalStatus.PROPOSED]),
            "total_consensus_decisions": len(self.consensus_history),
            "tribal_elder_participation": len(self.peer_manager.get_tribal_elders()),
            "network_consensus_health": "healthy" if len(self.peer_manager.get_tribal_elders()) >= 8 else "degraded"
        }
