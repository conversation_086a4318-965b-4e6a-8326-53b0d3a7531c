/**
 * ONNYX Platform - Comprehensive Responsive Navigation System
 * Implements mobile hamburger menu, active page detection, and cyberpunk-themed interactions
 */

class ONNYXNavigation {
    constructor() {
        this.mobileMenuOpen = false;
        this.userDropdownOpen = false;
        this.activeRoute = 'index';
        
        this.init();
    }

    init() {
        console.log('🚀 ONNYX Navigation System Initializing...');
        
        // Initialize all navigation components
        this.initializeActivePageDetection();
        this.initializeMobileMenu();
        this.initializeUserDropdown();
        this.initializeKeyboardNavigation();
        this.initializeResponsiveHandlers();
        
        console.log('✅ ONNYX Navigation System Ready');
    }

    /**
     * Active Page Detection & Highlighting
     */
    initializeActivePageDetection() {
        const currentPath = window.location.pathname;
        
        // Route detection logic
        if (currentPath === '/' || currentPath === '/index') {
            this.activeRoute = 'index';
        } else if (currentPath.startsWith('/sela')) {
            this.activeRoute = 'sela';
        } else if (currentPath.startsWith('/explorer')) {
            this.activeRoute = 'explorer';
        } else if (currentPath.startsWith('/tokenomics')) {
            this.activeRoute = 'tokenomics';
        } else if (currentPath.startsWith('/dashboard')) {
            this.activeRoute = 'dashboard';
        } else if (currentPath.startsWith('/auto_mining')) {
            this.activeRoute = 'auto_mining';
        }
        
        console.log(`🎯 Active route detected: ${this.activeRoute} (path: ${currentPath})`);
        
        this.applyActiveStates();
    }

    applyActiveStates() {
        // Apply active state to desktop navigation
        const desktopNavItems = document.querySelectorAll('.navbar-nav .nav-item[data-route]');
        desktopNavItems.forEach(item => {
            const itemRoute = item.getAttribute('data-route');
            if (itemRoute === this.activeRoute) {
                item.classList.add('nav-item-active');
                console.log(`✅ Desktop nav item activated: ${itemRoute}`);
            } else {
                item.classList.remove('nav-item-active');
            }
        });
        
        // Apply active state to mobile navigation
        const mobileNavItems = document.querySelectorAll('.mobile-nav-item[data-route]');
        mobileNavItems.forEach(item => {
            const itemRoute = item.getAttribute('data-route');
            if (itemRoute === this.activeRoute) {
                item.classList.add('mobile-nav-item-active');
                console.log(`✅ Mobile nav item activated: ${itemRoute}`);
            } else {
                item.classList.remove('mobile-nav-item-active');
            }
        });
    }

    /**
     * Mobile Hamburger Menu System
     */
    initializeMobileMenu() {
        const mobileBtn = document.getElementById('mobile-menu-button');
        const mobileOverlay = document.getElementById('mobile-menu-overlay');
        const hamburger = document.getElementById('hamburger-icon');

        if (!mobileBtn || !mobileOverlay) {
            console.warn('⚠️ Mobile menu elements not found');
            return;
        }

        // Click handler for mobile button
        mobileBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.toggleMobileMenu();
        });

        // Close menu when clicking outside
        mobileOverlay.addEventListener('click', (e) => {
            if (e.target === mobileOverlay) {
                this.closeMobileMenu();
            }
        });

        // Close menu when clicking nav items
        const mobileNavItems = mobileOverlay.querySelectorAll('.mobile-nav-item, .mobile-auth-btn');
        mobileNavItems.forEach(item => {
            item.addEventListener('click', () => {
                setTimeout(() => this.closeMobileMenu(), 100);
            });
        });

        console.log('✅ Mobile menu initialized');
    }

    toggleMobileMenu() {
        if (this.mobileMenuOpen) {
            this.closeMobileMenu();
        } else {
            this.openMobileMenu();
        }
    }

    openMobileMenu() {
        const mobileBtn = document.getElementById('mobile-menu-button');
        const mobileOverlay = document.getElementById('mobile-menu-overlay');
        const hamburger = document.getElementById('hamburger-icon');

        this.mobileMenuOpen = true;

        // Update ARIA attributes
        mobileBtn.setAttribute('aria-expanded', 'true');
        mobileOverlay.setAttribute('aria-hidden', 'false');

        // Opening animation
        mobileOverlay.style.display = 'block';
        mobileOverlay.style.opacity = '0';
        mobileOverlay.style.transform = 'translateY(-20px)';

        // Force reflow
        mobileOverlay.offsetHeight;

        mobileOverlay.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
        mobileOverlay.style.opacity = '1';
        mobileOverlay.style.transform = 'translateY(0)';

        // Hamburger animation
        if (hamburger) {
            hamburger.classList.add('active');
        }

        // Prevent body scroll
        document.body.style.overflow = 'hidden';

        // Focus management
        setTimeout(() => {
            const firstNavItem = mobileOverlay.querySelector('.mobile-nav-item');
            if (firstNavItem) firstNavItem.focus();
        }, 300);

        console.log('📱 Mobile menu opened');
    }

    closeMobileMenu() {
        const mobileBtn = document.getElementById('mobile-menu-button');
        const mobileOverlay = document.getElementById('mobile-menu-overlay');
        const hamburger = document.getElementById('hamburger-icon');

        this.mobileMenuOpen = false;

        // Update ARIA attributes
        mobileBtn.setAttribute('aria-expanded', 'false');
        mobileOverlay.setAttribute('aria-hidden', 'true');

        // Closing animation
        mobileOverlay.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
        mobileOverlay.style.opacity = '0';
        mobileOverlay.style.transform = 'translateY(-20px)';

        setTimeout(() => {
            mobileOverlay.style.display = 'none';
        }, 300);

        // Hamburger animation
        if (hamburger) {
            hamburger.classList.remove('active');
        }

        // Restore body scroll
        document.body.style.overflow = '';

        // Return focus to button
        mobileBtn.focus();

        console.log('📱 Mobile menu closed');
    }

    /**
     * User Dropdown System
     */
    initializeUserDropdown() {
        const userButton = document.querySelector('.user-button');
        const userMenu = document.querySelector('.user-menu');
        const chevron = document.querySelector('.dropdown-chevron');

        if (!userButton || !userMenu) {
            console.log('ℹ️ User dropdown not present (guest user)');
            return;
        }

        userButton.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.toggleUserDropdown();
        });

        // Close when clicking outside
        document.addEventListener('click', (e) => {
            if (!userButton.contains(e.target) && !userMenu.contains(e.target) && this.userDropdownOpen) {
                this.closeUserDropdown();
            }
        });

        console.log('✅ User dropdown initialized');
    }

    toggleUserDropdown() {
        if (this.userDropdownOpen) {
            this.closeUserDropdown();
        } else {
            this.openUserDropdown();
        }
    }

    openUserDropdown() {
        const userMenu = document.querySelector('.user-menu');
        const chevron = document.querySelector('.dropdown-chevron');

        this.userDropdownOpen = true;

        userMenu.style.display = 'block';
        userMenu.style.opacity = '0';
        userMenu.style.transform = 'scale(0.95)';

        // Force reflow
        userMenu.offsetHeight;

        userMenu.style.transition = 'opacity 0.2s ease, transform 0.2s ease';
        userMenu.style.opacity = '1';
        userMenu.style.transform = 'scale(1)';

        if (chevron) {
            chevron.style.transform = 'rotate(180deg)';
        }

        console.log('👤 User dropdown opened');
    }

    closeUserDropdown() {
        const userMenu = document.querySelector('.user-menu');
        const chevron = document.querySelector('.dropdown-chevron');

        this.userDropdownOpen = false;

        userMenu.style.transition = 'opacity 0.15s ease, transform 0.15s ease';
        userMenu.style.opacity = '0';
        userMenu.style.transform = 'scale(0.95)';

        setTimeout(() => {
            userMenu.style.display = 'none';
        }, 150);

        if (chevron) {
            chevron.style.transform = 'rotate(0deg)';
        }

        console.log('👤 User dropdown closed');
    }

    /**
     * Keyboard Navigation Support
     */
    initializeKeyboardNavigation() {
        document.addEventListener('keydown', (e) => {
            // Escape key closes any open menus
            if (e.key === 'Escape') {
                if (this.mobileMenuOpen) {
                    this.closeMobileMenu();
                }
                if (this.userDropdownOpen) {
                    this.closeUserDropdown();
                }
            }
        });

        console.log('✅ Keyboard navigation initialized');
    }

    /**
     * Responsive Handlers
     */
    initializeResponsiveHandlers() {
        // Monitor viewport changes
        window.addEventListener('resize', () => {
            this.handleViewportChange();
        });

        // Initial viewport check
        this.handleViewportChange();

        console.log('✅ Responsive handlers initialized');
    }

    handleViewportChange() {
        const width = window.innerWidth;
        
        // Close mobile menu if viewport becomes desktop
        if (width > 768 && this.mobileMenuOpen) {
            this.closeMobileMenu();
        }
        
        // Log viewport state for debugging
        if (width <= 768) {
            console.log(`📱 Mobile viewport: ${width}px`);
        } else if (width <= 992) {
            console.log(`📱 Tablet viewport: ${width}px`);
        } else {
            console.log(`🖥️ Desktop viewport: ${width}px`);
        }
    }
}

// Initialize navigation system when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.onnyxNavigation = new ONNYXNavigation();
    });
} else {
    window.onnyxNavigation = new ONNYXNavigation();
}
