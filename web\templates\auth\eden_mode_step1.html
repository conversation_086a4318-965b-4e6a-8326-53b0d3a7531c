{% extends "base.html" %}

{% block title %}The Forgotten Legacy - Eden Mode Awakening{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-onyx-black via-onyx-dark to-onyx-black relative overflow-hidden">
    <!-- Ambient Background Effects -->
    <div class="absolute inset-0 opacity-20">
        <div class="floating-particles"></div>
        <div class="data-streams"></div>
    </div>

    <!-- Progress Indicator - Non-sticky with Enhanced Design -->
    <div class="flex justify-center mb-12 pt-8">
        <div class="glass-card-enhanced px-8 py-6 rounded-3xl shadow-lg shadow-cyber-cyan/10">
            <div class="flex items-center space-x-6">
                <div class="w-10 h-10 rounded-full bg-gradient-to-br from-cyber-cyan to-cyber-blue flex items-center justify-center text-onyx-black font-bold text-lg shadow-lg shadow-cyber-cyan/30 animate-pulse">1</div>
                <div class="w-12 h-1 bg-gradient-to-r from-cyber-cyan to-glass-border rounded-full"></div>
                <div class="w-10 h-10 rounded-full bg-glass-border flex items-center justify-center text-text-tertiary font-bold text-lg transition-all duration-300 hover:bg-glass-hover">2</div>
                <div class="w-12 h-1 bg-glass-border rounded-full"></div>
                <div class="w-10 h-10 rounded-full bg-glass-border flex items-center justify-center text-text-tertiary font-bold text-lg transition-all duration-300 hover:bg-glass-hover">3</div>
                <div class="w-12 h-1 bg-glass-border rounded-full"></div>
                <div class="w-10 h-10 rounded-full bg-glass-border flex items-center justify-center text-text-tertiary font-bold text-lg transition-all duration-300 hover:bg-glass-hover">4</div>
                <div class="w-12 h-1 bg-glass-border rounded-full"></div>
                <div class="w-10 h-10 rounded-full bg-glass-border flex items-center justify-center text-text-tertiary font-bold text-lg transition-all duration-300 hover:bg-glass-hover">5</div>
            </div>
            <div class="flex items-center justify-center mt-4">
                <span class="text-sm font-orbitron text-cyber-cyan">The Forgotten Legacy</span>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container-xl mx-auto px-6 pt-32 pb-16">
        <div class="max-w-4xl mx-auto">

            <!-- Opening Revelation -->
            <div class="text-center mb-16 fade-in-sequence" data-delay="0">
                <div class="mb-8">
                    <div class="inline-block glass-card-premium p-6 rounded-3xl mb-6">
                        <svg class="w-16 h-16 text-cyber-cyan mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                                  d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                    </div>
                    <h1 class="text-6xl md:text-7xl font-orbitron font-bold text-cyber-cyan mb-6 glow-text">
                        The Forgotten Legacy
                    </h1>
                    <p class="text-2xl text-text-secondary max-w-3xl mx-auto leading-relaxed">
                        "Enquire, I pray thee, of the former age, and prepare thyself to the search of their fathers"
                    </p>
                    <p class="text-lg text-cyber-purple font-orbitron mt-4">
                        — Job 8:8
                    </p>
                </div>
            </div>

            <!-- Narrative Content -->
            <div class="space-y-12">
                <!-- The Awakening -->
                <div class="glass-card-premium p-12 rounded-3xl fade-in-sequence" data-delay="500">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                        <div>
                            <h2 class="text-4xl font-orbitron font-bold text-cyber-cyan mb-6">
                                🌅 The Awakening Begins
                            </h2>
                            <div class="space-y-6 text-lg text-text-secondary leading-relaxed">
                                <p>
                                    For generations, the true identity of nations has been <span class="text-cyber-cyan font-semibold">systematically erased</span>.
                                    Ancient bloodlines, forgotten. Sacred covenants, buried beneath layers of deception.
                                </p>
                                <p>
                                    But the blockchain remembers what flesh forgets. Every transaction, every covenant,
                                    every act of righteousness—<span class="text-cyber-purple font-semibold">permanently inscribed</span>
                                    in the immutable ledger of truth.
                                </p>
                                <p class="text-cyber-cyan font-semibold">
                                    You are not here by accident. Your awakening has begun.
                                </p>
                            </div>
                        </div>
                        <div class="relative">
                            <div class="glass-card-enhanced p-8 rounded-2xl text-center">
                                <div class="text-6xl mb-4">🏛️</div>
                                <h3 class="text-2xl font-orbitron font-bold text-cyber-purple mb-4">
                                    Know Who You Are
                                </h3>
                                <p class="text-text-secondary">
                                    Your identity is not a social construct—it's a <strong>covenant inheritance</strong>
                                    written in the stars and confirmed in the blockchain.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- The Hidden Truth -->
                <div class="glass-card-premium p-12 rounded-3xl fade-in-sequence" data-delay="1000">
                    <div class="text-center mb-8">
                        <h2 class="text-4xl font-orbitron font-bold text-cyber-purple mb-6">
                            🔍 The Hidden Truth
                        </h2>
                        <p class="text-xl text-text-secondary max-w-3xl mx-auto">
                            "They have said, Come, and let us cut them off from being a nation;
                            that the name of Israel may be no more in remembrance."
                        </p>
                        <p class="text-lg text-cyber-cyan font-orbitron mt-4">
                            — Psalms 83:4
                        </p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
                        <div class="glass-card-enhanced p-6 rounded-xl text-center">
                            <div class="text-4xl mb-4">📜</div>
                            <h3 class="text-xl font-orbitron font-bold text-cyber-cyan mb-3">Ancient Scrolls</h3>
                            <p class="text-text-secondary">
                                Hidden genealogies and tribal records, preserved in sacred texts but
                                <strong>forgotten by the world</strong>.
                            </p>
                        </div>
                        <div class="glass-card-enhanced p-6 rounded-xl text-center">
                            <div class="text-4xl mb-4">🌍</div>
                            <h3 class="text-xl font-orbitron font-bold text-cyber-cyan mb-3">Scattered Nations</h3>
                            <p class="text-text-secondary">
                                The twelve tribes dispersed across continents, their true identity
                                <strong>hidden in plain sight</strong>.
                            </p>
                        </div>
                        <div class="glass-card-enhanced p-6 rounded-xl text-center">
                            <div class="text-4xl mb-4">⛓️</div>
                            <h3 class="text-xl font-orbitron font-bold text-cyber-cyan mb-3">Broken Chains</h3>
                            <p class="text-text-secondary">
                                Economic slavery through usury and debt, designed to keep the covenant people
                                <strong>in perpetual bondage</strong>.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- The Blockchain Solution -->
                <div class="glass-card-premium p-12 rounded-3xl fade-in-sequence" data-delay="1500">
                    <div class="text-center">
                        <h2 class="text-4xl font-orbitron font-bold text-cyber-green mb-8">
                            ⛓️ The Blockchain Awakening
                        </h2>
                        <div class="max-w-4xl mx-auto">
                            <p class="text-xl text-text-secondary leading-relaxed mb-8">
                                But now, in these last days, the Most High has provided a way to
                                <span class="text-cyber-cyan font-semibold">restore what was lost</span>.
                                Through immutable ledger technology, we can rebuild the covenant economy,
                                reconnect with our true heritage, and establish
                                <span class="text-cyber-green font-semibold">economic sovereignty</span>
                                according to biblical principles.
                            </p>

                            <div class="glass-card-enhanced p-8 rounded-2xl bg-gradient-to-r from-cyber-cyan/10 to-cyber-purple/10">
                                <h3 class="text-2xl font-orbitron font-bold text-cyber-cyan mb-4">
                                    Know Where Your Value Goes
                                </h3>
                                <p class="text-lg text-text-secondary">
                                    Every token earned, every covenant kept, every act of righteousness—
                                    <strong>transparently recorded</strong> and <strong>fairly distributed</strong>
                                    according to the ancient laws of jubilee, gleaning, and sabbath rest.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Call to Action -->
            <div class="text-center mt-16 fade-in-sequence" data-delay="2000">
                <div class="glass-card-premium p-8 rounded-2xl inline-block">
                    <h3 class="text-2xl font-orbitron font-bold text-cyber-cyan mb-6">
                        Are you ready to discover your true identity?
                    </h3>
                    <p class="text-lg text-text-secondary mb-8 max-w-2xl">
                        Step into Eden Mode and begin your journey from forgotten legacy to covenant restoration.
                        Your ancestors are calling. The blockchain is waiting.
                    </p>

                    <button id="beginJourney"
                            class="glass-button-primary px-12 py-4 rounded-2xl font-orbitron font-bold text-xl transition-all duration-500 hover:scale-105 glow-on-hover min-h-[56px] flex items-center justify-center shadow-lg shadow-cyber-cyan/20 hover:shadow-xl hover:shadow-cyber-cyan/30">
                        <span class="mr-3 text-2xl">🚪</span>
                        <span>Begin the Journey</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Audio Controls (Optional) -->
<div class="fixed bottom-6 right-6 z-50">
    <div class="glass-card-enhanced p-4 rounded-xl">
        <button id="audioToggle" class="text-cyber-cyan hover:text-cyber-purple transition-colors">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 14.142M9 9a3 3 0 000 6h4v.01M9 15h4v.01"></path>
            </svg>
        </button>
    </div>
</div>

<script>
// Initialize Eden Mode Step 1
document.addEventListener('DOMContentLoaded', function() {
    // Initialize animations
    initializeSequentialAnimations();

    // Setup audio controls
    setupAudioControls();

    // Setup journey progression
    setupJourneyProgression();
});

function initializeSequentialAnimations() {
    const elements = document.querySelectorAll('.fade-in-sequence');

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const delay = entry.target.dataset.delay || 0;
                setTimeout(() => {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }, delay);
            }
        });
    }, { threshold: 0.1 });

    elements.forEach(element => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(40px)';
        element.style.transition = 'opacity 1s ease, transform 1s ease';
        observer.observe(element);
    });
}

function setupAudioControls() {
    const audioToggle = document.getElementById('audioToggle');
    let audioEnabled = false;

    audioToggle.addEventListener('click', function() {
        audioEnabled = !audioEnabled;
        // Audio implementation would go here
        console.log('Audio toggled:', audioEnabled);
    });
}

function setupJourneyProgression() {
    const beginButton = document.getElementById('beginJourney');

    beginButton.addEventListener('click', function() {
        // Add loading state
        beginButton.innerHTML = '🌟 Awakening...';
        beginButton.disabled = true;

        // Transition to step 2
        setTimeout(() => {
            window.location.href = '/auth/eden-mode/step2';
        }, 1500);
    });
}
</script>

<style>
.glow-text {
    text-shadow: 0 0 20px rgba(0, 255, 247, 0.5);
}

.glow-on-hover:hover {
    box-shadow: 0 0 30px rgba(0, 255, 247, 0.4);
}

.floating-particles::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(2px 2px at 20px 30px, rgba(0, 255, 247, 0.3), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(154, 0, 255, 0.3), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(0, 255, 247, 0.2), transparent);
    background-repeat: repeat;
    background-size: 100px 100px;
    animation: float 20s linear infinite;
}

@keyframes float {
    0% { transform: translateY(0px) translateX(0px); }
    33% { transform: translateY(-20px) translateX(10px); }
    66% { transform: translateY(-10px) translateX(-10px); }
    100% { transform: translateY(0px) translateX(0px); }
}
</style>
{% endblock %}
