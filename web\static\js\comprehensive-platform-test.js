/**
 * ONNYX Platform Comprehensive Testing Suite
 * Production readiness validation across all features
 */

class ComprehensivePlatformTest {
    constructor() {
        this.testResults = {
            homepage: {},
            navigation: {},
            authentication: {},
            validatorDirectory: {},
            blockchainExplorer: {},
            performance: {},
            accessibility: {},
            seo: {},
            responsiveness: {}
        };
        
        this.init();
    }

    async init() {
        console.log('🧪 ONNYX Comprehensive Platform Test Starting...');
        console.log('=====================================');
        
        await this.testHomepage();
        await this.testNavigation();
        await this.testAuthentication();
        await this.testValidatorDirectory();
        await this.testBlockchainExplorer();
        await this.testPerformance();
        await this.testAccessibility();
        await this.testSEO();
        await this.testResponsiveness();
        
        this.generateComprehensiveReport();
    }

    /**
     * Test homepage enhancements
     */
    async testHomepage() {
        console.log('🏠 Testing Homepage Enhancements...');
        
        // Test value proposition
        const valueProposition = document.querySelector('h2:contains("Reclaim Your Digital Identity")');
        this.testResults.homepage.valueProposition = !!valueProposition;
        
        // Test value cards
        const valueCards = document.querySelectorAll('.glass-card h3');
        this.testResults.homepage.valueCards = valueCards.length >= 3;
        
        // Test user journey section
        const userJourney = document.querySelector('h2:contains("Your Path to Digital Freedom")');
        this.testResults.homepage.userJourney = !!userJourney;
        
        // Test social proof
        const socialProof = document.querySelectorAll('.text-cyber-cyan:contains("+")');
        this.testResults.homepage.socialProof = socialProof.length >= 3;
        
        // Test CTAs
        const ctaButtons = document.querySelectorAll('.glass-button-primary');
        this.testResults.homepage.clearCTAs = ctaButtons.length >= 2;
        
        console.log('✅ Homepage tests complete');
    }

    /**
     * Test navigation system
     */
    async testNavigation() {
        console.log('🧭 Testing Navigation System...');
        
        // Test floating orb
        const floatingOrb = document.querySelector('.floating-auth-orb');
        this.testResults.navigation.floatingOrb = !!floatingOrb;
        
        // Test orb menu
        const orbMenu = document.querySelector('.orb-menu');
        this.testResults.navigation.orbMenu = !!orbMenu;
        
        // Test mobile menu
        const mobileMenu = document.querySelector('#mobile-menu-overlay');
        this.testResults.navigation.mobileMenu = !!mobileMenu;
        
        // Test sticky navigation
        const navbar = document.querySelector('.onnyx-navbar');
        this.testResults.navigation.stickyNav = !!navbar;
        
        // Test active states
        const activeNavItems = document.querySelectorAll('.nav-item.active, .mobile-nav-item.active');
        this.testResults.navigation.activeStates = activeNavItems.length >= 0; // May be 0 on some pages
        
        console.log('✅ Navigation tests complete');
    }

    /**
     * Test authentication features
     */
    async testAuthentication() {
        console.log('🔐 Testing Authentication Features...');
        
        // Test orb options
        const orbOptions = document.querySelectorAll('.orb-option');
        this.testResults.authentication.orbOptions = orbOptions.length >= 2;
        
        // Test security badge
        const securityBadge = document.querySelector('.orb-security-badge');
        this.testResults.authentication.securityBadge = !!securityBadge;
        
        // Test registration paths
        const registrationLinks = document.querySelectorAll('a[href*="register"]');
        this.testResults.authentication.registrationPaths = registrationLinks.length >= 2;
        
        console.log('✅ Authentication tests complete');
    }

    /**
     * Test validator directory enhancements
     */
    async testValidatorDirectory() {
        console.log('🏢 Testing Validator Directory...');
        
        if (window.location.pathname.includes('/sela/directory')) {
            // Test ROI calculator
            const roiCalculator = document.querySelector('#business-type');
            this.testResults.validatorDirectory.roiCalculator = !!roiCalculator;
            
            // Test validator spotlight
            const validatorSpotlight = document.querySelector('h2:contains("Validator Spotlight")');
            this.testResults.validatorDirectory.validatorSpotlight = !!validatorSpotlight;
            
            // Test why become validator section
            const whyBecomeValidator = document.querySelector('h2:contains("Why Become a Validator")');
            this.testResults.validatorDirectory.whyBecomeValidator = !!whyBecomeValidator;
            
            // Test search functionality
            const searchInput = document.querySelector('#validator-search');
            this.testResults.validatorDirectory.searchFunctionality = !!searchInput;
        } else {
            this.testResults.validatorDirectory.notOnPage = true;
        }
        
        console.log('✅ Validator Directory tests complete');
    }

    /**
     * Test blockchain explorer enhancements
     */
    async testBlockchainExplorer() {
        console.log('🔍 Testing Blockchain Explorer...');
        
        if (window.location.pathname.includes('/explorer')) {
            // Test transparency section
            const transparencySection = document.querySelector('h2:contains("Why Transparency Matters")');
            this.testResults.blockchainExplorer.transparencySection = !!transparencySection;
            
            // Test business benefits
            const businessBenefits = document.querySelector('h3:contains("For Your Business")');
            this.testResults.blockchainExplorer.businessBenefits = !!businessBenefits;
            
            // Test glossary
            const glossary = document.querySelector('h2:contains("Blockchain Terms Made Simple")');
            this.testResults.blockchainExplorer.glossary = !!glossary;
            
            // Test conversion CTAs
            const conversionCTAs = document.querySelectorAll('a[href*="register"]');
            this.testResults.blockchainExplorer.conversionCTAs = conversionCTAs.length >= 1;
        } else {
            this.testResults.blockchainExplorer.notOnPage = true;
        }
        
        console.log('✅ Blockchain Explorer tests complete');
    }

    /**
     * Test performance metrics
     */
    async testPerformance() {
        console.log('⚡ Testing Performance...');
        
        // Test load time
        const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
        this.testResults.performance.loadTime = loadTime;
        this.testResults.performance.fastLoad = loadTime < 3000;
        
        // Test image optimization
        const images = document.querySelectorAll('img');
        let optimizedImages = 0;
        images.forEach(img => {
            if (img.loading === 'lazy' || img.hasAttribute('data-src')) {
                optimizedImages++;
            }
        });
        this.testResults.performance.imageOptimization = optimizedImages / images.length;
        
        // Test service worker
        this.testResults.performance.serviceWorker = 'serviceWorker' in navigator;
        
        // Test performance optimizer
        this.testResults.performance.performanceOptimizer = !!window.PerformanceOptimizer;
        
        console.log('✅ Performance tests complete');
    }

    /**
     * Test accessibility compliance
     */
    async testAccessibility() {
        console.log('♿ Testing Accessibility...');
        
        // Test ARIA labels
        const ariaLabels = document.querySelectorAll('[aria-label]');
        this.testResults.accessibility.ariaLabels = ariaLabels.length;
        
        // Test semantic HTML
        const semanticElements = document.querySelectorAll('nav, main, section, article, header, footer');
        this.testResults.accessibility.semanticHTML = semanticElements.length >= 3;
        
        // Test focus indicators
        const focusableElements = document.querySelectorAll('a, button, input, select, textarea');
        this.testResults.accessibility.focusableElements = focusableElements.length;
        
        // Test alt text on images
        const imagesWithAlt = document.querySelectorAll('img[alt]');
        const totalImages = document.querySelectorAll('img');
        this.testResults.accessibility.imageAltText = imagesWithAlt.length / totalImages.length;
        
        // Test color contrast (simplified check)
        this.testResults.accessibility.colorContrast = this.checkColorContrast();
        
        console.log('✅ Accessibility tests complete');
    }

    /**
     * Test SEO optimization
     */
    async testSEO() {
        console.log('🔍 Testing SEO Optimization...');
        
        // Test meta tags
        const metaDescription = document.querySelector('meta[name="description"]');
        this.testResults.seo.metaDescription = !!metaDescription;
        
        const metaKeywords = document.querySelector('meta[name="keywords"]');
        this.testResults.seo.metaKeywords = !!metaKeywords;
        
        // Test Open Graph tags
        const ogTitle = document.querySelector('meta[property="og:title"]');
        this.testResults.seo.openGraph = !!ogTitle;
        
        // Test Twitter Card tags
        const twitterCard = document.querySelector('meta[name="twitter:card"]');
        this.testResults.seo.twitterCard = !!twitterCard;
        
        // Test structured data
        const structuredData = document.querySelector('script[type="application/ld+json"]');
        this.testResults.seo.structuredData = !!structuredData;
        
        // Test canonical URL
        const canonical = document.querySelector('link[rel="canonical"]');
        this.testResults.seo.canonical = !!canonical;
        
        console.log('✅ SEO tests complete');
    }

    /**
     * Test responsive design
     */
    async testResponsiveness() {
        console.log('📱 Testing Responsive Design...');
        
        const breakpoints = [480, 640, 768, 1024, 1280, 1536];
        const currentWidth = window.innerWidth;
        
        // Test mobile menu visibility
        const mobileMenuButton = document.querySelector('#mobile-menu-button');
        this.testResults.responsiveness.mobileMenuButton = !!mobileMenuButton;
        
        // Test floating orb responsiveness
        const floatingOrb = document.querySelector('.floating-auth-orb');
        if (floatingOrb) {
            const orbStyles = window.getComputedStyle(floatingOrb);
            this.testResults.responsiveness.floatingOrbVisible = orbStyles.display !== 'none';
        }
        
        // Test grid layouts
        const gridElements = document.querySelectorAll('[class*="grid-cols"]');
        this.testResults.responsiveness.gridLayouts = gridElements.length;
        
        // Test touch targets (minimum 44px)
        const touchTargets = document.querySelectorAll('button, a, input, select');
        let validTouchTargets = 0;
        touchTargets.forEach(element => {
            const rect = element.getBoundingClientRect();
            if (rect.height >= 44 && rect.width >= 44) {
                validTouchTargets++;
            }
        });
        this.testResults.responsiveness.touchTargetCompliance = validTouchTargets / touchTargets.length;
        
        console.log('✅ Responsive design tests complete');
    }

    /**
     * Check color contrast (simplified)
     */
    checkColorContrast() {
        // Simplified contrast check - in production, use a proper contrast analyzer
        const textElements = document.querySelectorAll('p, h1, h2, h3, h4, h5, h6, span, a');
        let contrastIssues = 0;
        
        textElements.forEach(element => {
            const styles = window.getComputedStyle(element);
            const color = styles.color;
            const backgroundColor = styles.backgroundColor;
            
            // Very basic check - in production, calculate actual contrast ratios
            if (color === backgroundColor) {
                contrastIssues++;
            }
        });
        
        return contrastIssues === 0;
    }

    /**
     * Generate comprehensive test report
     */
    generateComprehensiveReport() {
        console.log('\n📊 COMPREHENSIVE PLATFORM TEST REPORT');
        console.log('=====================================');
        
        // Homepage Report
        console.log('\n🏠 Homepage Enhancements:');
        console.log(`  ✅ Value Proposition: ${this.testResults.homepage.valueProposition ? 'PASS' : 'FAIL'}`);
        console.log(`  ✅ Value Cards: ${this.testResults.homepage.valueCards ? 'PASS' : 'FAIL'}`);
        console.log(`  ✅ User Journey: ${this.testResults.homepage.userJourney ? 'PASS' : 'FAIL'}`);
        console.log(`  ✅ Social Proof: ${this.testResults.homepage.socialProof ? 'PASS' : 'FAIL'}`);
        console.log(`  ✅ Clear CTAs: ${this.testResults.homepage.clearCTAs ? 'PASS' : 'FAIL'}`);
        
        // Navigation Report
        console.log('\n🧭 Navigation System:');
        console.log(`  ✅ Floating Orb: ${this.testResults.navigation.floatingOrb ? 'PASS' : 'FAIL'}`);
        console.log(`  ✅ Orb Menu: ${this.testResults.navigation.orbMenu ? 'PASS' : 'FAIL'}`);
        console.log(`  ✅ Mobile Menu: ${this.testResults.navigation.mobileMenu ? 'PASS' : 'FAIL'}`);
        console.log(`  ✅ Sticky Nav: ${this.testResults.navigation.stickyNav ? 'PASS' : 'FAIL'}`);
        
        // Authentication Report
        console.log('\n🔐 Authentication:');
        console.log(`  ✅ Orb Options: ${this.testResults.authentication.orbOptions ? 'PASS' : 'FAIL'}`);
        console.log(`  ✅ Security Badge: ${this.testResults.authentication.securityBadge ? 'PASS' : 'FAIL'}`);
        console.log(`  ✅ Registration Paths: ${this.testResults.authentication.registrationPaths ? 'PASS' : 'FAIL'}`);
        
        // Performance Report
        console.log('\n⚡ Performance:');
        console.log(`  ✅ Load Time: ${this.testResults.performance.loadTime}ms (${this.testResults.performance.fastLoad ? 'PASS' : 'FAIL'})`);
        console.log(`  ✅ Image Optimization: ${(this.testResults.performance.imageOptimization * 100).toFixed(1)}%`);
        console.log(`  ✅ Service Worker: ${this.testResults.performance.serviceWorker ? 'PASS' : 'FAIL'}`);
        console.log(`  ✅ Performance Optimizer: ${this.testResults.performance.performanceOptimizer ? 'PASS' : 'FAIL'}`);
        
        // Accessibility Report
        console.log('\n♿ Accessibility:');
        console.log(`  ✅ ARIA Labels: ${this.testResults.accessibility.ariaLabels} elements`);
        console.log(`  ✅ Semantic HTML: ${this.testResults.accessibility.semanticHTML ? 'PASS' : 'FAIL'}`);
        console.log(`  ✅ Image Alt Text: ${(this.testResults.accessibility.imageAltText * 100).toFixed(1)}%`);
        console.log(`  ✅ Color Contrast: ${this.testResults.accessibility.colorContrast ? 'PASS' : 'FAIL'}`);
        
        // SEO Report
        console.log('\n🔍 SEO Optimization:');
        console.log(`  ✅ Meta Description: ${this.testResults.seo.metaDescription ? 'PASS' : 'FAIL'}`);
        console.log(`  ✅ Meta Keywords: ${this.testResults.seo.metaKeywords ? 'PASS' : 'FAIL'}`);
        console.log(`  ✅ Open Graph: ${this.testResults.seo.openGraph ? 'PASS' : 'FAIL'}`);
        console.log(`  ✅ Twitter Card: ${this.testResults.seo.twitterCard ? 'PASS' : 'FAIL'}`);
        console.log(`  ✅ Structured Data: ${this.testResults.seo.structuredData ? 'PASS' : 'FAIL'}`);
        
        // Responsiveness Report
        console.log('\n📱 Responsive Design:');
        console.log(`  ✅ Mobile Menu: ${this.testResults.responsiveness.mobileMenuButton ? 'PASS' : 'FAIL'}`);
        console.log(`  ✅ Touch Targets: ${(this.testResults.responsiveness.touchTargetCompliance * 100).toFixed(1)}%`);
        console.log(`  ✅ Grid Layouts: ${this.testResults.responsiveness.gridLayouts} elements`);
        
        // Overall Score
        const overallScore = this.calculateOverallScore();
        console.log(`\n🎯 Overall Production Readiness: ${overallScore.toFixed(1)}%`);
        
        if (overallScore >= 95) {
            console.log('🚀 EXCELLENT! Platform is production-ready.');
        } else if (overallScore >= 90) {
            console.log('✅ VERY GOOD! Minor optimizations recommended.');
        } else if (overallScore >= 80) {
            console.log('⚠️  GOOD! Some improvements needed.');
        } else {
            console.log('❌ NEEDS WORK! Significant improvements required.');
        }
        
        console.log('\n=====================================');
        
        return {
            score: overallScore,
            results: this.testResults
        };
    }

    /**
     * Calculate overall production readiness score
     */
    calculateOverallScore() {
        const scores = [];
        
        // Homepage (20%)
        const homepageScore = Object.values(this.testResults.homepage)
            .filter(v => typeof v === 'boolean')
            .reduce((acc, val) => acc + (val ? 1 : 0), 0) / 5 * 100;
        scores.push(homepageScore * 0.20);
        
        // Navigation (15%)
        const navScore = Object.values(this.testResults.navigation)
            .filter(v => typeof v === 'boolean')
            .reduce((acc, val) => acc + (val ? 1 : 0), 0) / 4 * 100;
        scores.push(navScore * 0.15);
        
        // Authentication (10%)
        const authScore = Object.values(this.testResults.authentication)
            .filter(v => typeof v === 'boolean')
            .reduce((acc, val) => acc + (val ? 1 : 0), 0) / 3 * 100;
        scores.push(authScore * 0.10);
        
        // Performance (20%)
        const perfScore = (this.testResults.performance.fastLoad ? 25 : 0) +
                         (this.testResults.performance.serviceWorker ? 25 : 0) +
                         (this.testResults.performance.performanceOptimizer ? 25 : 0) +
                         (this.testResults.performance.imageOptimization * 25);
        scores.push(perfScore * 0.20);
        
        // Accessibility (15%)
        const accessScore = (this.testResults.accessibility.semanticHTML ? 25 : 0) +
                           (this.testResults.accessibility.colorContrast ? 25 : 0) +
                           (this.testResults.accessibility.imageAltText * 50);
        scores.push(accessScore * 0.15);
        
        // SEO (10%)
        const seoScore = Object.values(this.testResults.seo)
            .filter(v => typeof v === 'boolean')
            .reduce((acc, val) => acc + (val ? 1 : 0), 0) / 6 * 100;
        scores.push(seoScore * 0.10);
        
        // Responsiveness (10%)
        const respScore = (this.testResults.responsiveness.mobileMenuButton ? 50 : 0) +
                         (this.testResults.responsiveness.touchTargetCompliance * 50);
        scores.push(respScore * 0.10);
        
        return scores.reduce((acc, score) => acc + score, 0);
    }
}

// Run comprehensive test when requested
document.addEventListener('DOMContentLoaded', () => {
    // Only run when explicitly requested or in development
    if (window.location.search.includes('test=comprehensive') || 
        document.body.dataset.environment === 'development') {
        setTimeout(() => {
            new ComprehensivePlatformTest();
        }, 2000); // Wait for page to fully load
    }
});

// Export for manual testing
window.ComprehensivePlatformTest = ComprehensivePlatformTest;
