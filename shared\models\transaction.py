"""
Onnyx Transaction Model

This module provides the Transaction model for the Onnyx blockchain.
"""

import time
import logging
from typing import Dict, Any, List, Optional, ClassVar

from shared.models.base import BaseModel
from shared.db.db import db

# Set up logging
logger = logging.getLogger("onnyx.models.transaction")

class Transaction(BaseModel):
    """
    Transaction model for the Onnyx blockchain.
    """

    # Table name
    table_name: ClassVar[str] = "transactions"

    # Primary key column
    primary_key: ClassVar[str] = "tx_id"

    # JSON fields
    json_fields: ClassVar[List[str]] = ["data"]

    def __init__(
        self,
        tx_id: str,
        timestamp: int,
        op: str,
        data: Dict[str, Any],
        sender: str,
        signature: str,
        status: str = "pending",
        block_hash: Optional[str] = None,
        created_at: int = None,
        **kwargs
    ):
        """
        Initialize the Transaction model.

        Args:
            tx_id: The transaction ID
            timestamp: The transaction timestamp
            op: The transaction operation
            data: The transaction data
            sender: The sender identity ID
            signature: The transaction signature
            status: The transaction status
            block_hash: The block hash
            created_at: The creation timestamp
            **kwargs: Additional attributes
        """
        self.tx_id = tx_id
        self.timestamp = timestamp
        self.op = op
        self.data = data
        self.sender = sender
        self.signature = signature
        self.status = status
        self.block_hash = block_hash
        self.created_at = created_at or int(time.time())

        super().__init__(**kwargs)

    @classmethod
    def get_by_op(cls, op: str, limit: int = 100, offset: int = 0) -> List['Transaction']:
        """
        Get transactions by operation.

        Args:
            op: The transaction operation
            limit: The maximum number of transactions to return
            offset: The offset for pagination

        Returns:
            A list of transactions
        """
        query = f"SELECT * FROM {cls.table_name} WHERE op = ? ORDER BY timestamp DESC LIMIT ? OFFSET ?"
        rows = db.query(query, (op, limit, offset))

        return [cls.from_dict(row) for row in rows]

    @classmethod
    def get_by_sender(cls, sender: str, limit: int = 100, offset: int = 0) -> List['Transaction']:
        """
        Get transactions by sender.

        Args:
            sender: The sender identity ID
            limit: The maximum number of transactions to return
            offset: The offset for pagination

        Returns:
            A list of transactions
        """
        query = f"SELECT * FROM {cls.table_name} WHERE sender = ? ORDER BY timestamp DESC LIMIT ? OFFSET ?"
        rows = db.query(query, (sender, limit, offset))

        return [cls.from_dict(row) for row in rows]

    @classmethod
    def get_by_status(cls, status: str, limit: int = 100, offset: int = 0) -> List['Transaction']:
        """
        Get transactions by status.

        Args:
            status: The transaction status
            limit: The maximum number of transactions to return
            offset: The offset for pagination

        Returns:
            A list of transactions
        """
        query = f"SELECT * FROM {cls.table_name} WHERE status = ? ORDER BY timestamp DESC LIMIT ? OFFSET ?"
        rows = db.query(query, (status, limit, offset))

        return [cls.from_dict(row) for row in rows]

    @classmethod
    def get_by_block(cls, block_hash: str) -> List['Transaction']:
        """
        Get transactions by block hash.

        Args:
            block_hash: The block hash

        Returns:
            A list of transactions
        """
        query = f"SELECT * FROM {cls.table_name} WHERE block_hash = ? ORDER BY timestamp ASC"
        rows = db.query(query, (block_hash,))

        return [cls.from_dict(row) for row in rows]

    @classmethod
    def get_pending(cls) -> List['Transaction']:
        """
        Get pending transactions.

        Returns:
            A list of pending transactions
        """
        return cls.get_by_status("pending")

    @classmethod
    def get_confirmed(cls, limit: int = 100, offset: int = 0) -> List['Transaction']:
        """
        Get confirmed transactions.

        Args:
            limit: The maximum number of transactions to return
            offset: The offset for pagination

        Returns:
            A list of confirmed transactions
        """
        return cls.get_by_status("confirmed", limit, offset)

    def confirm(self, block_hash: str) -> None:
        """
        Confirm the transaction.

        Args:
            block_hash: The block hash
        """
        self.status = "confirmed"
        self.block_hash = block_hash
        self.save()

    def reject(self) -> None:
        """
        Reject the transaction.
        """
        self.status = "rejected"
        self.save()

    @classmethod
    def create(
        cls,
        op: str,
        data: Dict[str, Any],
        sender: str,
        signature: str
    ) -> 'Transaction':
        """
        Create a new transaction.

        Args:
            op: The transaction operation
            data: The transaction data
            sender: The sender identity ID
            signature: The transaction signature

        Returns:
            The created transaction
        """
        # Generate the transaction ID
        import hashlib
        timestamp = int(time.time())
        tx_data = f"{op}{data}{sender}{signature}{timestamp}"
        tx_id = hashlib.sha256(tx_data.encode()).hexdigest()

        logger.info(f"Creating transaction with ID: {tx_id}")
        logger.info(f"Operation: {op}")
        logger.info(f"Data: {data}")
        logger.info(f"Sender: {sender}")

        # Create the transaction
        transaction = cls(
            tx_id=tx_id,
            timestamp=timestamp,
            op=op,
            data=data,
            sender=sender,
            signature=signature
        )

        # Save the transaction
        try:
            # Convert the transaction to a dictionary for debugging
            tx_dict = transaction.to_dict()
            logger.info(f"Transaction dictionary: {tx_dict}")

            # Save the transaction directly using db.insert
            from shared.db.db import db
            db.insert("transactions", tx_dict)
            logger.info(f"Transaction saved successfully: {tx_id}")

            # Verify that the transaction was saved
            saved_tx = cls.get_by_id(tx_id)
            if saved_tx:
                logger.info(f"Transaction verified in database: {tx_id}")
            else:
                logger.error(f"Transaction not found in database after save: {tx_id}")
        except Exception as e:
            logger.error(f"Error saving transaction: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            # Continue despite the error

        return transaction
