{% extends "base.html" %}

{% block title %}My Biblical Tokenomics Dashboard - Onnyx Platform{% endblock %}

{% block head %}
<style>
    .deed-score-circle {
        background: conic-gradient(from 0deg, #00fff7 0%, #9a00ff {{ (user_stats.deed_score * 10)|round }}%, #2a2a2a {{ (user_stats.deed_score * 10)|round }}%);
        border-radius: 50%;
        padding: 4px;
    }
    
    .deed-score-inner {
        background: #1a1a1a;
        border-radius: 50%;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
    }
    
    .concentration-warning {
        background: linear-gradient(135deg, rgba(255, 165, 0, 0.1) 0%, rgba(255, 69, 0, 0.1) 100%);
        border: 1px solid rgba(255, 165, 0, 0.3);
    }
    
    .sabbath-observer {
        background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 255, 255, 0.1) 100%);
        border: 1px solid rgba(255, 215, 0, 0.3);
    }
    
    .loan-card {
        transition: all 0.3s ease;
    }
    
    .loan-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 255, 247, 0.15);
    }
    
    .deed-item {
        border-left: 3px solid;
        padding-left: 1rem;
    }
    
    .deed-mutual-aid { border-left-color: #00fff7; }
    .deed-firstfruits { border-left-color: #9a00ff; }
    .deed-sabbath { border-left-color: #ffd700; }
    .deed-lending { border-left-color: #0066ff; }
    .deed-default { border-left-color: #6b7280; }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-onyx-black">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-4xl font-orbitron font-bold mb-4">
                <span class="bg-gradient-to-r from-cyber-cyan to-cyber-purple bg-clip-text text-transparent">
                    My Biblical Tokenomics Dashboard
                </span>
            </h1>
            <p class="text-gray-300">Track your righteous deeds, manage loans, and monitor your spiritual economy.</p>
        </div>
        
        <!-- Sabbath Status -->
        {% if system_stats.is_sabbath %}
        <div class="sabbath-observer glass-card p-6 mb-8 text-center">
            <div class="flex items-center justify-center space-x-3 mb-4">
                <span class="text-3xl">🕊️</span>
                <h2 class="text-xl font-semibold text-yellow-300">Sabbath Period - Time for Rest</h2>
            </div>
            <p class="text-yellow-200">Mining operations are paused. Use this time for reflection and community.</p>
        </div>
        {% endif %}
        
        <!-- Personal Stats Overview -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Deed Score -->
            <div class="glass-card p-6 text-center">
                <div class="deed-score-circle w-24 h-24 mx-auto mb-4">
                    <div class="deed-score-inner">
                        <span class="text-2xl font-bold text-cyber-cyan">{{ "%.1f"|format(user_stats.deed_score) }}</span>
                        <span class="text-xs text-gray-400">Deed Score</span>
                    </div>
                </div>
                <h3 class="text-lg font-semibold mb-2">Righteousness Level</h3>
                <p class="text-sm text-gray-400">
                    {% if user_stats.deed_score >= 5 %}Highly Righteous
                    {% elif user_stats.deed_score >= 2 %}Righteous
                    {% elif user_stats.deed_score >= 1 %}Growing
                    {% else %}Beginning{% endif %}
                </p>
            </div>
            
            <!-- Sabbath Observer Status -->
            <div class="glass-card p-6 text-center {% if user_stats.is_sabbath_observer %}sabbath-observer{% endif %}">
                <div class="text-4xl mb-4">{% if user_stats.is_sabbath_observer %}🕊️{% else %}⏰{% endif %}</div>
                <h3 class="text-lg font-semibold mb-2">Sabbath Observer</h3>
                <p class="text-sm {% if user_stats.is_sabbath_observer %}text-yellow-300{% else %}text-gray-400{% endif %}">
                    {% if user_stats.is_sabbath_observer %}Faithful Rest Keeper{% else %}Not Observing{% endif %}
                </p>
            </div>
            
            <!-- Concentration Status -->
            <div class="glass-card p-6 text-center {% if user_stats.concentration_status == 'warning' %}concentration-warning{% endif %}">
                <div class="text-4xl mb-4">
                    {% if user_stats.concentration_status == 'warning' %}⚠️{% else %}✅{% endif %}
                </div>
                <h3 class="text-lg font-semibold mb-2">Wealth Status</h3>
                <p class="text-sm {% if user_stats.concentration_status == 'warning' %}text-orange-300{% else %}text-green-300{% endif %}">
                    {% if user_stats.concentration_status == 'warning' %}Above Threshold{% else %}Within Limits{% endif %}
                </p>
                <p class="text-xs text-gray-400 mt-1">{{ "%.0f"|format(user_stats.total_balance) }} / {{ "%.0f"|format(system_stats.concentration_threshold) }}</p>
            </div>
            
            <!-- Active Loans -->
            <div class="glass-card p-6 text-center">
                <div class="text-4xl mb-4">🤝</div>
                <h3 class="text-lg font-semibold mb-2">Active Loans</h3>
                <p class="text-2xl font-bold text-cyber-purple mb-1">
                    {{ user_stats.active_loans_as_borrower|length + user_stats.active_loans_as_lender|length }}
                </p>
                <p class="text-xs text-gray-400">
                    {{ user_stats.active_loans_as_borrower|length }} borrowed, {{ user_stats.active_loans_as_lender|length }} lent
                </p>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <a href="{{ url_for('tokenomics.firstfruits') }}" class="glass-card p-6 hover:bg-white/5 transition-all duration-300 group">
                <div class="flex items-center space-x-4">
                    <div class="text-4xl group-hover:scale-110 transition-transform">🎁</div>
                    <div>
                        <h3 class="text-lg font-semibold text-cyber-purple">Make Firstfruits Offering</h3>
                        <p class="text-sm text-gray-400">Contribute to the community and earn Etzem tokens</p>
                    </div>
                </div>
            </a>
            
            <a href="{{ url_for('tokenomics.loans') }}" class="glass-card p-6 hover:bg-white/5 transition-all duration-300 group">
                <div class="flex items-center space-x-4">
                    <div class="text-4xl group-hover:scale-110 transition-transform">🤝</div>
                    <div>
                        <h3 class="text-lg font-semibold text-cyber-blue">Manage Loans</h3>
                        <p class="text-sm text-gray-400">Create or repay interest-free loans</p>
                    </div>
                </div>
            </a>
            
            <a href="{{ url_for('tokenomics.gleaning') }}" class="glass-card p-6 hover:bg-white/5 transition-all duration-300 group">
                <div class="flex items-center space-x-4">
                    <div class="text-4xl group-hover:scale-110 transition-transform">🌾</div>
                    <div>
                        <h3 class="text-lg font-semibold text-cyber-cyan">Gleaning Pool</h3>
                        <p class="text-sm text-gray-400">Access community support resources</p>
                    </div>
                </div>
            </a>
        </div>
        
        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Recent Deeds -->
            <div class="glass-card p-6">
                <h3 class="text-xl font-semibold mb-6 flex items-center">
                    <span class="mr-3">⭐</span>Recent Righteous Deeds
                </h3>
                
                {% if user_stats.recent_deeds %}
                <div class="space-y-4">
                    {% for deed in user_stats.recent_deeds[:5] %}
                    <div class="deed-item deed-{{ deed.deed_type.lower().replace('_', '-') if deed.deed_type else 'default' }} py-3">
                        <div class="flex justify-between items-start">
                            <div>
                                <h4 class="font-semibold text-sm">
                                    {{ deed.deed_type.replace('_', ' ').title() }}
                                </h4>
                                <p class="text-xs text-gray-400 mt-1">{{ deed.description or 'No description' }}</p>
                                <p class="text-xs text-gray-500 mt-1">
                                    {{ deed.timestamp|timestamp_to_date if deed.timestamp else 'Unknown date' }}
                                </p>
                            </div>
                            <span class="text-sm font-bold text-cyber-cyan">+{{ "%.2f"|format(deed.deed_value) }}</span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                {% if user_stats.recent_deeds|length > 5 %}
                <div class="mt-4 text-center">
                    <a href="#" class="text-cyber-cyan hover:text-cyber-cyan/80 text-sm">View All Deeds</a>
                </div>
                {% endif %}
                {% else %}
                <div class="text-center py-8">
                    <div class="text-4xl mb-4">🌱</div>
                    <p class="text-gray-400">No righteous deeds recorded yet.</p>
                    <p class="text-sm text-gray-500 mt-2">Start by making a firstfruits offering or helping the community!</p>
                </div>
                {% endif %}
            </div>
            
            <!-- Active Loans Summary -->
            <div class="glass-card p-6">
                <h3 class="text-xl font-semibold mb-6 flex items-center">
                    <span class="mr-3">🤝</span>Loan Activity
                </h3>
                
                <!-- Loans as Borrower -->
                {% if user_stats.active_loans_as_borrower %}
                <div class="mb-6">
                    <h4 class="text-lg font-semibold mb-3 text-cyber-purple">As Borrower</h4>
                    {% for loan in user_stats.active_loans_as_borrower[:3] %}
                    <div class="loan-card glass-card p-4 mb-3">
                        <div class="flex justify-between items-start">
                            <div>
                                <p class="font-semibold">{{ "%.2f"|format(loan.amount) }} {{ loan.token_id }}</p>
                                <p class="text-sm text-gray-400">
                                    Paid: {{ "%.2f"|format(loan.amount_paid) }} ({{ "%.0f"|format((loan.amount_paid / loan.amount * 100) if loan.amount > 0 else 0) }}%)
                                </p>
                                <p class="text-xs text-gray-500">
                                    Created: {{ loan.created_at|timestamp_to_date if loan.created_at else 'Unknown' }}
                                </p>
                            </div>
                            <a href="{{ url_for('tokenomics.loans') }}" 
                               class="px-3 py-1 bg-cyber-purple/20 text-cyber-purple rounded text-sm hover:bg-cyber-purple/30">
                                Repay
                            </a>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}
                
                <!-- Loans as Lender -->
                {% if user_stats.active_loans_as_lender %}
                <div class="mb-6">
                    <h4 class="text-lg font-semibold mb-3 text-cyber-blue">As Lender</h4>
                    {% for loan in user_stats.active_loans_as_lender[:3] %}
                    <div class="loan-card glass-card p-4 mb-3">
                        <div class="flex justify-between items-start">
                            <div>
                                <p class="font-semibold">{{ "%.2f"|format(loan.amount) }} {{ loan.token_id }}</p>
                                <p class="text-sm text-gray-400">
                                    Repaid: {{ "%.2f"|format(loan.amount_paid) }} ({{ "%.0f"|format((loan.amount_paid / loan.amount * 100) if loan.amount > 0 else 0) }}%)
                                </p>
                                <p class="text-xs text-gray-500">
                                    Created: {{ loan.created_at|timestamp_to_date if loan.created_at else 'Unknown' }}
                                </p>
                            </div>
                            <span class="px-3 py-1 bg-cyber-blue/20 text-cyber-blue rounded text-sm">
                                Lending
                            </span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}
                
                {% if not user_stats.active_loans_as_borrower and not user_stats.active_loans_as_lender %}
                <div class="text-center py-8">
                    <div class="text-4xl mb-4">🤝</div>
                    <p class="text-gray-400">No active loans.</p>
                    <p class="text-sm text-gray-500 mt-2">Create an interest-free loan to help the community!</p>
                    <a href="{{ url_for('tokenomics.loans') }}" 
                       class="inline-block mt-4 px-4 py-2 bg-cyber-blue text-white rounded hover:bg-cyber-blue/90">
                        Explore Lending
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Token Classifications -->
        {% if user_stats.token_classifications %}
        <div class="glass-card p-6 mt-8">
            <h3 class="text-xl font-semibold mb-6 flex items-center">
                <span class="mr-3">🏷️</span>My Token Classifications
            </h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {% for token in user_stats.token_classifications %}
                <div class="glass-card p-4 text-center">
                    <div class="text-2xl mb-2">
                        {% if token.class_type == 'Avodah' %}⚒️
                        {% elif token.class_type == 'Zedek' %}⚖️
                        {% elif token.class_type == 'Yovel' %}🔄
                        {% elif token.class_type == 'Etzem' %}✨
                        {% else %}🏷️{% endif %}
                    </div>
                    <h4 class="font-semibold">{{ token.symbol }}</h4>
                    <p class="text-sm text-gray-400">{{ token.class_type }}</p>
                    <p class="text-xs text-gray-500 mt-1">{{ token.name }}</p>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
        
        <!-- System Information -->
        <div class="glass-card p-6 mt-8">
            <h3 class="text-xl font-semibold mb-6 flex items-center">
                <span class="mr-3">📊</span>System Information
            </h3>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center">
                    <h4 class="font-semibold text-cyber-cyan">Gleaning Pool</h4>
                    <p class="text-2xl font-bold">{{ "%.2f"|format(system_stats.gleaning_pool_balance) }}</p>
                    <p class="text-sm text-gray-400">ONX Available</p>
                </div>
                
                <div class="text-center">
                    <h4 class="font-semibold text-cyber-purple">Mining Rewards</h4>
                    <p class="text-lg">{{ system_stats.min_reward }} - {{ system_stats.max_reward }} ONX</p>
                    <p class="text-sm text-gray-400">Per Block Range</p>
                </div>
                
                <div class="text-center">
                    <h4 class="font-semibold text-cyber-blue">Active Loans</h4>
                    <p class="text-2xl font-bold">{{ system_stats.total_active_loans }}</p>
                    <p class="text-sm text-gray-400">Community Lending</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Add timestamp formatting
    document.addEventListener('DOMContentLoaded', function() {
        // Format timestamps
        const timestamps = document.querySelectorAll('[data-timestamp]');
        timestamps.forEach(el => {
            const timestamp = parseInt(el.dataset.timestamp);
            if (timestamp) {
                const date = new Date(timestamp * 1000);
                el.textContent = date.toLocaleDateString();
            }
        });
        
        // Add deed score animation
        const scoreCircle = document.querySelector('.deed-score-circle');
        if (scoreCircle) {
            setTimeout(() => {
                scoreCircle.style.transform = 'scale(1.05)';
                setTimeout(() => {
                    scoreCircle.style.transform = 'scale(1)';
                }, 200);
            }, 500);
        }
    });
</script>
{% endblock %}
