#!/usr/bin/env python3
"""
Deploy Genesis Voice Scrolls
Deploys the 3 foundational governance scrolls directly to database
"""

import sys
import os
import json
import time

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from shared.db.db import db

def deploy_genesis_scrolls():
    """Deploy the three Genesis Voice Scrolls."""
    print("📜 DEPLOYING GENESIS VOICE SCROLLS")
    print("=" * 40)
    
    try:
        # Load genesis scrolls configuration
        with open('data/genesis_voice_scrolls.json', 'r', encoding='utf-8') as f:
            scrolls_config = json.load(f)
        
        deployed_count = 0
        
        for scroll in scrolls_config['scrolls']:
            try:
                # Check if scroll already exists
                existing = db.query_one("SELECT scroll_id FROM voice_scrolls WHERE scroll_id = ?", (scroll['scroll_id'],))
                
                if not existing:
                    db.execute("""
                        INSERT INTO voice_scrolls (
                            scroll_id, title, type, proposed_by, content,
                            voting_requirements, status, created_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        scroll['scroll_id'],
                        scroll['title'],
                        scroll['type'],
                        scroll['proposed_by'],
                        json.dumps(scroll['content']),
                        json.dumps(scroll['voting_requirements']),
                        scroll['status'],
                        scroll['proposed_at']
                    ))
                    deployed_count += 1
                    print(f"✅ Deployed: {scroll['title']}")
                else:
                    print(f"⚠️ Already exists: {scroll['title']}")
                    
            except Exception as e:
                print(f"❌ Error deploying {scroll['scroll_id']}: {e}")
        
        print(f"\n📊 DEPLOYMENT SUMMARY:")
        print(f"   📜 Scrolls Deployed: {deployed_count}")
        print(f"   📋 Total Scrolls: {len(scrolls_config['scrolls'])}")
        
        if deployed_count > 0:
            print(f"\n🎉 GENESIS VOICE SCROLLS DEPLOYMENT COMPLETE!")
            return True
        else:
            print(f"\n⚠️ No new scrolls deployed (may already exist)")
            return True  # Still success if they already exist
        
    except Exception as e:
        print(f"❌ Error deploying scrolls: {e}")
        return False

def verify_scroll_deployment():
    """Verify the scrolls were deployed correctly."""
    print(f"\n🔍 Verifying Voice Scroll Deployment...")
    
    try:
        scrolls = db.query("""
            SELECT scroll_id, title, type, status
            FROM voice_scrolls
            ORDER BY created_at ASC
        """)
        
        print(f"📊 Found {len(scrolls)} voice scrolls:")
        for scroll in scrolls:
            print(f"   ✅ {scroll['scroll_id']}: {scroll['title']} ({scroll['status']})")
        
        # Check for the 3 genesis scrolls specifically
        genesis_scrolls = [
            "SCROLL_001_COUNCIL_RATIFICATION",
            "SCROLL_002_GOVERNANCE_QUORUM", 
            "SCROLL_003_BIBLICAL_TOKENOMICS"
        ]
        
        found_genesis = 0
        for scroll_id in genesis_scrolls:
            scroll = db.query_one("SELECT * FROM voice_scrolls WHERE scroll_id = ?", (scroll_id,))
            if scroll:
                found_genesis += 1
        
        print(f"\n📋 Genesis Scrolls Status: {found_genesis}/3")
        
        if found_genesis >= 3:
            print(f"🎉 All Genesis Voice Scrolls verified!")
            return True
        else:
            print(f"⚠️ Missing genesis scrolls: {3 - found_genesis}")
            return False
        
    except Exception as e:
        print(f"❌ Verification error: {e}")
        return False

def main():
    """Main function to deploy and verify genesis scrolls."""
    print("🔥 STEP 10 PHASE 1C: GENESIS VOICE SCROLLS DEPLOYMENT")
    print("=" * 60)
    
    # Deploy scrolls
    if deploy_genesis_scrolls():
        # Verify deployment
        if verify_scroll_deployment():
            print(f"\n🚀 PHASE 1 PREREQUISITES COMPLETE!")
            print(f"✅ Database setup complete")
            print(f"✅ Tribal council formed (12/12 elders)")
            print(f"✅ Genesis voice scrolls deployed")
            print(f"\n🔥 READY FOR PHASE 2: PUBLIC ONBOARDING SYSTEM")
            return True
    
    print(f"\n❌ Genesis voice scrolls deployment incomplete")
    return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
