#!/usr/bin/env python3
"""
Create Eden Mode Tables
Creates the missing tables required for Eden Mode functionality
"""

import sys
import os
import sqlite3
import json

def get_db_connection():
    """Get database connection."""
    db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'shared', 'db', 'db', 'onnyx.db')
    return sqlite3.connect(db_path)

def create_biblical_nations_table():
    """Create and populate the biblical_nations table."""
    print("🏛️ Creating biblical_nations table...")
    
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # Create the table
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS biblical_nations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nation_code TEXT UNIQUE NOT NULL,
            nation_name TEXT NOT NULL,
            tribe_name TEXT,
            nation_type TEXT NOT NULL CHECK (nation_type IN ('covenant', 'witness')),
            description TEXT,
            flag_symbol TEXT,
            ancestral_group TEXT,
            ancestral_description TEXT,
            historical_connection TEXT,
            created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
            updated_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
        )
    """)
    
    # Insert the 12 covenant tribes
    covenant_tribes = [
        ('JU', 'Judah', 'Judah', 'covenant', 'The Royal Tribe - Leadership and Governance', '🦁', None, None, 'Genesis 49:8-12 - The scepter shall not depart from Judah'),
        ('LE', 'Levi', 'Levi', 'covenant', 'The Priestly Tribe - Teaching and Spiritual Leadership', '⚖️', None, None, 'Numbers 3:6-10 - Set apart for service to the Lord'),
        ('EP', 'Ephraim', 'Ephraim', 'covenant', 'The Fruitful Tribe - Abundance and Growth', '🌾', None, None, 'Genesis 48:19 - His seed shall become a multitude of nations'),
        ('BE', 'Benjamin', 'Benjamin', 'covenant', 'Son of the Right Hand - Security and Protection', '🏹', None, None, 'Genesis 49:27 - Benjamin is a ravenous wolf'),
        ('SI', 'Simeon', 'Simeon', 'covenant', 'He Who Hears - Communication and Understanding', '👂', None, None, 'Genesis 49:5-7 - Instruments of cruelty are in their habitations'),
        ('MA', 'Manasseh', 'Manasseh', 'covenant', 'Making to Forget - Healing and Restoration', '🌿', None, None, 'Genesis 48:14 - The younger shall be greater than the elder'),
        ('IS', 'Issachar', 'Issachar', 'covenant', 'There is Recompense - Timing and Seasons', '⏰', None, None, 'Genesis 49:14-15 - A strong donkey lying down between two burdens'),
        ('ZE', 'Zebulun', 'Zebulun', 'covenant', 'Dwelling - Commerce and Trade', '⚓', None, None, 'Genesis 49:13 - Zebulun shall dwell at the haven of the sea'),
        ('NA', 'Naphtali', 'Naphtali', 'covenant', 'My Wrestling - Spiritual Warfare', '🦌', None, None, 'Genesis 49:21 - Naphtali is a hind let loose'),
        ('GA', 'Gad', 'Gad', 'covenant', 'A Troop - Military and Defense', '⚔️', None, None, 'Genesis 49:19 - Gad, a troop shall overcome him'),
        ('AS', 'Asher', 'Asher', 'covenant', 'Happy - Joy and Celebration', '🍯', None, None, 'Genesis 49:20 - Out of Asher his bread shall be fat'),
        ('DA', 'Dan', 'Dan', 'covenant', 'Judge - Justice and Judgment', '⚖️', None, None, 'Genesis 49:16 - Dan shall judge his people')
    ]
    
    for tribe in covenant_tribes:
        cursor.execute("""
            INSERT OR REPLACE INTO biblical_nations 
            (nation_code, nation_name, tribe_name, nation_type, description, flag_symbol, ancestral_group, ancestral_description, historical_connection)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, tribe)
    
    # Insert witness nations organized by ancestral groups
    witness_nations = [
        # Ancient Mesopotamian Roots
        ('IR', 'Iran', None, 'witness', 'Ancient Persia - Land of the Medes and Persians', '🏺', 'Ancient Mesopotamian', 'Descendants of ancient Mesopotamian civilizations', 'Daniel 6:8 - Law of the Medes and Persians'),
        ('IQ', 'Iraq', None, 'witness', 'Ancient Babylon - Cradle of Civilization', '🏛️', 'Ancient Mesopotamian', 'Descendants of ancient Mesopotamian civilizations', 'Daniel 1:1 - Nebuchadnezzar king of Babylon'),
        ('TR', 'Turkey', None, 'witness', 'Ancient Anatolia - Bridge Between Worlds', '🌉', 'Ancient Mesopotamian', 'Descendants of ancient Mesopotamian civilizations', 'Revelation 2:12 - Church in Pergamos'),
        
        # Hamitic Heritage
        ('EG', 'Egypt', None, 'witness', 'Ancient Kemet - Land of the Pharaohs', '🔺', 'Hamitic Heritage', 'Descendants of Ham through Mizraim', 'Genesis 10:6 - The sons of Ham: Cush, Mizraim, Put, and Canaan'),
        ('ET', 'Ethiopia', None, 'witness', 'Ancient Cush - Land of the Burning Faces', '👑', 'Hamitic Heritage', 'Descendants of Ham through Cush', 'Psalm 68:31 - Ethiopia shall soon stretch out her hands unto God'),
        ('LY', 'Libya', None, 'witness', 'Ancient Put - Western Shores', '🏜️', 'Hamitic Heritage', 'Descendants of Ham through Put', 'Ezekiel 30:5 - Ethiopia, and Libya, and Lydia'),
        
        # Semitic Lineage
        ('SA', 'Saudi Arabia', None, 'witness', 'Arabian Peninsula - Land of Ishmael', '🐪', 'Semitic Lineage', 'Descendants of Shem through Abraham', 'Genesis 25:13 - These are the names of the sons of Ishmael'),
        ('JO', 'Jordan', None, 'witness', 'Ancient Edom and Moab - Eastern Neighbors', '🏔️', 'Semitic Lineage', 'Descendants of Shem through Lot and Esau', 'Genesis 36:1 - These are the generations of Esau, who is Edom'),
        ('SY', 'Syria', None, 'witness', 'Ancient Aram - Northern Kingdom', '🌸', 'Semitic Lineage', 'Descendants of Shem through Aram', 'Genesis 10:22 - The children of Shem: Aram'),
        
        # Japhetic Nations
        ('GR', 'Greece', None, 'witness', 'Ancient Hellas - Wisdom and Philosophy', '🏛️', 'Japhetic Nations', 'Descendants of Japheth through Javan', 'Daniel 8:21 - The rough goat is the king of Grecia'),
        ('IT', 'Italy', None, 'witness', 'Ancient Rome - Eternal City', '🏺', 'Japhetic Nations', 'Descendants of Japheth', 'Acts 28:14 - And so we went toward Rome'),
        ('ES', 'Spain', None, 'witness', 'Ancient Tarshish - Western Shores', '⛵', 'Japhetic Nations', 'Descendants of Japheth through Tarshish', 'Jonah 1:3 - To flee unto Tarshish from the presence of the Lord'),
        
        # Celtic and Germanic Heritage
        ('IE', 'Ireland', None, 'witness', 'Emerald Isle - Land of Saints and Scholars', '🍀', 'Celtic Heritage', 'Celtic peoples who preserved ancient wisdom', 'Historical connection to early Christianity'),
        ('GB', 'United Kingdom', None, 'witness', 'British Isles - Maritime Empire', '👑', 'Celtic Heritage', 'Celtic and Anglo-Saxon heritage', 'Historical spread of biblical knowledge'),
        ('DE', 'Germany', None, 'witness', 'Germanic Tribes - Land of Reformation', '🏰', 'Germanic Heritage', 'Germanic peoples', 'Historical role in biblical reformation'),
        
        # Slavic Peoples
        ('RU', 'Russia', None, 'witness', 'Land of the Rus - Eastern Orthodox', '🏔️', 'Slavic Peoples', 'Slavic heritage with Orthodox Christianity', 'Historical preservation of biblical texts'),
        ('PL', 'Poland', None, 'witness', 'Land of the Poles - Catholic Stronghold', '🏰', 'Slavic Peoples', 'Slavic heritage with Catholic tradition', 'Historical biblical scholarship'),
        
        # Nordic Heritage
        ('NO', 'Norway', None, 'witness', 'Land of the Midnight Sun - Viking Heritage', '⛵', 'Nordic Heritage', 'Nordic peoples with maritime tradition', 'Historical exploration and biblical spread'),
        ('SE', 'Sweden', None, 'witness', 'Land of the Swedes - Lutheran Tradition', '🌲', 'Nordic Heritage', 'Nordic peoples with Protestant heritage', 'Historical biblical translation work'),
        
        # Modern Nations
        ('US', 'United States', None, 'witness', 'Land of Liberty - New World Covenant', '🗽', 'Modern Nations', 'Melting pot of all peoples', 'Historical role in biblical missions'),
        ('CA', 'Canada', None, 'witness', 'Great White North - Peaceful Kingdom', '🍁', 'Modern Nations', 'Diverse heritage with peaceful tradition', 'Historical biblical preservation'),
        ('AU', 'Australia', None, 'witness', 'Southern Land - Island Continent', '🦘', 'Modern Nations', 'Diverse heritage in southern hemisphere', 'Historical biblical missions'),
        ('BR', 'Brazil', None, 'witness', 'Land of the Cross - Southern Giant', '✝️', 'Modern Nations', 'Portuguese heritage with Catholic tradition', 'Historical biblical evangelization'),
        ('MX', 'Mexico', None, 'witness', 'Land of the Aztecs - Bridge of Americas', '🌵', 'Modern Nations', 'Indigenous and Spanish heritage', 'Historical biblical syncretism'),
        
        # African Heritage
        ('ZA', 'South Africa', None, 'witness', 'Rainbow Nation - Southern Tip', '🌈', 'African Heritage', 'Diverse African heritage', 'Historical biblical missions'),
        ('NG', 'Nigeria', None, 'witness', 'Giant of Africa - Western Coast', '🦅', 'African Heritage', 'West African heritage', 'Historical biblical evangelization'),
        ('KE', 'Kenya', None, 'witness', 'Land of Safari - Eastern Highlands', '🦁', 'African Heritage', 'East African heritage', 'Historical biblical missions'),
        
        # Asian Heritage
        ('CN', 'China', None, 'witness', 'Middle Kingdom - Ancient Civilization', '🐉', 'Asian Heritage', 'Ancient Chinese civilization', 'Historical biblical translation work'),
        ('IN', 'India', None, 'witness', 'Land of the Ganges - Spiritual Seeking', '🕉️', 'Asian Heritage', 'Ancient Indian civilization', 'Historical biblical missions'),
        ('JP', 'Japan', None, 'witness', 'Land of the Rising Sun - Island Empire', '🌅', 'Asian Heritage', 'Japanese heritage with unique culture', 'Historical biblical adaptation'),
        ('KR', 'South Korea', None, 'witness', 'Land of Morning Calm - Divided Peninsula', '🏔️', 'Asian Heritage', 'Korean heritage with strong Christian presence', 'Modern biblical revival'),
        
        # Pacific Heritage
        ('PH', 'Philippines', None, 'witness', 'Pearl of the Orient - Island Nation', '🏝️', 'Pacific Heritage', 'Austronesian heritage with Catholic tradition', 'Historical biblical evangelization'),
        ('NZ', 'New Zealand', None, 'witness', 'Land of the Long White Cloud - Maori Heritage', '🌿', 'Pacific Heritage', 'Polynesian and European heritage', 'Historical biblical missions'),
        
        # Latin American Heritage
        ('AR', 'Argentina', None, 'witness', 'Land of Silver - Southern Cone', '🥩', 'Latin American', 'Spanish and Italian heritage', 'Historical Catholic biblical tradition'),
        ('CL', 'Chile', None, 'witness', 'Land of Poets - Pacific Coast', '🏔️', 'Latin American', 'Spanish heritage with indigenous roots', 'Historical biblical evangelization'),
        ('PE', 'Peru', None, 'witness', 'Land of the Incas - Andean Heights', '🏔️', 'Latin American', 'Indigenous and Spanish heritage', 'Historical biblical syncretism')
    ]
    
    for nation in witness_nations:
        cursor.execute("""
            INSERT OR REPLACE INTO biblical_nations 
            (nation_code, nation_name, tribe_name, nation_type, description, flag_symbol, ancestral_group, ancestral_description, historical_connection)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, nation)
    
    conn.commit()
    conn.close()
    print(f"✅ Created biblical_nations table with {len(covenant_tribes)} covenant tribes and {len(witness_nations)} witness nations")

def create_tribal_calling_restrictions_table():
    """Create and populate the tribal_calling_restrictions table."""
    print("📜 Creating tribal_calling_restrictions table...")
    
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # Create the table
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS tribal_calling_restrictions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            tribe_code TEXT NOT NULL,
            tribe_name TEXT NOT NULL,
            calling_category TEXT NOT NULL,
            role_name TEXT NOT NULL,
            role_description TEXT,
            biblical_reference TEXT,
            is_exclusive BOOLEAN DEFAULT 0,
            created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
            updated_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
        )
    """)
    
    # Insert tribal calling restrictions based on Genesis 49 prophecies
    tribal_callings = [
        # Judah - Royal and Leadership roles
        ('JU', 'Judah', 'Governance', 'Tribal Elder', 'Leadership of tribal council and governance decisions', 'Genesis 49:10 - The scepter shall not depart from Judah', 1),
        ('JU', 'Judah', 'Governance', 'Council President', 'Presiding over the Council of Twelve Tribes', 'Genesis 49:8 - Your brothers shall praise you', 1),
        ('JU', 'Judah', 'Leadership', 'Executive Leader', 'Executive leadership in covenant organizations', 'Genesis 49:9 - Judah is a lion\'s whelp', 0),
        ('JU', 'Judah', 'Governance', 'Royal Administrator', 'Administrative oversight of covenant affairs', 'Genesis 49:10 - Until Shiloh comes', 0),
        
        # Levi - Priestly and Teaching roles
        ('LE', 'Levi', 'Spiritual', 'High Priest', 'Spiritual leadership and covenant interpretation', 'Numbers 3:6 - Bring the tribe of Levi near', 1),
        ('LE', 'Levi', 'Education', 'Torah Teacher', 'Teaching biblical principles and covenant law', 'Deuteronomy 33:10 - They shall teach Jacob your judgments', 1),
        ('LE', 'Levi', 'Spiritual', 'Covenant Interpreter', 'Interpreting biblical law for modern application', 'Malachi 2:7 - The priest\'s lips should keep knowledge', 0),
        ('LE', 'Levi', 'Education', 'Biblical Scholar', 'Advanced biblical research and scholarship', 'Ezra 7:10 - Ezra had prepared his heart to seek the law', 0),
        
        # Dan - Judgment and Justice roles
        ('DA', 'Dan', 'Justice', 'Chief Judge', 'Presiding over covenant justice and arbitration', 'Genesis 49:16 - Dan shall judge his people', 1),
        ('DA', 'Dan', 'Justice', 'Arbitrator', 'Resolving disputes within the covenant community', 'Genesis 49:16 - As one of the tribes of Israel', 0),
        ('DA', 'Dan', 'Justice', 'Legal Counsel', 'Providing legal guidance based on biblical law', 'Genesis 49:17 - Dan shall be a serpent by the way', 0),
        
        # Benjamin - Security and Protection roles
        ('BE', 'Benjamin', 'Security', 'Security Chief', 'Overseeing covenant community protection', 'Genesis 49:27 - Benjamin shall ravin as a wolf', 1),
        ('BE', 'Benjamin', 'Security', 'Guardian', 'Personal and community security services', 'Deuteronomy 33:12 - The beloved of the Lord shall dwell in safety', 0),
        ('BE', 'Benjamin', 'Military', 'Defense Coordinator', 'Coordinating community defense strategies', 'Judges 20:16 - Left-handed; every one could sling stones', 0),
        
        # Issachar - Timing and Seasons roles
        ('IS', 'Issachar', 'Timing', 'Season Keeper', 'Determining proper timing for covenant activities', '1 Chronicles 12:32 - Men that had understanding of the times', 1),
        ('IS', 'Issachar', 'Planning', 'Strategic Planner', 'Long-term planning and seasonal coordination', 'Genesis 49:14 - Issachar is a strong ass', 0),
        ('IS', 'Issachar', 'Agriculture', 'Harvest Coordinator', 'Coordinating agricultural and economic cycles', 'Genesis 49:15 - He saw that rest was good', 0),
        
        # Other tribes have general roles available but no exclusive restrictions
        # These are examples of roles available to all tribes
        ('EP', 'Ephraim', 'Agriculture', 'Farmer', 'Agricultural production and stewardship', 'Genesis 48:19 - His seed shall become a multitude', 0),
        ('MA', 'Manasseh', 'Healing', 'Healer', 'Physical and emotional healing ministries', 'Genesis 48:14 - The younger shall be greater', 0),
        ('SI', 'Simeon', 'Communication', 'Messenger', 'Communication and information sharing', 'Genesis 49:5 - Simeon and Levi are brethren', 0),
        ('ZE', 'Zebulun', 'Commerce', 'Merchant', 'Trade and commercial activities', 'Genesis 49:13 - Zebulun shall dwell at the haven', 0),
        ('NA', 'Naphtali', 'Arts', 'Artist', 'Creative and artistic expression', 'Genesis 49:21 - Naphtali gives goodly words', 0),
        ('GA', 'Gad', 'Military', 'Warrior', 'Military and defense roles', 'Genesis 49:19 - Gad, a troop shall overcome', 0),
        ('AS', 'Asher', 'Hospitality', 'Host', 'Hospitality and celebration coordination', 'Genesis 49:20 - Out of Asher his bread shall be fat', 0)
    ]
    
    for calling in tribal_callings:
        cursor.execute("""
            INSERT OR REPLACE INTO tribal_calling_restrictions 
            (tribe_code, tribe_name, calling_category, role_name, role_description, biblical_reference, is_exclusive)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, calling)
    
    conn.commit()
    conn.close()
    print(f"✅ Created tribal_calling_restrictions table with {len(tribal_callings)} calling restrictions")

def main():
    """Main function to create all Eden Mode tables."""
    print("🌟 CREATING EDEN MODE TABLES")
    print("=" * 50)
    
    try:
        create_biblical_nations_table()
        create_tribal_calling_restrictions_table()
        
        print(f"\n🎉 EDEN MODE TABLES SUCCESSFULLY CREATED!")
        print("✅ biblical_nations table: 12 covenant tribes + 35 witness nations")
        print("✅ tribal_calling_restrictions table: Biblical role restrictions")
        print("\nEden Mode should now work properly!")
        
    except Exception as e:
        print(f"❌ Error creating Eden Mode tables: {e}")
        return False
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
