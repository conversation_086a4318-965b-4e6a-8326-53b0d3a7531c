#!/usr/bin/env python3
"""
ONNYX Blockchain Verification Script
Comprehensive verification of blockchain state and functionality
"""

import sys
import os
import json
import time

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

def verify_database_schema():
    """Verify database schema and tables."""
    print("🗄️ Verifying Database Schema...")
    
    try:
        from shared.db.db import db
        
        # Check core tables
        core_tables = [
            'identities', 'transactions', 'blocks', 'tokens', 'selas',
            'labor_records', 'mikvah_transactions', 'biblical_nations'
        ]
        
        for table in core_tables:
            try:
                result = db.query_one(f"SELECT COUNT(*) as count FROM {table}")
                count = result['count'] if result else 0
                print(f"  ✅ {table}: {count} records")
            except Exception as e:
                print(f"  ❌ {table}: Error - {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Database verification failed: {e}")
        return False

def verify_blockchain_state():
    """Verify blockchain state and integrity."""
    print("\n📦 Verifying Blockchain State...")
    
    try:
        from shared.db.db import db
        
        # Check blocks
        blocks = db.query("SELECT * FROM blocks ORDER BY block_height ASC LIMIT 5")
        print(f"  📊 Total blocks: {len(blocks)}")
        
        if blocks:
            genesis = blocks[0]
            print(f"  🌱 Genesis block height: {genesis.get('block_height', 'N/A')}")
            print(f"  🔗 Genesis hash: {genesis.get('block_hash', 'N/A')[:16]}...")
            
            # Verify chain integrity
            for i in range(1, len(blocks)):
                current = blocks[i]
                previous = blocks[i-1]
                
                if current['previous_hash'] != previous['block_hash']:
                    print(f"  ❌ Chain integrity broken at block {current['block_height']}")
                    return False
            
            print(f"  ✅ Chain integrity verified for {len(blocks)} blocks")
        
        # Check transactions
        tx_count = db.query_one("SELECT COUNT(*) as count FROM transactions")
        print(f"  📝 Total transactions: {tx_count['count'] if tx_count else 0}")
        
        # Check transaction types
        tx_types = db.query("SELECT op, COUNT(*) as count FROM transactions GROUP BY op")
        for tx_type in tx_types:
            print(f"    - {tx_type['op']}: {tx_type['count']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Blockchain verification failed: {e}")
        return False

def verify_opcodes():
    """Verify opcode implementation."""
    print("\n⚙️ Verifying Opcode Implementation...")
    
    try:
        from blockchain.vm.opcodes import (
            OP_MINT, OP_SEND, OP_IDENTITY, OP_SCROLL, OP_BURN,
            OP_GRANT_REPUTATION, OP_STAKE, OP_VOTE, OP_REWARD,
            OP_JUBILEE, OP_LEND, OP_REPAY, OP_FORGIVE, 
            OP_FIRSTFRUITS, OP_GLEANING_CLAIM
        )
        
        core_opcodes = [
            OP_MINT, OP_SEND, OP_IDENTITY, OP_SCROLL, OP_BURN,
            OP_GRANT_REPUTATION, OP_STAKE, OP_VOTE, OP_REWARD
        ]
        
        biblical_opcodes = [
            OP_JUBILEE, OP_LEND, OP_REPAY, OP_FORGIVE,
            OP_FIRSTFRUITS, OP_GLEANING_CLAIM
        ]
        
        print(f"  ✅ Core opcodes: {len(core_opcodes)} implemented")
        for opcode in core_opcodes:
            print(f"    - {opcode}")
        
        print(f"  ✅ Biblical opcodes: {len(biblical_opcodes)} implemented")
        for opcode in biblical_opcodes:
            print(f"    - {opcode}")
        
        return True
        
    except Exception as e:
        print(f"❌ Opcode verification failed: {e}")
        return False

def verify_biblical_tokenomics():
    """Verify biblical tokenomics implementation."""
    print("\n🪙 Verifying Biblical Tokenomics...")
    
    try:
        from blockchain.tokenomics.biblical_tokenomics import BiblicalTokenomics
        from blockchain.tokenomics.mikvah_tokens import MikvahTokenManager
        from shared.db.db import db
        
        bt = BiblicalTokenomics(db)
        mm = MikvahTokenManager(db)
        
        print(f"  ✅ Current Yovel cycle: {bt.current_yovel_cycle}")
        print(f"  ✅ Current season: {bt.current_season}")
        print(f"  ✅ Sabbath enforcement: {'Active' if bt.is_sabbath_period() else 'Inactive'}")
        
        # Check gleaning pools
        pools = db.query("SELECT * FROM gleaning_pool")
        print(f"  ✅ Gleaning pools: {len(pools)}")
        
        # Check deed tracking
        deeds = db.query("SELECT COUNT(*) as count FROM deeds_ledger")
        deed_count = deeds[0]['count'] if deeds else 0
        print(f"  ✅ Deed records: {deed_count}")
        
        # Check loan system
        loans = db.query("SELECT COUNT(*) as count FROM loans")
        loan_count = loans[0]['count'] if loans else 0
        print(f"  ✅ Anti-usury loans: {loan_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ Biblical tokenomics verification failed: {e}")
        return False

def verify_identity_system():
    """Verify identity and CIPP system."""
    print("\n👤 Verifying Identity & CIPP System...")
    
    try:
        from shared.db.db import db
        
        # Check identities
        identities = db.query("SELECT COUNT(*) as count FROM identities")
        id_count = identities[0]['count'] if identities else 0
        print(f"  ✅ Total identities: {id_count}")
        
        # Check nations
        nations = db.query("SELECT * FROM biblical_nations")
        print(f"  ✅ Biblical nations: {len(nations)}")
        
        # Check verification progress
        verifications = db.query("SELECT COUNT(*) as count FROM verification_progress")
        ver_count = verifications[0]['count'] if verifications else 0
        print(f"  ✅ Verification records: {ver_count}")
        
        # Check covenant acceptances
        covenants = db.query("SELECT COUNT(*) as count FROM covenant_acceptances")
        cov_count = covenants[0]['count'] if covenants else 0
        print(f"  ✅ Covenant acceptances: {cov_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ Identity system verification failed: {e}")
        return False

def verify_sela_system():
    """Verify Sela business system."""
    print("\n🏢 Verifying Sela Business System...")
    
    try:
        from shared.db.db import db
        
        # Check Selas
        selas = db.query("SELECT COUNT(*) as count FROM selas")
        sela_count = selas[0]['count'] if selas else 0
        print(f"  ✅ Registered Selas: {sela_count}")
        
        # Check labor records
        labor = db.query("SELECT COUNT(*) as count FROM labor_records")
        labor_count = labor[0]['count'] if labor else 0
        print(f"  ✅ Labor records: {labor_count}")
        
        # Check labor linked to Selas
        sela_labor = db.query("SELECT COUNT(*) as count FROM labor_records WHERE sela_id IS NOT NULL")
        sela_labor_count = sela_labor[0]['count'] if sela_labor else 0
        print(f"  ✅ Sela-linked labor: {sela_labor_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ Sela system verification failed: {e}")
        return False

def verify_chain_parameters():
    """Verify chain parameters."""
    print("\n⚙️ Verifying Chain Parameters...")
    
    try:
        from shared.config.chain_parameters import chain_parameters
        
        key_params = [
            'block_reward', 'reward_token', 'target_block_time',
            'max_transactions_per_block', 'gleaning_pool_percentage',
            'jubilee_interval_blocks', 'sabbath_start_day'
        ]
        
        for param in key_params:
            value = chain_parameters.get(param)
            print(f"  ✅ {param}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ Chain parameters verification failed: {e}")
        return False

def main():
    """Main verification function."""
    print("🔍 ONNYX BLOCKCHAIN COMPREHENSIVE VERIFICATION")
    print("=" * 60)
    
    results = []
    
    # Run all verifications
    results.append(verify_database_schema())
    results.append(verify_blockchain_state())
    results.append(verify_opcodes())
    results.append(verify_biblical_tokenomics())
    results.append(verify_identity_system())
    results.append(verify_sela_system())
    results.append(verify_chain_parameters())
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 VERIFICATION SUMMARY")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    print(f"✅ Passed: {passed}/{total} ({(passed/total)*100:.1f}%)")
    
    if passed == total:
        print("🎉 ALL VERIFICATIONS PASSED - BLOCKCHAIN IS OPERATIONAL")
    else:
        print("⚠️ SOME VERIFICATIONS FAILED - REVIEW REQUIRED")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
