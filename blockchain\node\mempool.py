"""
Onnyx Mempool Module

This module provides the Mempool class for managing pending transactions.
"""

import os
import json
import time
from typing import Dict, List, Any, Optional

class Mempool:
    """
    Mempool manages pending transactions that are waiting to be included in a block.
    """
    
    def __init__(self, mempool_file: str = "mempool.json"):
        """
        Initialize the Mempool.
        
        Args:
            mempool_file: Path to the mempool JSON file
        """
        self.mempool_file = mempool_file
        self.pool = self._load()
    
    def _load(self) -> Dict[str, Any]:
        """
        Load the mempool from the JSON file.
        
        Returns:
            The mempool as a dictionary of transactions
        """
        if os.path.exists(self.mempool_file):
            try:
                with open(self.mempool_file, 'r') as f:
                    return json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                return {}
        return {}
    
    def _save(self) -> None:
        """Save the mempool to the JSON file."""
        with open(self.mempool_file, 'w') as f:
            json.dump(self.pool, f, indent=2)
    
    def add_transaction(self, tx: Dict[str, Any]) -> bool:
        """
        Add a transaction to the mempool.
        
        Args:
            tx: The transaction to add
        
        Returns:
            True if the transaction was added, False otherwise
        """
        # Ensure the transaction has a txid
        if "txid" not in tx:
            return False
        
        # Add the transaction to the pool
        self.pool[tx["txid"]] = tx
        self._save()
        
        return True
    
    def get_transaction(self, txid: str) -> Optional[Dict[str, Any]]:
        """
        Get a transaction from the mempool.
        
        Args:
            txid: The transaction ID
        
        Returns:
            The transaction or None if not found
        """
        return self.pool.get(txid)
    
    def remove_transaction(self, txid: str) -> bool:
        """
        Remove a transaction from the mempool.
        
        Args:
            txid: The transaction ID
        
        Returns:
            True if the transaction was removed, False otherwise
        """
        if txid in self.pool:
            del self.pool[txid]
            self._save()
            return True
        return False
    
    def get_all(self) -> List[Dict[str, Any]]:
        """
        Get all transactions in the mempool.
        
        Returns:
            All transactions in the mempool
        """
        return list(self.pool.values())
    
    def get_count(self) -> int:
        """
        Get the number of transactions in the mempool.
        
        Returns:
            The number of transactions in the mempool
        """
        return len(self.pool)
    
    def clear(self) -> None:
        """Clear all transactions from the mempool."""
        self.pool = {}
        self._save()
    
    def get_transactions_by_sender(self, sender: str) -> List[Dict[str, Any]]:
        """
        Get all transactions from a specific sender.
        
        Args:
            sender: The sender's identity ID
        
        Returns:
            All transactions from the sender
        """
        return [tx for tx in self.pool.values() if tx.get("from") == sender]
    
    def get_transactions_by_type(self, tx_type: str) -> List[Dict[str, Any]]:
        """
        Get all transactions of a specific type.
        
        Args:
            tx_type: The transaction type
        
        Returns:
            All transactions of the specified type
        """
        return [tx for tx in self.pool.values() if tx.get("type") == tx_type]
    
    def get_oldest_transactions(self, count: int) -> List[Dict[str, Any]]:
        """
        Get the oldest transactions in the mempool.
        
        Args:
            count: The number of transactions to get
        
        Returns:
            The oldest transactions
        """
        # Sort transactions by timestamp
        sorted_txs = sorted(
            self.pool.values(),
            key=lambda tx: tx.get("timestamp", 0)
        )
        
        return sorted_txs[:count]
    
    def prune_old_transactions(self, max_age_seconds: int) -> int:
        """
        Remove transactions older than a certain age.
        
        Args:
            max_age_seconds: The maximum age in seconds
        
        Returns:
            The number of transactions removed
        """
        current_time = int(time.time())
        txids_to_remove = []
        
        for txid, tx in self.pool.items():
            tx_time = tx.get("timestamp", 0)
            if current_time - tx_time > max_age_seconds:
                txids_to_remove.append(txid)
        
        for txid in txids_to_remove:
            del self.pool[txid]
        
        if txids_to_remove:
            self._save()
        
        return len(txids_to_remove)
