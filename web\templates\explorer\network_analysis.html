{% extends "base.html" %}

{% block title %}P2P Network Analysis - ONNYX Platform{% endblock %}

{% block content %}
<div class="network-analysis-content hero-gradient cyber-grid relative py-8">
    <!-- Floating particles -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute top-1/4 left-1/4 w-2 h-2 bg-cyber-cyan rounded-full animate-ping opacity-70"></div>
        <div class="absolute top-1/3 right-1/3 w-1 h-1 bg-cyber-purple rounded-full animate-pulse opacity-60"></div>
        <div class="absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-cyber-blue rounded-full animate-bounce opacity-50"></div>
    </div>

    <div class="container-xl px-4 relative z-10">
        <!-- Header Section -->
        <div class="text-center mb-12">
            <div class="mb-8 flex justify-center">
                <div class="w-16 h-16 md:w-20 md:h-20 rounded-2xl flex items-center justify-center shadow-cyber bg-glass-bg backdrop-blur-lg border border-glass-border hover:shadow-cyber-cyan/50 transition-normal group">
                    <i class="fas fa-network-wired text-3xl md:text-4xl text-cyber-cyan group-hover:scale-110 transition-normal"></i>
                </div>
            </div>

            <h1 class="text-5xl md:text-7xl font-orbitron font-bold mb-6">
                <span class="hologram-text">P2P Network Analysis</span>
            </h1>
            <p class="text-xl md:text-2xl text-secondary container-lg mx-auto leading-relaxed mb-8">
                Real-time monitoring and analysis of the ONNYX covenant blockchain P2P network
            </p>

            <!-- Live Network Status -->
            <div class="flex items-center justify-center space-x-6 mb-8">
                <div class="flex items-center">
                    <div id="network-status-indicator" class="w-4 h-4 bg-green-500 rounded-full animate-pulse mr-3"></div>
                    <span class="text-lg text-gray-300">Network: <span id="network-status-text" class="text-cyber-cyan font-orbitron font-bold">Connected</span></span>
                </div>
                <div class="flex items-center">
                    <div id="consensus-status-indicator" class="w-4 h-4 bg-green-500 rounded-full mr-3"></div>
                    <span class="text-lg text-gray-300">Consensus: <span id="consensus-status-text" class="text-cyber-green font-orbitron font-bold">Healthy</span></span>
                </div>
                <div class="text-lg text-gray-400">
                    Last Updated: <span id="last-updated" class="text-cyber-purple font-orbitron">--</span>
                </div>
            </div>
        </div>

        <!-- Network Topology Visualization -->
        <div class="glass-card mb-8">
            <div class="card-header">
                <div class="flex items-center justify-between">
                    <div>
                        <h2 class="card-title">🌐 Network Topology</h2>
                        <p class="card-subtitle">Visual representation of P2P network structure</p>
                    </div>
                    <button onclick="refreshTopology()" class="glass-button-sm px-4 py-2">
                        <i class="fas fa-sync-alt mr-2"></i> Refresh
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- Network Diagram -->
                    <div class="lg:col-span-2">
                        <div id="network-topology" class="h-96 bg-gradient-to-br from-cyber-cyan/5 to-cyber-purple/5 rounded-2xl border border-glass-border relative overflow-hidden">
                            <!-- Network visualization will be rendered here -->
                            <div class="absolute inset-0 flex items-center justify-center">
                                <div class="text-center">
                                    <div class="w-16 h-16 bg-gradient-to-br from-cyber-cyan to-cyber-purple rounded-2xl flex items-center justify-center mx-auto mb-4">
                                        <i class="fas fa-project-diagram text-2xl text-onyx-black"></i>
                                    </div>
                                    <p class="text-muted font-orbitron">Network Topology Visualization</p>
                                    <p class="text-sm text-tertiary">Interactive diagram coming soon</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Network Statistics -->
                    <div class="space-y-4">
                        <div class="glass-card p-4">
                            <div class="text-sm text-muted mb-2">Total Nodes</div>
                            <div class="text-3xl font-orbitron font-bold text-cyber-cyan" id="total-nodes">18</div>
                        </div>
                        
                        <div class="glass-card p-4">
                            <div class="text-sm text-muted mb-2">Active Connections</div>
                            <div class="text-3xl font-orbitron font-bold text-cyber-purple" id="active-connections">156</div>
                        </div>
                        
                        <div class="glass-card p-4">
                            <div class="text-sm text-muted mb-2">Average Latency</div>
                            <div class="text-3xl font-orbitron font-bold text-cyber-blue" id="avg-latency">45ms</div>
                        </div>
                        
                        <div class="glass-card p-4">
                            <div class="text-sm text-muted mb-2">Network Uptime</div>
                            <div class="text-3xl font-orbitron font-bold text-cyber-green" id="network-uptime">99.9%</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Node Types Analysis -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <!-- Bootstrap & Discovery -->
            <div class="glass-card">
                <div class="card-header">
                    <h2 class="card-title">📡 Bootstrap & Discovery</h2>
                    <p class="card-subtitle">Network discovery and peer registration</p>
                </div>
                <div class="card-body">
                    <div class="space-y-4">
                        <div class="flex items-center justify-between p-4 bg-glass-bg rounded-lg border border-glass-border">
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-cyber-cyan rounded-full mr-3"></div>
                                <span class="font-orbitron font-bold">Bootstrap Node</span>
                            </div>
                            <div class="text-right">
                                <div class="text-lg font-orbitron font-bold text-cyber-cyan" id="bootstrap-peers">18</div>
                                <div class="text-xs text-muted">registered peers</div>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-2 gap-4">
                            <div class="text-center p-3 bg-glass-bg rounded-lg border border-glass-border">
                                <div class="text-xl font-orbitron font-bold text-cyber-purple" id="discovery-rate">12/min</div>
                                <div class="text-xs text-muted">Discovery Rate</div>
                            </div>
                            <div class="text-center p-3 bg-glass-bg rounded-lg border border-glass-border">
                                <div class="text-xl font-orbitron font-bold text-cyber-blue" id="peer-churn">2%</div>
                                <div class="text-xs text-muted">Peer Churn</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tribal Elder Validators -->
            <div class="glass-card">
                <div class="card-header">
                    <h2 class="card-title">👑 Tribal Elder Validators</h2>
                    <p class="card-subtitle">Covenant governance and validation</p>
                </div>
                <div class="card-body">
                    <div class="space-y-4">
                        <div class="grid grid-cols-3 gap-3 mb-4">
                            <div class="text-center p-3 bg-glass-bg rounded-lg border border-glass-border">
                                <div class="text-lg font-orbitron font-bold text-cyber-cyan" id="major-tribes">3</div>
                                <div class="text-xs text-muted">Major Tribes (2x)</div>
                            </div>
                            <div class="text-center p-3 bg-glass-bg rounded-lg border border-glass-border">
                                <div class="text-lg font-orbitron font-bold text-cyber-purple" id="standard-tribes">9</div>
                                <div class="text-xs text-muted">Standard Tribes</div>
                            </div>
                            <div class="text-center p-3 bg-glass-bg rounded-lg border border-glass-border">
                                <div class="text-lg font-orbitron font-bold text-cyber-green" id="voting-power">15</div>
                                <div class="text-xs text-muted">Total Votes</div>
                            </div>
                        </div>
                        
                        <div class="space-y-2" id="tribal-elder-list">
                            <!-- Will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mining Network Analysis -->
        <div class="glass-card mb-8">
            <div class="card-header">
                <h2 class="card-title">⛏️ Mining Network Analysis</h2>
                <p class="card-subtitle">P2P mining coordination and proposal tracking</p>
            </div>
            <div class="card-body">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                    <div class="glass-card p-4 text-center">
                        <div class="text-2xl font-orbitron font-bold text-cyber-cyan mb-2" id="active-miners">5</div>
                        <div class="text-sm text-tertiary">Active Miners</div>
                    </div>
                    
                    <div class="glass-card p-4 text-center">
                        <div class="text-2xl font-orbitron font-bold text-cyber-purple mb-2" id="mining-proposals">12</div>
                        <div class="text-sm text-tertiary">Proposals (24h)</div>
                    </div>
                    
                    <div class="glass-card p-4 text-center">
                        <div class="text-2xl font-orbitron font-bold text-cyber-blue mb-2" id="proposal-success">83%</div>
                        <div class="text-sm text-tertiary">Success Rate</div>
                    </div>
                    
                    <div class="glass-card p-4 text-center">
                        <div class="text-2xl font-orbitron font-bold text-cyber-green mb-2" id="avg-proposal-time">45s</div>
                        <div class="text-sm text-tertiary">Avg Proposal Time</div>
                    </div>
                </div>

                <!-- Recent Mining Activity -->
                <div>
                    <h3 class="text-lg font-orbitron font-bold text-primary mb-4">Recent Mining Activity</h3>
                    <div class="space-y-3" id="mining-activity-list">
                        <!-- Will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Biblical Compliance Dashboard -->
        <div class="glass-card mb-8">
            <div class="card-header">
                <h2 class="card-title">📜 Biblical Compliance Dashboard</h2>
                <p class="card-subtitle">Real-time covenant enforcement monitoring</p>
            </div>
            <div class="card-body">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <!-- Anti-Usury Enforcement -->
                    <div class="glass-card p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="font-orbitron font-bold text-cyber-cyan">Anti-Usury</h3>
                            <i class="fas fa-shield-alt text-cyber-cyan"></i>
                        </div>
                        <div class="text-3xl font-orbitron font-bold text-cyber-green mb-2" id="anti-usury-compliance">100%</div>
                        <div class="text-sm text-muted mb-3">Compliance Rate</div>
                        <div class="w-full bg-gray-700 rounded-full h-2">
                            <div class="bg-cyber-green h-2 rounded-full" style="width: 100%" id="anti-usury-progress"></div>
                        </div>
                        <div class="text-xs text-tertiary mt-2">
                            <span id="usury-violations">0</span> violations detected
                        </div>
                    </div>

                    <!-- Sabbath Observance -->
                    <div class="glass-card p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="font-orbitron font-bold text-cyber-purple">Sabbath</h3>
                            <i class="fas fa-moon text-cyber-purple"></i>
                        </div>
                        <div class="text-3xl font-orbitron font-bold text-cyber-green mb-2" id="sabbath-compliance">100%</div>
                        <div class="text-sm text-muted mb-3">Observance Rate</div>
                        <div class="w-full bg-gray-700 rounded-full h-2">
                            <div class="bg-cyber-green h-2 rounded-full" style="width: 100%" id="sabbath-progress"></div>
                        </div>
                        <div class="text-xs text-tertiary mt-2">
                            Next Sabbath: <span id="next-sabbath">Friday 6PM</span>
                        </div>
                    </div>

                    <!-- Gleaning Pools -->
                    <div class="glass-card p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="font-orbitron font-bold text-cyber-blue">Gleaning</h3>
                            <i class="fas fa-seedling text-cyber-blue"></i>
                        </div>
                        <div class="text-3xl font-orbitron font-bold text-cyber-cyan mb-2" id="gleaning-participation">95%</div>
                        <div class="text-sm text-muted mb-3">Participation</div>
                        <div class="w-full bg-gray-700 rounded-full h-2">
                            <div class="bg-cyber-cyan h-2 rounded-full" style="width: 95%" id="gleaning-progress"></div>
                        </div>
                        <div class="text-xs text-tertiary mt-2">
                            Pool Balance: <span id="gleaning-balance">234.75 ONX</span>
                        </div>
                    </div>

                    <!-- Tribal Governance -->
                    <div class="glass-card p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="font-orbitron font-bold text-cyber-green">Governance</h3>
                            <i class="fas fa-gavel text-cyber-green"></i>
                        </div>
                        <div class="text-3xl font-orbitron font-bold text-cyber-purple mb-2" id="governance-health">92%</div>
                        <div class="text-sm text-muted mb-3">Health Score</div>
                        <div class="w-full bg-gray-700 rounded-full h-2">
                            <div class="bg-cyber-purple h-2 rounded-full" style="width: 92%" id="governance-progress"></div>
                        </div>
                        <div class="text-xs text-tertiary mt-2">
                            <span id="active-proposals-count">3</span> active proposals
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="text-center">
            <h2 class="text-2xl font-orbitron font-bold text-primary mb-8">Network Management</h2>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ url_for('explorer.explorer_index') }}"
                   class="glass-button px-8 py-4 rounded-lg font-orbitron font-bold transition-normal hover:scale-105">
                    🔍 Blockchain Explorer
                </a>
                <a href="{{ url_for('network.network_status') }}"
                   class="glass-button px-8 py-4 rounded-lg font-orbitron font-bold transition-normal hover:scale-105">
                    📊 Network Status
                </a>
                <button onclick="exportNetworkData()"
                        class="glass-button px-8 py-4 rounded-lg font-orbitron font-bold transition-normal hover:scale-105">
                    📥 Export Data
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Network Analysis JavaScript will be added in the next section
let analysisUpdateInterval;

document.addEventListener('DOMContentLoaded', function() {
    loadNetworkAnalysis();
    startAnalysisMonitoring();
});

async function loadNetworkAnalysis() {
    try {
        const response = await fetch('/explorer/api/network-stats');
        const data = await response.json();
        
        if (data.success) {
            updateNetworkAnalysis(data);
        }
    } catch (error) {
        console.error('Failed to load network analysis:', error);
    }
}

function updateNetworkAnalysis(data) {
    // Update network statistics
    if (data.network) {
        document.getElementById('total-nodes').textContent = data.network.total_nodes || 18;
        document.getElementById('active-connections').textContent = data.network.active_connections || 156;
        document.getElementById('avg-latency').textContent = (data.network.average_latency || 45) + 'ms';
        document.getElementById('network-uptime').textContent = ((data.network.network_uptime || 0.999) * 100).toFixed(1) + '%';
    }
    
    // Update last updated time
    document.getElementById('last-updated').textContent = new Date().toLocaleTimeString();
}

function startAnalysisMonitoring() {
    analysisUpdateInterval = setInterval(loadNetworkAnalysis, 15000);
}

async function refreshTopology() {
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Refreshing...';
    button.disabled = true;
    
    await loadNetworkAnalysis();
    
    setTimeout(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    }, 1000);
}

function exportNetworkData() {
    // Placeholder for network data export functionality
    alert('Network data export functionality coming soon!');
}

window.addEventListener('beforeunload', function() {
    if (analysisUpdateInterval) {
        clearInterval(analysisUpdateInterval);
    }
});
</script>
{% endblock %}
