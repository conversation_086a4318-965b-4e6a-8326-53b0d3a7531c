/* ONNYX Platform - Futuristic Cyberpunk Design System */
/* Next-Generation Enterprise Interface with Advanced Glass Morphism */

/* ===== ONNYX PRECISION DESIGN SYSTEM V2.0 ===== */
/* Mathematical Foundation with Enhanced Cyberpunk Aesthetics */
:root {
    /* ===== PRECISION BASE UNIT SYSTEM ===== */
    /* All measurements based on 8px grid for perfect alignment */
    --base-unit: 8px;              /* Foundation unit for all measurements */
    --base-2: 16px;                /* 2 * base-unit */
    --base-3: 24px;                /* 3 * base-unit */
    --base-4: 32px;                /* 4 * base-unit */
    --base-5: 40px;                /* 5 * base-unit */
    --base-6: 48px;                /* 6 * base-unit */
    --base-8: 64px;                /* 8 * base-unit */
    --base-10: 80px;               /* 10 * base-unit */
    --base-12: 96px;               /* 12 * base-unit */
    --base-16: 128px;              /* 16 * base-unit */
    --base-20: 160px;              /* 20 * base-unit */
    --base-24: 192px;              /* 24 * base-unit */

    /* ===== ENHANCED CORE COLORS ===== */
    --onyx-black: #0f0f0f;         /* Deeper primary background */
    --onyx-gray: #1a1a1a;          /* Enhanced secondary background */
    --onyx-light: #2a2a2a;         /* Refined tertiary background */
    --onyx-lighter: #3a3a3a;       /* Elevated quaternary background */
    --onyx-accent: #4a4a4a;        /* New accent background */

    /* ===== ADVANCED CYBER THEME COLORS ===== */
    --cyber-cyan: #00fff7;         /* Enhanced primary cyan with more vibrancy */
    --cyber-cyan-bright: #33fffa;  /* Brighter cyan for highlights */
    --cyber-cyan-dark: #00cccc;    /* Darker cyan for depth */
    --cyber-purple: #9a00ff;       /* Intensified purple accent */
    --cyber-purple-bright: #b333ff; /* Brighter purple for highlights */
    --cyber-purple-dark: #7700cc;  /* Darker purple for depth */
    --cyber-blue: #0080ff;         /* Enhanced blue accent */
    --cyber-blue-bright: #3399ff;  /* Brighter blue for highlights */
    --cyber-blue-dark: #0066cc;    /* Darker blue for depth */
    --cyber-green: #00ff88;        /* Enhanced success green */
    --cyber-red: #ff3366;          /* Enhanced error red */
    --cyber-yellow: #ffcc00;       /* Enhanced warning yellow */
    --cyber-orange: #ff6600;       /* New accent orange */

    /* ===== ADVANCED GLASS MORPHISM SYSTEM ===== */
    --glass-bg-light: rgba(255, 255, 255, 0.05);   /* Ultra-light glass */
    --glass-bg: rgba(255, 255, 255, 0.08);         /* Standard glass */
    --glass-bg-medium: rgba(255, 255, 255, 0.12);  /* Medium glass */
    --glass-bg-heavy: rgba(255, 255, 255, 0.16);   /* Heavy glass */
    --glass-border-light: rgba(255, 255, 255, 0.08); /* Light border */
    --glass-border: rgba(255, 255, 255, 0.16);     /* Standard border */
    --glass-border-heavy: rgba(255, 255, 255, 0.24); /* Heavy border */
    --glass-shadow: rgba(0, 255, 247, 0.15);       /* Enhanced shadow */
    --glass-hover: rgba(255, 255, 255, 0.15);      /* Enhanced hover */
    --glass-active: rgba(255, 255, 255, 0.20);     /* Active state */

    /* ===== ENHANCED GLASS MORPHISM VARIANTS ===== */
    --glass-card-bg: rgba(255, 255, 255, 0.08);
    --glass-card-border: rgba(255, 255, 255, 0.16);
    --glass-card-premium-bg: rgba(255, 255, 255, 0.12);
    --glass-card-premium-border: rgba(255, 255, 255, 0.24);
    --glass-card-enhanced-bg: rgba(255, 255, 255, 0.10);
    --glass-card-enhanced-border: rgba(255, 255, 255, 0.20);

    /* ===== ENHANCED TEXT COLORS ===== */
    --text-primary: #ffffff;       /* Pure white for maximum contrast */
    --text-secondary: #f0f0f0;     /* Enhanced secondary text */
    --text-tertiary: #cccccc;      /* Improved tertiary text */
    --text-muted: #999999;         /* Refined muted text */
    --text-disabled: #666666;      /* New disabled text color */

    /* ===== MATHEMATICAL TYPOGRAPHY SCALE ===== */
    /* Based on 1.25 ratio (Major Third) for harmonic progression */
    --font-size-xs: 0.75rem;       /* 12px - base */
    --font-size-sm: 0.875rem;      /* 14px - base * 1.167 */
    --font-size-base: 1rem;        /* 16px - base * 1.333 */
    --font-size-lg: 1.125rem;      /* 18px - base * 1.5 */
    --font-size-xl: 1.25rem;       /* 20px - base * 1.667 */
    --font-size-2xl: 1.5rem;       /* 24px - base * 2 */
    --font-size-3xl: 1.875rem;     /* 30px - base * 2.5 */
    --font-size-4xl: 2.25rem;      /* 36px - base * 3 */
    --font-size-5xl: 3rem;         /* 48px - base * 4 */
    --font-size-6xl: 3.75rem;      /* 60px - base * 5 */
    --font-size-7xl: 4.5rem;       /* 72px - base * 6 */

    /* ===== PRECISE SPACING SCALE ===== */
    /* All values are multiples of 8px for perfect grid alignment */
    --space-0: 0;                  /* 0px */
    --space-1: var(--base-unit);   /* 8px */
    --space-2: var(--base-2);      /* 16px */
    --space-3: var(--base-3);      /* 24px */
    --space-4: var(--base-4);      /* 32px */
    --space-5: var(--base-5);      /* 40px */
    --space-6: var(--base-6);      /* 48px */
    --space-8: var(--base-8);      /* 64px */
    --space-10: var(--base-10);    /* 80px */
    --space-12: var(--base-12);    /* 96px */
    --space-16: var(--base-16);    /* 128px */
    --space-20: var(--base-20);    /* 160px */
    --space-24: var(--base-24);    /* 192px */

    /* ===== MATHEMATICAL BORDER RADIUS SCALE ===== */
    /* Based on 4px increments for consistent curvature */
    --radius-none: 0;              /* 0px - sharp corners */
    --radius-sm: 4px;              /* 4px - subtle rounding */
    --radius-md: var(--base-unit); /* 8px - standard rounding */
    --radius-lg: 12px;             /* 12px - pronounced rounding */
    --radius-xl: var(--base-2);    /* 16px - large rounding */
    --radius-2xl: var(--base-3);   /* 24px - extra large rounding */
    --radius-3xl: var(--base-4);   /* 32px - maximum rounding */
    --radius-full: 9999px;         /* Full circle/pill shape */

    /* ===== PRECISE SHADOW SYSTEM ===== */
    /* Mathematically calculated for consistent depth perception */
    --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.2);
    --shadow-xl: 0 16px 32px rgba(0, 0, 0, 0.25);
    --shadow-2xl: 0 24px 48px rgba(0, 0, 0, 0.3);
    --shadow-cyber: 0 0 var(--base-3) rgba(0, 212, 255, 0.3);
    --shadow-purple: 0 0 var(--base-3) rgba(139, 92, 246, 0.3);

    /* ===== PRECISE ANIMATION TIMING ===== */
    /* Based on natural motion curves */
    --transition-instant: 100ms ease-out;
    --transition-fast: 200ms ease-out;
    --transition-normal: 300ms ease-out;
    --transition-slow: 500ms ease-out;
    --transition-slower: 700ms ease-out;

    /* ===== LAYOUT PRECISION VARIABLES ===== */
    --container-xs: 480px;         /* Extra small container */
    --container-sm: 640px;         /* Small container */
    --container-md: 768px;         /* Medium container */
    --container-lg: 1024px;        /* Large container */
    --container-xl: 1280px;        /* Extra large container */
    --container-2xl: 1536px;       /* 2X large container */

    /* ===== GRID SYSTEM PRECISION ===== */
    --grid-columns: 12;            /* Standard 12-column grid */
    --grid-gap: var(--base-3);     /* 24px gap between grid items */
    --grid-gap-sm: var(--base-2);  /* 16px gap for smaller screens */
    --grid-gap-lg: var(--base-4);  /* 32px gap for larger screens */

    /* ===== COMPONENT PRECISION MEASUREMENTS ===== */
    --nav-height: var(--base-8);   /* 64px navigation height */
    --button-height: 44px;         /* Minimum touch target */
    --input-height: 44px;          /* Consistent input height */
    --card-padding: var(--base-3); /* 24px standard card padding */
    --section-padding: var(--base-8); /* 64px section padding */
}

/* ===== ADVANCED ANIMATION SYSTEM ===== */
/* Sophisticated micro-interactions and cyberpunk effects */

/* Core entrance animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(var(--base-3)) scale(0.95);
        filter: blur(4px);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
        filter: blur(0);
    }
}

@keyframes slideIn {
    from {
        transform: translateX(-100%) scale(0.9);
        opacity: 0;
        filter: blur(2px);
    }
    to {
        transform: translateX(0) scale(1);
        opacity: 1;
        filter: blur(0);
    }
}

@keyframes slideUp {
    from {
        transform: translateY(var(--base-6)) scale(0.95);
        opacity: 0;
    }
    to {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

/* Enhanced glow effects */
@keyframes cyberGlow {
    0%, 100% {
        box-shadow:
            0 0 var(--base-3) rgba(0, 255, 247, 0.4),
            0 0 var(--base-6) rgba(0, 255, 247, 0.2),
            0 0 var(--base-8) rgba(0, 255, 247, 0.1);
        filter: brightness(1);
    }
    50% {
        box-shadow:
            0 0 var(--base-4) rgba(0, 255, 247, 0.6),
            0 0 var(--base-8) rgba(0, 255, 247, 0.3),
            0 0 var(--base-12) rgba(0, 255, 247, 0.15);
        filter: brightness(1.1);
    }
}

@keyframes purpleGlow {
    0%, 100% {
        box-shadow:
            0 0 var(--base-3) rgba(154, 0, 255, 0.4),
            0 0 var(--base-6) rgba(154, 0, 255, 0.2),
            0 0 var(--base-8) rgba(154, 0, 255, 0.1);
        filter: brightness(1);
    }
    50% {
        box-shadow:
            0 0 var(--base-4) rgba(154, 0, 255, 0.6),
            0 0 var(--base-8) rgba(154, 0, 255, 0.3),
            0 0 var(--base-12) rgba(154, 0, 255, 0.15);
        filter: brightness(1.1);
    }
}

@keyframes blueGlow {
    0%, 100% {
        box-shadow:
            0 0 var(--base-3) rgba(0, 128, 255, 0.4),
            0 0 var(--base-6) rgba(0, 128, 255, 0.2),
            0 0 var(--base-8) rgba(0, 128, 255, 0.1);
    }
    50% {
        box-shadow:
            0 0 var(--base-4) rgba(0, 128, 255, 0.6),
            0 0 var(--base-8) rgba(0, 128, 255, 0.3),
            0 0 var(--base-12) rgba(0, 128, 255, 0.15);
    }
}

/* Floating and movement animations */
@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    33% {
        transform: translateY(-var(--base-unit)) rotate(1deg);
    }
    66% {
        transform: translateY(-var(--base-2)) rotate(-1deg);
    }
}

@keyframes gentleFloat {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-var(--base-1));
    }
}

/* Advanced shimmer effects */
@keyframes shimmer {
    0% {
        background-position: -200% 0;
        opacity: 0.3;
    }
    50% {
        opacity: 0.8;
    }
    100% {
        background-position: 200% 0;
        opacity: 0.3;
    }
}

@keyframes dataFlow {
    0% {
        background-position: -100% 0;
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        background-position: 100% 0;
        opacity: 0;
    }
}

/* Counter and scale animations */
@keyframes countUp {
    from {
        opacity: 0;
        transform: scale(0.5) rotate(-5deg);
        filter: blur(2px);
    }
    to {
        opacity: 1;
        transform: scale(1) rotate(0deg);
        filter: blur(0);
    }
}

@keyframes scaleIn {
    from {
        transform: scale(0.8);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

/* Gradient and color animations */
@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
        filter: hue-rotate(0deg);
    }
    25% {
        background-position: 100% 50%;
        filter: hue-rotate(90deg);
    }
    50% {
        background-position: 100% 50%;
        filter: hue-rotate(180deg);
    }
    75% {
        background-position: 0% 50%;
        filter: hue-rotate(270deg);
    }
    100% {
        background-position: 0% 50%;
        filter: hue-rotate(360deg);
    }
}

@keyframes colorCycle {
    0% { color: var(--cyber-cyan); }
    25% { color: var(--cyber-purple); }
    50% { color: var(--cyber-blue); }
    75% { color: var(--cyber-green); }
    100% { color: var(--cyber-cyan); }
}

@keyframes logoGlow {
    0%, 100% {
        filter: brightness(0) saturate(100%) invert(64%) sepia(100%) saturate(2000%) hue-rotate(180deg) brightness(100%) contrast(100%);
        box-shadow: 0 0 20px rgba(0, 255, 247, 0.3);
    }
    50% {
        filter: brightness(0) saturate(100%) invert(64%) sepia(100%) saturate(2000%) hue-rotate(180deg) brightness(120%) contrast(110%);
        box-shadow: 0 0 30px rgba(0, 255, 247, 0.5);
    }
}

@keyframes logoSpin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes logoPulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.1); opacity: 0.8; }
}

/* Utility classes */
.fade-in {
    animation: fadeIn 0.8s ease-out;
}

.slide-in {
    animation: slideIn 0.5s ease-out;
}

.glow-effect {
    animation: glow 2s ease-in-out infinite;
}

.purple-glow-effect {
    animation: purpleGlow 2s ease-in-out infinite;
}

.float-effect {
    animation: float 3s ease-in-out infinite;
}

/* Enhanced text utility classes */
.text-primary-bright {
    color: var(--text-primary) !important;
}

.text-secondary-bright {
    color: var(--text-secondary) !important;
}

.text-tertiary-bright {
    color: var(--text-tertiary) !important;
}

.text-muted-bright {
    color: var(--text-muted) !important;
}

/* Enhanced accent colors with glow */
.text-cyber-cyan-glow {
    color: var(--cyber-cyan);
    text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.text-cyber-purple-glow {
    color: var(--cyber-purple);
    text-shadow: 0 0 10px rgba(139, 92, 246, 0.5);
}

.text-cyber-blue-glow {
    color: var(--cyber-blue);
    text-shadow: 0 0 10px rgba(0, 102, 255, 0.5);
}

/* SECTION TITLE CONSISTENCY - PART 1 SOLUTION */
.section-title {
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    color: var(--section-header-color);
    text-shadow: 0 0 15px rgba(0, 212, 255, 0.6);
    margin-top: var(--section-spacing-top);
    margin-bottom: var(--section-spacing-bottom);
    letter-spacing: 0.05em;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -1rem;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--cyber-cyan), transparent);
    box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

/* Section spacing standardization */
.section-container {
    margin-top: var(--section-spacing-top);
    margin-bottom: var(--section-spacing-bottom);
}

.section-content-buffer {
    margin-top: 2rem;
    margin-bottom: 2rem;
}

.shimmer-effect {
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
}

.count-up {
    animation: countUp 0.6s ease-out;
}

/* Logo-specific utility classes */
.logo-glow {
    animation: logoGlow 3s ease-in-out infinite;
}

.logo-spin {
    animation: logoSpin 2s linear infinite;
}

.logo-pulse {
    animation: logoPulse 2s ease-in-out infinite;
}

.logo-loading {
    animation: logoSpin 1s linear infinite, logoPulse 2s ease-in-out infinite;
}

/* Logo container styles */
.logo-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.logo-container:hover {
    transform: scale(1.05);
}

.logo-container.loading::after {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
}

/* ===== COMPREHENSIVE GLASS MORPHISM CARD SYSTEM ===== */
/* Standard glass card with consistent styling */
.glass-card {
    background: var(--glass-card-bg);
    border: 1px solid var(--glass-card-border);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border-radius: var(--radius-xl);
    padding: var(--card-padding);
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);
}

.glass-card:hover {
    background: var(--glass-hover);
    border-color: var(--glass-border-heavy);
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

/* Premium glass card with enhanced effects */
.glass-card-premium {
    background: var(--glass-card-premium-bg);
    border: 1px solid var(--glass-card-premium-border);
    backdrop-filter: blur(24px);
    -webkit-backdrop-filter: blur(24px);
    border-radius: var(--radius-2xl);
    padding: var(--space-8);
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-lg), 0 0 var(--base-4) rgba(0, 255, 247, 0.1);
}

.glass-card-premium:hover {
    background: var(--glass-bg-heavy);
    border-color: var(--cyber-cyan);
    box-shadow: var(--shadow-xl), 0 0 var(--base-6) rgba(0, 255, 247, 0.2);
    transform: translateY(-2px);                     /* Standardized to match Sela page */
}

/* Enhanced glass card with subtle glow */
.glass-card-enhanced {
    background: var(--glass-card-enhanced-bg);
    border: 1px solid var(--glass-card-enhanced-border);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-md), 0 0 var(--base-3) rgba(154, 0, 255, 0.08);
}

.glass-card-enhanced:hover {
    background: var(--glass-bg-medium);
    border-color: var(--cyber-purple);
    box-shadow: var(--shadow-lg), 0 0 var(--base-5) rgba(154, 0, 255, 0.15);
    transform: translateY(-2px);                     /* Standardized to match Sela page */
}

/* Card header styling */
.card-header {
    margin-bottom: var(--space-6);
    padding-bottom: var(--space-4);
    border-bottom: 1px solid var(--glass-border-light);
}

.card-title {
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    font-size: var(--font-size-xl);
    color: var(--text-primary);
    margin-bottom: var(--space-2);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.card-subtitle {
    font-size: var(--font-size-sm);
    color: var(--text-tertiary);
    margin: 0;
}

.card-body {
    padding: 0;
}

/* ===== ENHANCED BUTTON SYSTEM ===== */
/* Primary glass button */
.glass-button-primary {
    background: linear-gradient(135deg, var(--cyber-cyan), var(--cyber-blue));
    color: var(--onyx-black);
    border: none;
    padding: var(--space-3) var(--space-6);
    border-radius: var(--radius-xl);
    font-weight: 600;
    font-family: 'Orbitron', monospace;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-md), 0 0 var(--base-3) rgba(0, 255, 247, 0.3);
    cursor: pointer;
    min-height: var(--button-height);
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.glass-button-primary:hover {
    background: linear-gradient(135deg, var(--cyber-blue), var(--cyber-cyan));
    box-shadow: var(--shadow-lg), 0 0 var(--base-5) rgba(0, 255, 247, 0.5);
    transform: translateY(-2px) scale(1.02);
}

.glass-button-primary:active {
    transform: translateY(0) scale(0.98);
}

/* Secondary glass button */
.glass-button-secondary {
    background: var(--glass-bg);
    color: var(--text-primary);
    border: 1px solid var(--glass-border);
    padding: var(--space-3) var(--space-6);
    border-radius: var(--radius-xl);
    font-weight: 600;
    transition: all var(--transition-normal);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    cursor: pointer;
    min-height: var(--button-height);
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.glass-button-secondary:hover {
    background: var(--glass-hover);
    border-color: var(--cyber-cyan);
    box-shadow: 0 0 var(--base-3) rgba(0, 255, 247, 0.2);
    transform: translateY(-1px);
}

/* Small glass button */
.glass-button-sm {
    background: var(--glass-bg-light);
    color: var(--text-secondary);
    border: 1px solid var(--glass-border-light);
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    font-weight: 500;
    transition: all var(--transition-fast);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.glass-button-sm:hover {
    background: var(--glass-bg);
    border-color: var(--glass-border);
    color: var(--text-primary);
}

/* ===== ENHANCED FORM COMPONENTS ===== */
/* Glass input styling */
.glass-input {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    color: var(--text-primary);
    padding: var(--space-3) var(--space-4);
    border-radius: var(--radius-xl);
    font-size: var(--font-size-base);
    transition: all var(--transition-normal);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    min-height: var(--input-height);
    width: 100%;
}

.glass-input::placeholder {
    color: var(--text-muted);
}

.glass-input:focus {
    outline: none;
    border-color: var(--cyber-cyan);
    box-shadow: 0 0 0 2px rgba(0, 255, 247, 0.2);
    background: var(--glass-bg-medium);
}

.glass-input:hover {
    background: var(--glass-hover);
    border-color: var(--glass-border-heavy);
}

/* Glass select styling */
.glass-select {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    color: #ffffff !important; /* Force white text */
    padding: var(--space-3) var(--space-4);
    border-radius: var(--radius-xl);
    font-size: var(--font-size-base);
    transition: all var(--transition-normal);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    min-height: var(--input-height);
    width: 100%;
    appearance: none;
    cursor: pointer;
    opacity: 1 !important; /* Force full opacity */
}

.glass-select option {
    background: var(--onyx-black);
    color: #ffffff !important;
    padding: 8px 12px;
    border: none;
}

.glass-select:focus option {
    background: var(--onyx-gray);
    color: #ffffff !important;
}

.glass-select:focus {
    outline: none;
    border-color: var(--cyber-purple);
    box-shadow: 0 0 0 2px rgba(154, 0, 255, 0.2);
    background: var(--glass-bg-medium);
}

.glass-select:hover {
    background: var(--glass-hover);
    border-color: var(--glass-border-heavy);
}

/* Form label styling */
.form-label {
    display: block;
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-2);
}

.form-label.required::after {
    content: ' *';
    color: var(--cyber-red);
}

/* Form group styling */
.form-group {
    margin-bottom: var(--space-6);
}

.form-group-inline {
    display: flex;
    gap: var(--space-4);
    align-items: end;
}

/* Form validation states */
.form-field-valid {
    border-color: var(--cyber-green) !important;
    box-shadow: 0 0 0 2px rgba(0, 255, 136, 0.2) !important;
}

.form-field-invalid {
    border-color: var(--cyber-red) !important;
    box-shadow: 0 0 0 2px rgba(255, 51, 102, 0.2) !important;
}

.form-error-message {
    color: var(--cyber-red);
    font-size: var(--font-size-xs);
    margin-top: var(--space-1);
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

.form-success-message {
    color: var(--cyber-green);
    font-size: var(--font-size-xs);
    margin-top: var(--space-1);
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

/* ===== ENHANCED LOADING STATES ===== */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--glass-border);
    border-radius: 50%;
    border-top-color: var(--cyber-cyan);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.loading-dots {
    display: inline-flex;
    gap: 4px;
}

.loading-dots::after {
    content: '';
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: var(--cyber-cyan);
    animation: loadingDots 1.4s infinite ease-in-out both;
}

.loading-dots::before {
    content: '';
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: var(--cyber-cyan);
    animation: loadingDots 1.4s infinite ease-in-out both;
    animation-delay: -0.16s;
}

@keyframes loadingDots {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* ===== ENHANCED TOOLTIP SYSTEM ===== */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltip-text {
    visibility: hidden;
    width: 200px;
    background: var(--onyx-black);
    color: var(--text-primary);
    text-align: center;
    border-radius: var(--radius-md);
    padding: var(--space-2) var(--space-3);
    position: absolute;
    z-index: 1000;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity var(--transition-normal);
    font-size: var(--font-size-xs);
    border: 1px solid var(--glass-border);
    box-shadow: var(--shadow-lg);
}

.tooltip .tooltip-text::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: var(--onyx-black) transparent transparent transparent;
}

.tooltip:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}

/* ===== RESPONSIVE DESIGN UTILITIES ===== */
/* Container classes with precise measurements */
.container-xs {
    max-width: var(--container-xs);
    margin: 0 auto;
    padding: 0 var(--space-4);
}

.container-sm {
    max-width: var(--container-sm);
    margin: 0 auto;
    padding: 0 var(--space-4);
}

.container-md {
    max-width: var(--container-md);
    margin: 0 auto;
    padding: 0 var(--space-4);
}

.container-lg {
    max-width: var(--container-lg);
    margin: 0 auto;
    padding: 0 var(--space-6);
}

.container-xl {
    max-width: var(--container-xl);
    margin: 0 auto;
    padding: 0 var(--space-6);
}

.container-2xl {
    max-width: var(--container-2xl);
    margin: 0 auto;
    padding: 0 var(--space-8);
}

/* Responsive breakpoint utilities */
@media (max-width: 640px) {
    .container-xl,
    .container-lg,
    .container-md {
        padding: 0 var(--space-4);
    }

    .glass-card-premium {
        padding: var(--space-6);
    }

    .card-title {
        font-size: var(--font-size-lg);
    }

    .glass-button-primary,
    .glass-button-secondary {
        padding: var(--space-3) var(--space-4);
        font-size: var(--font-size-sm);
    }
}

@media (max-width: 768px) {
    .form-group-inline {
        flex-direction: column;
        gap: var(--space-3);
    }

    .glass-card {
        padding: var(--space-4);
    }

    .card-header {
        margin-bottom: var(--space-4);
        padding-bottom: var(--space-3);
    }
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */
/* Focus states for keyboard navigation */
*:focus {
    outline: 2px solid var(--cyber-cyan);
    outline-offset: 2px;
}

.glass-button-primary:focus,
.glass-button-secondary:focus,
.glass-input:focus,
.glass-select:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 255, 247, 0.3);
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .floating-particles,
    .data-streams {
        display: none;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --glass-bg: rgba(255, 255, 255, 0.2);
        --glass-border: rgba(255, 255, 255, 0.4);
        --text-primary: #ffffff;
        --text-secondary: #f0f0f0;
    }
}

/* ===== ENHANCED LOGO STYLING ===== */
.onnyx-nav-logo {
    transition: all var(--transition-normal);
    filter: brightness(0) saturate(100%) invert(64%) sepia(100%) saturate(2000%) hue-rotate(180deg) brightness(100%) contrast(100%);
}

.onnyx-page-logo {
    transition: all var(--transition-normal);
    filter: brightness(0) saturate(100%) invert(64%) sepia(100%) saturate(2000%) hue-rotate(180deg) brightness(100%) contrast(100%);
}

.onnyx-hero-logo {
    transition: all var(--transition-normal);
    filter: brightness(0) saturate(100%) invert(64%) sepia(100%) saturate(2000%) hue-rotate(180deg) brightness(100%) contrast(100%);
}

.onnyx-nav-logo:hover,
.onnyx-page-logo:hover,
.onnyx-hero-logo:hover {
    filter: brightness(0) saturate(100%) invert(64%) sepia(100%) saturate(2000%) hue-rotate(180deg) brightness(120%) contrast(110%);
    transform: scale(1.05);
}

/* ===== ENHANCED STATUS INDICATORS ===== */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-indicator.status-online {
    background: rgba(0, 255, 136, 0.2);
    color: var(--cyber-green);
    border: 1px solid rgba(0, 255, 136, 0.3);
}

.status-indicator.status-offline {
    background: rgba(255, 51, 102, 0.2);
    color: var(--cyber-red);
    border: 1px solid rgba(255, 51, 102, 0.3);
}

.status-indicator.status-pending {
    background: rgba(255, 204, 0, 0.2);
    color: var(--cyber-yellow);
    border: 1px solid rgba(255, 204, 0, 0.3);
}

.status-indicator.status-confirmed {
    background: rgba(0, 255, 247, 0.2);
    color: var(--cyber-cyan);
    border: 1px solid rgba(0, 255, 247, 0.3);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.status-dot.online {
    background: var(--cyber-green);
}

.status-dot.offline {
    background: var(--cyber-red);
}

.status-dot.pending {
    background: var(--cyber-yellow);
}

.status-dot.confirmed {
    background: var(--cyber-cyan);
}
    border: 2px solid transparent;
    border-top: 2px solid var(--cyber-cyan);
    border-radius: inherit;
    animation: logoSpin 1s linear infinite;
}

/* ===== ADVANCED GLASS MORPHISM SYSTEM V2.0 ===== */
/* Next-generation glass effects with enhanced cyberpunk aesthetics */

/* Base glass card with enhanced effects */
.glass-card {
    background: var(--glass-bg);
    backdrop-filter: blur(var(--base-2)) saturate(1.2);
    -webkit-backdrop-filter: blur(var(--base-2)) saturate(1.2);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    padding: var(--card-padding);
    box-shadow:
        var(--shadow-lg),
        0 0 0 1px rgba(255, 255, 255, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        0 0 var(--base-4) rgba(0, 255, 247, 0.05);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.glass-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(0, 255, 247, 0.1),
        transparent);
    transition: left 0.3s ease;
    pointer-events: none;
}

.glass-card:hover::before {
    left: 100%;
}

/* Removed duplicate glass-card hover definition - using the standardized one above */

/* Enhanced glass card with premium effects */
.glass-card-enhanced {
    background: var(--glass-bg-medium);
    backdrop-filter: blur(var(--base-3)) saturate(1.3) brightness(1.1);
    -webkit-backdrop-filter: blur(var(--base-3)) saturate(1.3) brightness(1.1);
    border: 1px solid var(--glass-border-heavy);
    border-radius: var(--radius-2xl);
    padding: var(--space-6);
    box-shadow:
        var(--shadow-xl),
        0 0 0 1px rgba(255, 255, 255, 0.08),
        inset 0 2px 0 rgba(255, 255, 255, 0.12),
        0 0 var(--base-6) rgba(0, 255, 247, 0.08);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.glass-card-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
        transparent,
        rgba(0, 255, 247, 0.15),
        rgba(154, 0, 255, 0.1),
        transparent);
    transition: left 0.3s ease;
    pointer-events: none;
}

.glass-card-enhanced:hover::before {
    left: 100%;
}

.glass-card-enhanced:hover {
    background: var(--glass-bg-heavy);
    border-color: rgba(0, 255, 247, 0.5);
    box-shadow:
        var(--shadow-2xl),
        0 0 var(--base-6) rgba(0, 255, 247, 0.4),
        0 0 var(--base-12) rgba(0, 255, 247, 0.2),
        inset 0 2px 0 rgba(255, 255, 255, 0.18);
    transform: translateY(-1px);                     /* Reduced movement for subtlety */
}

/* Premium glass card for hero sections */
.glass-card-premium {
    background: var(--glass-bg-heavy);
    backdrop-filter: blur(var(--base-4)) saturate(1.4) brightness(1.2);
    -webkit-backdrop-filter: blur(var(--base-4)) saturate(1.4) brightness(1.2);
    border: 2px solid var(--glass-border-heavy);
    border-radius: var(--radius-3xl);
    padding: var(--space-8);
    box-shadow:
        var(--shadow-2xl),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 3px 0 rgba(255, 255, 255, 0.15),
        0 0 var(--base-8) rgba(0, 255, 247, 0.1),
        0 0 var(--base-16) rgba(0, 255, 247, 0.05);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.glass-card-premium::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
        transparent,
        rgba(0, 255, 247, 0.2),
        rgba(154, 0, 255, 0.15),
        rgba(0, 128, 255, 0.1),
        transparent);
    transition: left 0.3s ease;
    pointer-events: none;
}

.glass-card-premium:hover::before {
    left: 100%;
}

.glass-card-premium:hover {
    background: var(--glass-active);
    border-color: rgba(0, 255, 247, 0.7);
    box-shadow:
        var(--shadow-2xl),
        0 0 var(--base-8) rgba(0, 255, 247, 0.5),
        0 0 var(--base-16) rgba(0, 255, 247, 0.3),
        0 0 var(--base-24) rgba(0, 255, 247, 0.1),
        inset 0 3px 0 rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);                     /* Standardized to match Sela page */
}

.glass-nav {
    background: transparent;                        /* Fully transparent */
    backdrop-filter: none;                          /* No blur */
    -webkit-backdrop-filter: none;
    border-bottom: none;                            /* No border line */
    height: var(--nav-height);                      /* 64px height */
}

.glass-button {
    background: var(--glass-bg);
    backdrop-filter: blur(var(--base-2)) saturate(1.2);
    -webkit-backdrop-filter: blur(var(--base-2)) saturate(1.2);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    padding: var(--space-3) var(--space-6);
    min-height: var(--button-height);
    transition: all var(--transition-normal);
    color: var(--text-primary);
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.glass-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.15),
        transparent);
    transition: left 0.3s ease;
    pointer-events: none;
}

.glass-button:hover::before {
    left: 100%;
}

.glass-button:hover {
    background: var(--glass-hover);
    border-color: rgba(0, 255, 247, 0.5);
    box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.3),
        0 0 12px rgba(0, 255, 247, 0.2);
    transform: translateY(-2px);                     /* Standardized to match Sela page */
}

.glass-button-primary {
    background: linear-gradient(135deg,
        var(--cyber-cyan) 0%,
        var(--cyber-blue) 100%);
    background-size: 200% 200%;
    border: none;
    color: var(--onyx-black);
    font-weight: 700;
    box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.3),
        0 0 16px rgba(0, 255, 247, 0.2);
    animation: gradientShift 6s ease infinite;
}

.glass-button-primary:hover {
    background: linear-gradient(135deg,
        var(--cyber-cyan-bright) 0%,
        var(--cyber-purple) 50%,
        var(--cyber-blue-bright) 100%);
    transform: translateY(-2px);                     /* Removed scale for consistency */
    box-shadow:
        0 6px 20px rgba(0, 0, 0, 0.4),
        0 0 20px rgba(0, 255, 247, 0.3);
    animation-duration: 3s;
}

.glass-button-enhanced {
    background: var(--glass-bg-medium);
    backdrop-filter: blur(var(--base-3)) saturate(1.3);
    -webkit-backdrop-filter: blur(var(--base-3)) saturate(1.3);
    border: 2px solid var(--glass-border-heavy);
    color: var(--text-primary);
    padding: var(--space-3) var(--space-6);
    border-radius: var(--radius-xl);
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    cursor: pointer;
    min-height: var(--button-height);
}

.glass-button-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
        transparent,
        rgba(154, 0, 255, 0.2),
        transparent);
    transition: left 0.3s ease;
    pointer-events: none;
}

.glass-button-enhanced:hover::before {
    left: 100%;
}

.glass-button-enhanced:hover {
    background: var(--glass-bg-heavy);
    border-color: var(--cyber-purple);
    transform: translateY(-2px);                     /* Removed scale for consistency */
    box-shadow:
        0 6px 20px rgba(0, 0, 0, 0.4),
        0 0 16px rgba(154, 0, 255, 0.3);
}

/* Neumorphism Components */
.neuro-card {
    background: var(--onyx-gray);
    border-radius: 20px;
    box-shadow:
        20px 20px 40px rgba(0, 0, 0, 0.5),
        -20px -20px 40px rgba(255, 255, 255, 0.02);
    transition: all 0.3s ease;
}

.neuro-card:hover {
    box-shadow:
        25px 25px 50px rgba(0, 0, 0, 0.6),
        -25px -25px 50px rgba(255, 255, 255, 0.03),
        0 0 30px rgba(0, 255, 255, 0.1);
    transform: translateY(-2px);                     /* Reduced from 5px to 2px */
}

.neuro-inset {
    background: var(--onyx-gray);
    border-radius: 15px;
    box-shadow:
        inset 10px 10px 20px rgba(0, 0, 0, 0.5),
        inset -10px -10px 20px rgba(255, 255, 255, 0.02);
}

/* Form styles with Onyx theme - IMPROVED CONTRAST */
.form-input {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 12px;
    padding: 12px 16px;
    color: #ffffff;                    /* Ensure pure white text */
    transition: all 0.3s ease;
    font-family: 'Montserrat', sans-serif;
}

.form-input:focus {
    outline: none;
    border-color: var(--cyber-cyan);
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.12);  /* Slightly more opaque for better readability */
}

.form-input::placeholder {
    color: rgba(255, 255, 255, 0.6);  /* Improved placeholder visibility */
}

.form-select {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 12px;
    padding: 12px 16px;
    color: white;
    transition: all 0.3s ease;
    font-family: 'Montserrat', sans-serif;
}

.form-select:focus {
    outline: none;
    border-color: var(--cyber-cyan);
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
}

.form-textarea {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 12px;
    padding: 12px 16px;
    color: white;
    transition: all 0.3s ease;
    resize: vertical;
    font-family: 'Montserrat', sans-serif;
}

.form-textarea:focus {
    outline: none;
    border-color: var(--cyber-cyan);
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
}

.form-textarea::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.form-checkbox {
    width: 18px;
    height: 18px;
    background: var(--glass-bg);
    border: 2px solid var(--glass-border);
    border-radius: 4px;
    transition: all 0.3s ease;
}

.form-checkbox:checked {
    background: var(--cyber-cyan);
    border-color: var(--cyber-cyan);
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
}

.form-radio {
    width: 18px;
    height: 18px;
    background: var(--glass-bg);
    border: 2px solid var(--glass-border);
    transition: all 0.3s ease;
}

.form-radio:checked {
    background: var(--cyber-cyan);
    border-color: var(--cyber-cyan);
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
}

/* Special Effects */
.hero-gradient {
    background: linear-gradient(135deg,
        var(--onyx-black) 0%,
        var(--onyx-gray) 25%,
        var(--onyx-black) 50%,
        var(--onyx-light) 75%,
        var(--onyx-black) 100%);
    background-size: 400% 400%;
    animation: gradientShift 8s ease infinite;
}

.cyber-grid {
    background-image:
        linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    opacity: 0.3;
}

.hologram-text {
    background: linear-gradient(45deg, var(--cyber-cyan), var(--cyber-purple), var(--cyber-blue));
    background-size: 200% 200%;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradientShift 3s ease infinite;
}

.data-stream {
    position: relative;
    overflow: hidden;
}

.data-stream::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(0, 255, 255, 0.2),
        transparent);
    animation: shimmer 3s infinite;
}

/* Badge styles with Onyx theme */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    font-family: 'Montserrat', sans-serif;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.badge-success {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
    border: 1px solid rgba(34, 197, 94, 0.3);
}

.badge-warning {
    background: rgba(251, 191, 36, 0.2);
    color: #fbbf24;
    border: 1px solid rgba(251, 191, 36, 0.3);
}

.badge-error {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.badge-info {
    background: rgba(0, 255, 255, 0.2);
    color: var(--cyber-cyan);
    border: 1px solid rgba(0, 255, 255, 0.3);
}

.badge-purple {
    background: rgba(154, 0, 255, 0.2);
    color: var(--cyber-purple);
    border: 1px solid rgba(154, 0, 255, 0.3);
}

/* Table styles with Onyx theme */
.table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
    overflow: hidden;
}

.table th {
    padding: 16px 24px;
    background: rgba(0, 255, 255, 0.1);
    color: var(--cyber-cyan);
    font-family: 'Orbitron', monospace;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    border-bottom: 1px solid var(--glass-border);
}

.table td {
    padding: 16px 24px;
    color: white;
    font-size: 14px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.table tbody tr:hover {
    background: rgba(0, 255, 255, 0.05);
}

/* Loading spinner with cyber theme */
.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(0, 255, 255, 0.2);
    border-top: 2px solid var(--cyber-cyan);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Hash display with cyber styling */
.hash {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    background: var(--glass-bg);
    color: var(--cyber-cyan);
    padding: 4px 8px;
    border-radius: 6px;
    border: 1px solid rgba(0, 255, 255, 0.3);
}

.hash-truncate {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: var(--cyber-cyan);
}

/* Status indicators with glow effects */
.status-active {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
    border: 1px solid rgba(34, 197, 94, 0.3);
    box-shadow: 0 0 10px rgba(34, 197, 94, 0.2);
}

.status-inactive {
    background: rgba(107, 114, 128, 0.2);
    color: #9ca3af;
    border: 1px solid rgba(107, 114, 128, 0.3);
}

.status-pending {
    background: rgba(251, 191, 36, 0.2);
    color: #fbbf24;
    border: 1px solid rgba(251, 191, 36, 0.3);
    box-shadow: 0 0 10px rgba(251, 191, 36, 0.2);
}

.status-error {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.3);
    box-shadow: 0 0 10px rgba(239, 68, 68, 0.2);
}

/* ===== PRECISION NAVIGATION SYSTEM ===== */
/* Mathematically precise navigation with exact measurements */

.onnyx-navbar {
    position: fixed;
    top: 0;                                         /* Flush to top of page */
    left: 0;
    right: 0;
    z-index: 1000;
    height: var(--nav-height);                      /* 64px precise height */
    background: transparent;                        /* Fully transparent */
    backdrop-filter: none;                          /* No blur */
    -webkit-backdrop-filter: none;
    border-bottom: none;                            /* No border line */
    box-shadow: none;                               /* No shadow */
    transition: var(--transition-normal);
}

.navbar-container {
    max-width: var(--container-2xl);                /* 1536px max width */
    margin: 0 auto;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--space-4);                      /* 32px horizontal padding */
    gap: var(--space-4);                            /* 32px gap */
}

/* Hide old navigation styles */
nav:not(.onnyx-navbar) {
    display: none !important;
}

/* Ensure body content starts below navigation flush to top */
body {
    padding-top: calc(var(--nav-height) + var(--base-1)) !important;     /* 64px + 8px = 72px total */
}

/* ===== FLOATING AUTHENTICATION ORB ===== */
/* Sophisticated floating orb with cyberpunk aesthetic */

.floating-auth-orb {
    position: fixed;
    top: calc(var(--nav-height) + var(--space-2));  /* 64px + 16px below navigation */
    right: 50%;                                     /* Center horizontally */
    transform: translateX(calc(var(--container-xl) / 2 - var(--space-4))); /* Align with container edge */
    z-index: 999;
    transition: var(--transition-normal);
    will-change: transform;
}

.orb-core {
    width: var(--space-8);                          /* 64px orb size */
    height: var(--space-8);
    background: linear-gradient(135deg,
        var(--cyber-cyan) 0%,
        var(--cyber-purple) 50%,
        var(--cyber-blue) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    position: relative;
    box-shadow:
        0 0 var(--base-3) rgba(0, 212, 255, 0.6),   /* 24px inner glow */
        0 0 var(--base-6) rgba(0, 212, 255, 0.4),   /* 48px outer glow */
        0 0 var(--base-8) rgba(139, 92, 246, 0.3);  /* 64px purple glow */
    animation: orbFloat 4s ease-in-out infinite;
    backdrop-filter: blur(var(--base-2));           /* 16px blur */
    -webkit-backdrop-filter: blur(var(--base-2));
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.orb-core:hover {
    transform: scale(1.1);
    box-shadow:
        0 0 var(--base-4) rgba(0, 212, 255, 0.8),   /* 32px enhanced glow */
        0 0 var(--base-8) rgba(0, 212, 255, 0.6),   /* 64px enhanced glow */
        0 0 var(--base-10) rgba(139, 92, 246, 0.4); /* 80px purple glow */
}

.orb-pulse {
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border: 2px solid var(--cyber-cyan);
    border-radius: 50%;
    animation: orbPulse 2s ease-in-out infinite;
    opacity: 0.7;
}

.orb-icon {
    font-size: var(--font-size-xl);                 /* 20px icon */
    color: var(--onyx-black);
    text-shadow: 0 0 var(--base-unit) rgba(0, 0, 0, 0.5); /* 8px shadow */
    z-index: 1;
    position: relative;
}

/* Floating Menu - More Compact Design */
.orb-menu {
    position: absolute;
    top: calc(100% + var(--space-1));               /* 8px below orb - reduced gap */
    right: 0;
    width: 280px;                                   /* Reduced width for compactness */
    background: var(--glass-bg);
    backdrop-filter: blur(var(--base-3));           /* 24px blur */
    -webkit-backdrop-filter: blur(var(--base-3));
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);                /* 16px radius - more compact */
    box-shadow:
        var(--shadow-2xl),
        0 0 var(--base-4) rgba(0, 212, 255, 0.15);  /* 32px cyber glow - reduced */
    opacity: 0;
    visibility: hidden;
    transform: translateY(-var(--space-1)) scale(0.95); /* 8px up, 95% scale */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
    z-index: 1000;
}

/* Bridge area to prevent menu disappearing */
.orb-menu::before {
    content: '';
    position: absolute;
    top: -var(--space-1);                           /* 8px bridge area */
    left: 0;
    right: 0;
    height: var(--space-1);                         /* 8px height */
    background: transparent;
    pointer-events: all;
}

.floating-auth-orb:hover .orb-menu,
.orb-menu:hover,
.orb-menu.menu-visible {
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateY(0) scale(1) !important;
    pointer-events: all !important;
}

/* Ensure hover state is maintained */
.floating-auth-orb:hover {
    z-index: 1001;
}

/* Enhanced orb core hover effects */
.floating-auth-orb:hover .orb-core {
    transform: scale(1.1);
    box-shadow:
        0 0 var(--base-4) rgba(0, 212, 255, 0.8),
        0 0 var(--base-8) rgba(0, 212, 255, 0.6),
        0 0 var(--base-10) rgba(139, 92, 246, 0.4);
}

.orb-menu-content {
    padding: var(--space-3);                        /* 24px padding - more compact */
}

.orb-menu-header {
    text-align: center;
    margin-bottom: var(--space-4);                  /* 32px margin */
    border-bottom: 1px solid var(--glass-border);
    padding-bottom: var(--space-3);                 /* 24px padding */
}

.orb-menu-title {
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    font-size: var(--font-size-lg);                 /* 18px */
    color: var(--cyber-cyan);
    text-shadow: 0 0 var(--base-unit) rgba(0, 212, 255, 0.5); /* 8px glow */
    margin-bottom: var(--space-1);                  /* 8px margin */
}

.orb-menu-subtitle {
    font-size: var(--font-size-sm);                 /* 14px */
    color: var(--text-secondary);
    font-weight: 500;
}

.orb-menu-options {
    margin-bottom: var(--space-2);                  /* 16px margin - more compact */
}

.orb-option {
    display: flex;
    align-items: center;
    gap: var(--space-2);                            /* 16px gap - more compact */
    padding: var(--space-2);                        /* 16px padding - more compact */
    border-radius: var(--radius-lg);                /* 12px radius */
    text-decoration: none;
    color: var(--text-primary);
    transition: var(--transition-normal);
    border: 1px solid transparent;
    margin-bottom: var(--space-1);                  /* 8px margin - more compact */
    min-height: var(--space-5);                     /* 40px touch target - smaller */
}

.orb-option:hover {
    background: rgba(0, 212, 255, 0.1);
    border-color: rgba(0, 212, 255, 0.3);
    transform: translateX(var(--space-1));          /* 8px slide */
    box-shadow: 0 0 var(--base-2) rgba(0, 212, 255, 0.3); /* 16px glow */
}

.orb-option-icon {
    width: var(--space-4);                          /* 32px icon container - more compact */
    height: var(--space-4);
    background: linear-gradient(135deg,
        rgba(0, 212, 255, 0.2),
        rgba(139, 92, 246, 0.2));
    border-radius: var(--radius-md);                /* 8px radius - smaller */
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-base);               /* 16px icon - smaller */
    flex-shrink: 0;
}

.orb-option-content {
    flex: 1;
}

.orb-option-title {
    font-weight: 600;
    font-size: var(--font-size-sm);                 /* 14px - smaller for compactness */
    color: var(--text-primary);
    margin-bottom: 2px;                             /* 2px margin - very compact */
}

.orb-option-desc {
    font-size: var(--font-size-xs);                 /* 12px - smaller */
    color: var(--text-secondary);
    font-weight: 400;
}

/* Enhanced option styles */
.orb-option-login:hover .orb-option-icon {
    background: linear-gradient(135deg,
        rgba(0, 212, 255, 0.4),
        rgba(139, 92, 246, 0.3));
    box-shadow: 0 0 var(--base-2) rgba(0, 212, 255, 0.4); /* 16px glow */
}

.orb-option-register:hover .orb-option-icon {
    background: linear-gradient(135deg,
        rgba(139, 92, 246, 0.4),
        rgba(0, 212, 255, 0.3));
    box-shadow: 0 0 var(--base-2) rgba(139, 92, 246, 0.4); /* 16px glow */
}

.orb-menu-footer {
    text-align: center;
    padding-top: var(--space-2);                    /* 16px padding - more compact */
    border-top: 1px solid var(--glass-border);
}

.orb-security-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-1);                            /* 8px gap */
    padding: var(--space-1) var(--space-2);         /* 8px vertical, 16px horizontal */
    background: rgba(0, 212, 255, 0.1);
    border: 1px solid rgba(0, 212, 255, 0.3);
    border-radius: var(--radius-full);              /* Full radius */
    font-size: var(--font-size-xs);                 /* 12px */
    color: var(--cyber-cyan);
    font-weight: 600;
}

.security-icon {
    font-size: var(--font-size-sm);                 /* 14px */
}

/* Orb animations */
@keyframes orbFloat {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    25% {
        transform: translateY(-8px) rotate(90deg);
    }
    50% {
        transform: translateY(-4px) rotate(180deg);
    }
    75% {
        transform: translateY(-12px) rotate(270deg);
    }
}

@keyframes orbPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.7;
    }
    50% {
        transform: scale(1.1);
        opacity: 1;
    }
}

/* Mobile responsiveness for floating orb */
@media (max-width: 767px) {
    .floating-auth-orb {
        top: calc(var(--nav-height) + var(--space-1)); /* 64px + 8px on mobile */
        right: 50%;                                  /* Center horizontally */
        transform: translateX(calc(50vw - var(--space-3))); /* Align with mobile content edge */
    }

    .orb-core {
        width: var(--space-7);                      /* 56px smaller on mobile */
        height: var(--space-7);
    }

    .orb-menu {
        width: 280px;                               /* Smaller menu on mobile */
        right: -var(--space-2);                    /* Adjust position */
    }

    .orb-option {
        padding: var(--space-2);                   /* 16px padding on mobile */
        min-height: var(--space-6);               /* 48px touch target on mobile */
    }
}

/* Reduced motion support for orb */
@media (prefers-reduced-motion: reduce) {
    .orb-core {
        animation: none;
    }

    .orb-pulse {
        animation: none;
        opacity: 0.7;
    }

    .orb-core:hover {
        transform: none;
    }

    .orb-option:hover {
        transform: none;
    }
}

/* ===== CORE TECHNOLOGIES ANIMATED VISUALIZATIONS ===== */
/* Cyberpunk blockchain activity animations for technology cards */

/* Base animation container for all tech cards */
.tech-card-animated {
    position: relative;
    overflow: hidden;
    will-change: transform;
    transform: translateZ(0); /* Force hardware acceleration */
}

.tech-card-animated::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 1;
    opacity: 0.6;
    will-change: transform, opacity;
}

/* 1. QUANTUM-RESISTANT SECURITY ANIMATIONS */
/* Animated cryptographic key exchanges and encryption patterns */

.quantum-security-card {
    position: relative;
    overflow: hidden;
}

.quantum-security-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(45deg, transparent 48%, rgba(0, 212, 255, 0.1) 49%, rgba(0, 212, 255, 0.1) 51%, transparent 52%),
        linear-gradient(-45deg, transparent 48%, rgba(0, 212, 255, 0.05) 49%, rgba(0, 212, 255, 0.05) 51%, transparent 52%);
    background-size: 32px 32px, 24px 24px;
    animation: cryptoPattern 8s linear infinite;
    pointer-events: none;
    z-index: 1;
}

.quantum-security-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(0, 212, 255, 0.2) 25%,
        rgba(0, 212, 255, 0.4) 50%,
        rgba(0, 212, 255, 0.2) 75%,
        transparent 100%);
    animation: keyExchange 6s ease-in-out infinite;
    pointer-events: none;
    z-index: 2;
    will-change: transform, opacity;
    transform: translateZ(0);
}

@keyframes cryptoPattern {
    0% { transform: translateX(0) translateY(0); }
    100% { transform: translateX(32px) translateY(32px); }
}

@keyframes keyExchange {
    0%, 100% { left: -100%; opacity: 0; }
    10% { opacity: 1; }
    50% { left: 0%; opacity: 1; }
    90% { opacity: 1; }
    100% { left: 100%; opacity: 0; }
}

/* 2. ETZEM TRUST PROTOCOL ANIMATIONS */
/* Animated trust network nodes with pulsing connections */

.trust-protocol-card {
    position: relative;
    overflow: hidden;
}

.trust-protocol-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 30%, rgba(154, 0, 255, 0.3) 2px, transparent 2px),
        radial-gradient(circle at 80% 20%, rgba(154, 0, 255, 0.2) 1px, transparent 1px),
        radial-gradient(circle at 40% 70%, rgba(154, 0, 255, 0.25) 1.5px, transparent 1.5px),
        radial-gradient(circle at 70% 80%, rgba(154, 0, 255, 0.2) 1px, transparent 1px),
        radial-gradient(circle at 60% 40%, rgba(154, 0, 255, 0.3) 2px, transparent 2px);
    background-size: 100% 100%;
    animation: trustNodes 4s ease-in-out infinite;
    pointer-events: none;
    z-index: 1;
}

.trust-protocol-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(45deg, transparent 40%, rgba(154, 0, 255, 0.1) 50%, transparent 60%),
        linear-gradient(135deg, transparent 40%, rgba(154, 0, 255, 0.08) 50%, transparent 60%);
    background-size: 40px 40px, 60px 60px;
    animation: trustConnections 6s linear infinite;
    pointer-events: none;
    z-index: 2;
    will-change: transform;
    transform: translateZ(0);
}

@keyframes trustNodes {
    0%, 100% {
        background-image:
            radial-gradient(circle at 20% 30%, rgba(154, 0, 255, 0.3) 2px, transparent 2px),
            radial-gradient(circle at 80% 20%, rgba(154, 0, 255, 0.2) 1px, transparent 1px),
            radial-gradient(circle at 40% 70%, rgba(154, 0, 255, 0.25) 1.5px, transparent 1.5px),
            radial-gradient(circle at 70% 80%, rgba(154, 0, 255, 0.2) 1px, transparent 1px),
            radial-gradient(circle at 60% 40%, rgba(154, 0, 255, 0.3) 2px, transparent 2px);
    }
    50% {
        background-image:
            radial-gradient(circle at 20% 30%, rgba(154, 0, 255, 0.5) 3px, transparent 3px),
            radial-gradient(circle at 80% 20%, rgba(154, 0, 255, 0.4) 2px, transparent 2px),
            radial-gradient(circle at 40% 70%, rgba(154, 0, 255, 0.45) 2.5px, transparent 2.5px),
            radial-gradient(circle at 70% 80%, rgba(154, 0, 255, 0.4) 2px, transparent 2px),
            radial-gradient(circle at 60% 40%, rgba(154, 0, 255, 0.5) 3px, transparent 3px);
    }
}

@keyframes trustConnections {
    0% { transform: translateX(0) translateY(0); }
    100% { transform: translateX(40px) translateY(40px); }
}

/* 3. MIKVAH TOKEN ECONOMY ANIMATIONS */
/* Animated token minting/transfer effects with flowing particles */

.token-economy-card {
    position: relative;
    overflow: hidden;
}

.token-economy-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 10% 20%, rgba(0, 128, 255, 0.3) 1px, transparent 1px),
        radial-gradient(circle at 30% 60%, rgba(0, 128, 255, 0.2) 1px, transparent 1px),
        radial-gradient(circle at 70% 30%, rgba(0, 128, 255, 0.25) 1px, transparent 1px),
        radial-gradient(circle at 90% 70%, rgba(0, 128, 255, 0.2) 1px, transparent 1px),
        radial-gradient(circle at 50% 90%, rgba(0, 128, 255, 0.3) 1px, transparent 1px);
    background-size: 100% 100%;
    animation: tokenParticles 5s ease-in-out infinite;
    pointer-events: none;
    z-index: 1;
}

.token-economy-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 200%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(0, 128, 255, 0.1) 20%,
        rgba(0, 128, 255, 0.2) 40%,
        rgba(0, 128, 255, 0.3) 50%,
        rgba(0, 128, 255, 0.2) 60%,
        rgba(0, 128, 255, 0.1) 80%,
        transparent 100%);
    animation: tokenFlow 4s ease-in-out infinite;
    pointer-events: none;
    z-index: 2;
    will-change: transform, opacity;
    transform: translateZ(0);
}

@keyframes tokenParticles {
    0%, 100% {
        transform: translateY(0);
        opacity: 0.6;
    }
    25% {
        transform: translateY(-8px);
        opacity: 0.8;
    }
    50% {
        transform: translateY(-4px);
        opacity: 1;
    }
    75% {
        transform: translateY(-12px);
        opacity: 0.8;
    }
}

@keyframes tokenFlow {
    0%, 100% {
        left: -100%;
        opacity: 0;
    }
    10% {
        opacity: 0.5;
    }
    50% {
        left: 0%;
        opacity: 1;
    }
    90% {
        opacity: 0.5;
    }
    100% {
        left: 100%;
        opacity: 0;
    }
}

/* Enhanced hover effects for animated cards */
.tech-card-animated:hover::before {
    opacity: 0.8;
    animation-duration: 0.5s;
}

.tech-card-animated:hover::after {
    opacity: 1;
    animation-duration: 0.3s;
}

/* Ensure content stays above animations */
.tech-card-animated > * {
    position: relative;
    z-index: 10;
}

/* Enhanced JavaScript-driven animations */
@keyframes encryptionScan {
    0% { transform: translateX(-100%) translateY(-100%); }
    100% { transform: translateX(100%) translateY(100%); }
}

@keyframes trustNodePulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.6;
    }
    50% {
        transform: scale(1.5);
        opacity: 1;
    }
}

@keyframes tokenFlow {
    0% {
        left: -4px;
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        left: calc(100% + 4px);
        opacity: 0;
    }
}

/* Enhanced active state for tech cards */
.tech-card-active {
    transform: translateY(-4px);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        0 0 30px rgba(0, 212, 255, 0.2);
}

.tech-card-active.quantum-security-card {
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        0 0 30px rgba(0, 212, 255, 0.3);
}

.tech-card-active.trust-protocol-card {
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        0 0 30px rgba(154, 0, 255, 0.3);
}

.tech-card-active.token-economy-card {
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        0 0 30px rgba(0, 128, 255, 0.3);
}

/* ===== USER JOURNEY ANIMATED VISUALIZATIONS ===== */
/* Cyberpunk animations for "Your Path to Digital Freedom" cards */

.journey-card-animated {
    position: relative;
    overflow: hidden;
    will-change: transform;
    transform: translateZ(0);
}

/* 1. COVENANT AWAKENING CARD */
.covenant-awakening-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 30% 40%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 70% 60%, rgba(0, 212, 255, 0.08) 0%, transparent 40%);
    animation: covenantAwakening 6s ease-in-out infinite;
    pointer-events: none;
    z-index: 1;
    opacity: 0.7;
}

@keyframes covenantAwakening {
    0%, 100% {
        transform: scale(1) rotate(0deg);
        opacity: 0.7;
    }
    50% {
        transform: scale(1.05) rotate(2deg);
        opacity: 1;
    }
}

/* 2. BUSINESS REGISTRATION CARD */
.business-registration-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(45deg, transparent 30%, rgba(154, 0, 255, 0.1) 50%, transparent 70%),
        linear-gradient(-45deg, transparent 30%, rgba(154, 0, 255, 0.08) 50%, transparent 70%);
    background-size: 60px 60px, 40px 40px;
    animation: businessRegistration 8s linear infinite;
    pointer-events: none;
    z-index: 1;
    opacity: 0.6;
}

@keyframes businessRegistration {
    0% { transform: translateX(0) translateY(0); }
    100% { transform: translateX(60px) translateY(60px); }
}

/* 3. REWARDS EARNING CARD */
.rewards-earning-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(0, 128, 255, 0.2) 2px, transparent 2px),
        radial-gradient(circle at 75% 75%, rgba(0, 128, 255, 0.15) 1px, transparent 1px),
        radial-gradient(circle at 50% 50%, rgba(0, 128, 255, 0.1) 1.5px, transparent 1.5px);
    background-size: 50px 50px, 30px 30px, 40px 40px;
    animation: rewardsEarning 5s ease-in-out infinite;
    pointer-events: none;
    z-index: 1;
    opacity: 0.8;
}

@keyframes rewardsEarning {
    0%, 100% {
        transform: translateY(0);
        opacity: 0.8;
    }
    50% {
        transform: translateY(-8px);
        opacity: 1;
    }
}

/* ===== ROADMAP ANIMATED VISUALIZATIONS ===== */
/* Cyberpunk animations for Development Roadmap cards */

.roadmap-card-animated {
    position: relative;
    overflow: hidden;
    will-change: transform;
    transform: translateZ(0);
}

/* 1. FOUNDATION PHASE CARD */
.foundation-phase-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(90deg, transparent 0%, rgba(0, 255, 136, 0.1) 50%, transparent 100%);
    animation: foundationPhase 4s ease-in-out infinite;
    pointer-events: none;
    z-index: 1;
    opacity: 0.6;
}

@keyframes foundationPhase {
    0%, 100% {
        transform: translateX(-100%);
        opacity: 0;
    }
    50% {
        transform: translateX(0%);
        opacity: 0.6;
    }
}

/* 2. TOKENOMICS PHASE CARD */
.tokenomics-phase-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(0, 212, 255, 0.1) 0%, transparent 40%);
    animation: tokenomicsPhase 7s ease-in-out infinite;
    pointer-events: none;
    z-index: 1;
    opacity: 0.8;
}

@keyframes tokenomicsPhase {
    0%, 100% {
        transform: scale(1);
        opacity: 0.8;
    }
    33% {
        transform: scale(1.02);
        opacity: 1;
    }
    66% {
        transform: scale(0.98);
        opacity: 0.9;
    }
}

/* 3. ADVANCED FEATURES CARD */
.advanced-features-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(135deg, transparent 40%, rgba(154, 0, 255, 0.12) 50%, transparent 60%),
        linear-gradient(45deg, transparent 40%, rgba(154, 0, 255, 0.08) 50%, transparent 60%);
    background-size: 80px 80px, 60px 60px;
    animation: advancedFeatures 9s linear infinite;
    pointer-events: none;
    z-index: 1;
    opacity: 0.7;
}

@keyframes advancedFeatures {
    0% { transform: translateX(0) translateY(0) rotate(0deg); }
    100% { transform: translateX(80px) translateY(80px) rotate(360deg); }
}

/* 4. GLOBAL EXPANSION CARD */
.global-expansion-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 10% 20%, rgba(0, 128, 255, 0.15) 1px, transparent 1px),
        radial-gradient(circle at 30% 70%, rgba(0, 128, 255, 0.12) 1px, transparent 1px),
        radial-gradient(circle at 70% 30%, rgba(0, 128, 255, 0.1) 1px, transparent 1px),
        radial-gradient(circle at 90% 80%, rgba(0, 128, 255, 0.08) 1px, transparent 1px);
    background-size: 100% 100%;
    animation: globalExpansion 6s ease-in-out infinite;
    pointer-events: none;
    z-index: 1;
    opacity: 0.9;
}

@keyframes globalExpansion {
    0%, 100% {
        transform: scale(1) rotate(0deg);
        opacity: 0.9;
    }
    25% {
        transform: scale(1.03) rotate(1deg);
        opacity: 1;
    }
    50% {
        transform: scale(1.01) rotate(0deg);
        opacity: 0.95;
    }
    75% {
        transform: scale(1.02) rotate(-1deg);
        opacity: 1;
    }
}

/* ===== TRUST INDICATORS ANIMATED VISUALIZATIONS ===== */
/* Cyberpunk animations for "Reclaim Your Future" trust indicators */

.trust-indicator-animated {
    position: relative;
    will-change: transform;
    transform: translateZ(0);
}

.trust-indicator-animated > div:first-child {
    position: relative;
    overflow: hidden;
}

/* 1. QUANTUM SECURE INDICATOR */
.quantum-secure-indicator > div:first-child::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at center, rgba(0, 212, 255, 0.2) 0%, transparent 70%);
    animation: quantumSecure 3s ease-in-out infinite;
    pointer-events: none;
    z-index: 1;
}

@keyframes quantumSecure {
    0%, 100% {
        transform: scale(1);
        opacity: 0.6;
    }
    50% {
        transform: scale(1.1);
        opacity: 1;
    }
}

/* 2. LIGHTNING FAST INDICATOR */
.lightning-fast-indicator > div:first-child::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(154, 0, 255, 0.3) 50%,
        transparent 100%);
    animation: lightningFast 2s ease-in-out infinite;
    pointer-events: none;
    z-index: 1;
}

@keyframes lightningFast {
    0%, 100% {
        left: -100%;
        opacity: 0;
    }
    50% {
        left: 100%;
        opacity: 1;
    }
}

/* 3. GLOBAL NETWORK INDICATOR */
.global-network-indicator > div:first-child::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 30% 30%, rgba(0, 128, 255, 0.2) 2px, transparent 2px),
        radial-gradient(circle at 70% 70%, rgba(0, 128, 255, 0.15) 1px, transparent 1px);
    background-size: 20px 20px, 15px 15px;
    animation: globalNetwork 4s linear infinite;
    pointer-events: none;
    z-index: 1;
}

@keyframes globalNetwork {
    0% { transform: translateX(0) translateY(0); }
    100% { transform: translateX(20px) translateY(20px); }
}

/* Additional JavaScript-driven animation keyframes */
@keyframes covenantEnergy {
    0%, 100% {
        transform: scale(1);
        opacity: 0.3;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.7;
    }
}

@keyframes businessNetwork {
    0% { transform: translateX(0) translateY(0); }
    100% { transform: translateX(30px) translateY(30px); }
}

@keyframes rewardParticles {
    0% {
        transform: translateY(0) scale(1);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-50px) scale(0.5);
        opacity: 0;
    }
}

/* Enhanced active states for new sections */
.journey-card-active {
    transform: translateY(-2px);
    transition: all 0.3s ease;
}

.roadmap-card-active {
    transition: all 0.3s ease;
}

.trust-indicator-active {
    transition: all 0.3s ease;
}

/* ===== ENHANCED ROADMAP SECTION STYLING ===== */
/* Custom styling for the redesigned roadmap section */

/* Roadmap card enhancements */
.roadmap-card-animated {
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.roadmap-card-animated:hover {
    transform: translateY(-4px) scale(1.01);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        0 0 30px rgba(0, 212, 255, 0.15);
}

/* Enhanced roadmap card state for JavaScript animations */
.roadmap-card-enhanced {
    transform: translateY(-2px) !important;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2), 0 0 20px rgba(0, 212, 255, 0.1) !important;
    transition: all 0.3s ease !important;
}

/* Fix Sacred Economics card header layout */
.tokenomics-phase-card .flex.items-center.space-x-6 {
    align-items: flex-start !important;
    gap: 1.5rem !important;
}

.tokenomics-phase-card .flex-shrink-0 {
    margin-top: 0.25rem !important;
}

/* NUCLEAR OPTION: Force timeline positioning with maximum specificity */
/* Override ALL possible conflicts with extreme specificity */

/* Timeline nodes - force positioning */
div.absolute.left-1\/2.transform.-translate-x-3.roadmap-timeline-node,
.roadmap-timeline-node.absolute.left-1\/2.transform.-translate-x-3,
.roadmap-timeline-node {
    transform: translateX(-12px) !important; /* Force -translate-x-3 */
    position: absolute !important;
    left: 50% !important;
    z-index: 20 !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
}

/* Sacred Economics card header - fix clock icon overlap */
div.tokenomics-phase-card div.flex.items-center.space-x-6,
.tokenomics-phase-card .flex.items-center.space-x-6 {
    align-items: flex-start !important;
    gap: 1.5rem !important;
    margin-bottom: 1.5rem !important;
}

div.tokenomics-phase-card div.flex-shrink-0,
.tokenomics-phase-card .flex-shrink-0 {
    margin-top: 0.25rem !important;
    flex-shrink: 0 !important;
}

/* Phase icons - force proper spacing with extreme specificity */
div.w-20.h-20.bg-gradient-to-br.from-cyber-cyan.to-cyber-blue.mr-12,
.tokenomics-phase-card + div.w-20.h-20,
div.tokenomics-phase-card ~ div.w-20.h-20 {
    margin-right: 3rem !important; /* Force mr-12 */
    margin-left: 0 !important;
}

div.w-20.h-20.bg-gradient-to-br.from-cyber-green.to-green-400.ml-8,
.foundation-phase-card + div.w-20.h-20,
div.foundation-phase-card ~ div.w-20.h-20 {
    margin-left: 2rem !important; /* Force ml-8 */
    margin-right: 0 !important;
}

div.w-20.h-20.bg-gradient-to-br.from-cyber-purple.to-cyber-blue.ml-8,
.advanced-features-card + div.w-20.h-20,
div.advanced-features-card ~ div.w-20.h-20 {
    margin-left: 2rem !important; /* Force ml-8 */
    margin-right: 0 !important;
}

div.w-20.h-20.bg-gradient-to-br.from-cyber-blue.to-green-400.mr-8,
.global-expansion-card + div.w-20.h-20,
div.global-expansion-card ~ div.w-20.h-20 {
    margin-right: 2rem !important; /* Force mr-8 */
    margin-left: 0 !important;
}

/* Disable ALL JavaScript-applied transforms on roadmap elements */
[class*="roadmap"] {
    transform: none !important;
}

/* Re-enable only our specific transforms */
.roadmap-timeline-node {
    transform: translateX(-12px) !important;
}

/* Prevent any animation system from overriding */
.roadmap-card-animated,
.roadmap-card-active,
.roadmap-card-enhanced {
    transform: none !important;
    transition: box-shadow 0.3s ease !important;
}

/* Phase-specific hover effects */
.foundation-phase-card:hover {
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        0 0 30px rgba(0, 255, 136, 0.2);
}

.tokenomics-phase-card:hover {
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        0 0 30px rgba(0, 212, 255, 0.2);
}

.advanced-features-card:hover {
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        0 0 30px rgba(154, 0, 255, 0.2);
}

.global-expansion-card:hover {
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        0 0 30px rgba(0, 128, 255, 0.2);
}

/* Enhanced timeline styling */


/* Roadmap progress indicator styling */
.roadmap-progress-indicator {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    background: rgba(0, 0, 0, 0.2);
    border-radius: 50px;
    padding: 16px 24px;
    border: 1px solid rgba(0, 212, 255, 0.2);
}

/* Enhanced roadmap icon container */
.roadmap-icon-container {
    position: relative;
    overflow: hidden;
}

.roadmap-icon-container::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg, transparent, rgba(0, 212, 255, 0.1), transparent);
    animation: iconRotate 8s linear infinite;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.roadmap-icon-container:hover::before {
    opacity: 1;
}

@keyframes iconRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced timeline node styling */
.roadmap-timeline-node {
    position: relative;
    transition: all 0.3s ease;
}

.roadmap-timeline-node::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: radial-gradient(circle, currentColor 0%, transparent 70%);
    opacity: 0.3;
    animation: nodeGlow 4s ease-in-out infinite;
}

.roadmap-timeline-node::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: radial-gradient(circle, currentColor 0%, transparent 80%);
    opacity: 0.15;
    animation: nodeGlow 4s ease-in-out infinite reverse;
}

/* Enhanced floating dots animation */
@keyframes floatingDots {
    0%, 100% {
        transform: translateY(0) scale(1);
        opacity: 0.3;
    }
    25% {
        transform: translateY(-10px) scale(1.1);
        opacity: 0.6;
    }
    50% {
        transform: translateY(-5px) scale(0.9);
        opacity: 0.4;
    }
    75% {
        transform: translateY(-15px) scale(1.05);
        opacity: 0.5;
    }
}

/* Apply floating animation to background dots */
.floating-blockchain-nodes > div {
    animation-duration: 4s;
    animation-timing-function: ease-in-out;
    animation-iteration-count: infinite;
}

.floating-blockchain-nodes > div:nth-child(1) { animation-name: floatingDots; animation-delay: 0s; }
.floating-blockchain-nodes > div:nth-child(2) { animation-name: floatingDots; animation-delay: 0.5s; }
.floating-blockchain-nodes > div:nth-child(3) { animation-name: floatingDots; animation-delay: 1s; }
.floating-blockchain-nodes > div:nth-child(4) { animation-name: floatingDots; animation-delay: 1.5s; }
.floating-blockchain-nodes > div:nth-child(5) { animation-name: floatingDots; animation-delay: 2s; }
.floating-blockchain-nodes > div:nth-child(6) { animation-name: floatingDots; animation-delay: 2.5s; }
.floating-blockchain-nodes > div:nth-child(7) { animation-name: floatingDots; animation-delay: 3s; }
.floating-blockchain-nodes > div:nth-child(8) { animation-name: floatingDots; animation-delay: 3.5s; }
.floating-blockchain-nodes > div:nth-child(9) { animation-name: floatingDots; animation-delay: 0.8s; }
.floating-blockchain-nodes > div:nth-child(10) { animation-name: floatingDots; animation-delay: 1.3s; }
.floating-blockchain-nodes > div:nth-child(11) { animation-name: floatingDots; animation-delay: 1.8s; }
.floating-blockchain-nodes > div:nth-child(12) { animation-name: floatingDots; animation-delay: 2.3s; }

/* Accessibility: Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    /* Core Technologies animations */
    .quantum-security-card::before,
    .quantum-security-card::after,
    .trust-protocol-card::before,
    .trust-protocol-card::after,
    .token-economy-card::before,
    .token-economy-card::after {
        animation: none;
    }

    .tech-card-animated::before,
    .tech-card-animated::after {
        opacity: 0.3;
    }

    .quantum-encryption-overlay,
    .trust-network-overlay,
    .token-flow-overlay {
        display: none !important;
    }

    .tech-card-active {
        transform: none;
        box-shadow: none;
    }

    /* User Journey animations */
    .covenant-awakening-card::before,
    .business-registration-card::before,
    .rewards-earning-card::before {
        animation: none;
        opacity: 0.2;
    }

    .journey-card-animated::before {
        opacity: 0.2;
    }

    /* Roadmap animations */
    .foundation-phase-card::before,
    .tokenomics-phase-card::before,
    .advanced-features-card::before,
    .global-expansion-card::before {
        animation: none;
        opacity: 0.2;
    }

    .roadmap-card-animated::before {
        opacity: 0.2;
    }

    /* Trust Indicators animations */
    .quantum-secure-indicator > div:first-child::before,
    .lightning-fast-indicator > div:first-child::before,
    .global-network-indicator > div:first-child::before {
        animation: none;
        opacity: 0.2;
    }

    .trust-indicator-animated > div:first-child::before {
        opacity: 0.2;
    }

    /* JavaScript-driven animations */
    .covenant-energy-overlay,
    .business-network-overlay,
    .rewards-flow-overlay {
        display: none !important;
    }

    .journey-card-active,
    .roadmap-card-active,
    .trust-indicator-active {
        transform: none !important;
        box-shadow: none !important;
    }
}

/* ===== ENHANCED LOGO SYSTEM ===== */
/* Advanced cyberpunk logo integration with real logo support */

.navbar-logo {
    flex-shrink: 0;
    position: relative;
}

.logo-link {
    display: flex;
    align-items: center;
    gap: var(--space-3);                    /* 24px precise gap */
    text-decoration: none;
    padding: var(--space-2) var(--space-3); /* 16px vertical, 24px horizontal */
    border-radius: var(--radius-2xl);       /* 24px radius */
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(var(--base-unit)); /* 8px blur */
    -webkit-backdrop-filter: blur(var(--base-unit));
}

.logo-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(0, 255, 247, 0.15),
        transparent);
    transition: left var(--transition-slow);
    z-index: 0;
}

.logo-link:hover::before {
    left: 100%;
}

.logo-link:hover {
    background: var(--glass-bg-light);
    border: 1px solid rgba(0, 255, 247, 0.2);
    transform: translateY(-2px);
    box-shadow:
        0 0 var(--base-4) rgba(0, 255, 247, 0.3),
        0 0 var(--base-8) rgba(0, 255, 247, 0.1);
}

/* Clean logo icon container - no glow effects */
.logo-icon {
    width: var(--space-8);                  /* 64px precise size */
    height: var(--space-8);
    background: linear-gradient(135deg,
        var(--cyber-cyan) 0%,
        var(--cyber-purple) 50%,
        var(--cyber-blue) 100%);
    border-radius: var(--radius-2xl);       /* 24px radius */
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    transition: all var(--transition-normal);
    z-index: 1;
}

.logo-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg,
        transparent 30%,
        rgba(255, 255, 255, 0.2) 50%,
        transparent 70%);
    transform: translateX(-100%) rotate(45deg);
    transition: transform var(--transition-slow);
    z-index: 1;
}

.logo-link:hover .logo-icon::before {
    transform: translateX(100%) rotate(45deg);
}

.logo-link:hover .logo-icon {
    transform: scale(1.02);
}

/* Logo image styling - no filters for visibility */
.logo-image {
    width: var(--space-6);                  /* 48px image size */
    height: var(--space-6);
    object-fit: contain;
    transition: all var(--transition-normal);
    z-index: 2;
    position: relative;
    display: block !important;
}

.logo-link:hover .logo-image {
    transform: scale(1.05);
}

/* ===== CYBERPUNK HERO LOGO EFFECTS ===== */
/* Enhanced visual effects for the homepage hero logo */

.onnyx-hero-logo {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    filter: drop-shadow(0 0 20px rgba(0, 212, 255, 0.4))
            drop-shadow(0 0 40px rgba(0, 212, 255, 0.2));
    animation: logoGlow 3s ease-in-out infinite alternate,
               logoPulse 2s ease-in-out infinite;
    position: relative;
    z-index: 10;
}

/* Hover effects for hero logo */
.onnyx-hero-logo:hover {
    transform: scale(1.1);
    filter: drop-shadow(0 0 30px rgba(0, 212, 255, 0.6))
            drop-shadow(0 0 60px rgba(0, 212, 255, 0.4))
            drop-shadow(0 0 80px rgba(139, 92, 246, 0.3));
    animation: logoGlowIntense 1.5s ease-in-out infinite alternate,
               logoPulseIntense 1s ease-in-out infinite;
}

/* Scanning line effect overlay */
.onnyx-hero-logo::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(0, 212, 255, 0.3) 50%,
        transparent 100%);
    animation: logoScan 4s ease-in-out infinite;
    z-index: 1;
    pointer-events: none;
}

/* Shimmer effect */
.onnyx-hero-logo::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg,
        transparent 30%,
        rgba(0, 212, 255, 0.1) 50%,
        transparent 70%);
    border-radius: 8px;
    animation: logoShimmer 6s ease-in-out infinite;
    z-index: -1;
    pointer-events: none;
}

/* Keyframe animations for cyberpunk effects */
@keyframes logoGlow {
    0% {
        filter: drop-shadow(0 0 20px rgba(0, 212, 255, 0.4))
                drop-shadow(0 0 40px rgba(0, 212, 255, 0.2));
    }
    100% {
        filter: drop-shadow(0 0 25px rgba(0, 212, 255, 0.5))
                drop-shadow(0 0 50px rgba(0, 212, 255, 0.3));
    }
}

@keyframes logoGlowIntense {
    0% {
        filter: drop-shadow(0 0 30px rgba(0, 212, 255, 0.6))
                drop-shadow(0 0 60px rgba(0, 212, 255, 0.4))
                drop-shadow(0 0 80px rgba(139, 92, 246, 0.3));
    }
    100% {
        filter: drop-shadow(0 0 40px rgba(0, 212, 255, 0.8))
                drop-shadow(0 0 80px rgba(0, 212, 255, 0.6))
                drop-shadow(0 0 120px rgba(139, 92, 246, 0.5));
    }
}

@keyframes logoPulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.9;
    }
}

@keyframes logoPulseIntense {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.95;
    }
}

@keyframes logoScan {
    0% {
        left: -100%;
    }
    50% {
        left: 100%;
    }
    100% {
        left: 100%;
    }
}

@keyframes logoShimmer {
    0% {
        transform: translateX(-100%) rotate(45deg);
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: translateX(200%) rotate(45deg);
        opacity: 0;
    }
}

/* ===== CONTEXT-SPECIFIC LOGO CLASSES ===== */
/* Standardized logo styling for different page contexts */

/* Navigation Header Logo - Subtle effects */
.onnyx-nav-logo {
    transition: all 0.3s ease;
    filter: drop-shadow(0 0 8px rgba(0, 212, 255, 0.3));
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

.onnyx-nav-logo:hover {
    transform: scale(1.05);
    filter: drop-shadow(0 0 12px rgba(0, 212, 255, 0.5));
}

/* Page Header Logo - Moderate effects */
.onnyx-page-logo {
    transition: all 0.4s ease;
    filter: drop-shadow(0 0 15px rgba(0, 212, 255, 0.4));
    animation: logoGlowSubtle 4s ease-in-out infinite alternate;
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

.onnyx-page-logo:hover {
    transform: scale(1.08);
    filter: drop-shadow(0 0 20px rgba(0, 212, 255, 0.6))
            drop-shadow(0 0 40px rgba(139, 92, 246, 0.3));
}

/* Secondary/Footer Logo - Minimal effects */
.onnyx-secondary-logo {
    transition: all 0.2s ease;
    filter: drop-shadow(0 0 5px rgba(0, 212, 255, 0.2));
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

.onnyx-secondary-logo:hover {
    transform: scale(1.03);
    filter: drop-shadow(0 0 8px rgba(0, 212, 255, 0.4));
}

/* Subtle glow animation for page logos */
@keyframes logoGlowSubtle {
    0% {
        filter: drop-shadow(0 0 15px rgba(0, 212, 255, 0.4));
    }
    100% {
        filter: drop-shadow(0 0 18px rgba(0, 212, 255, 0.5));
    }
}

/* Fallback symbol for when logo image is not available */
.logo-symbol {
    font-size: var(--font-size-4xl);        /* 36px symbol */
    font-weight: 900;
    color: var(--onyx-black);
    text-shadow:
        0 0 var(--base-unit) rgba(255, 255, 255, 0.8),
        0 0 var(--base-2) rgba(0, 255, 247, 0.4);
    transition: all var(--transition-normal);
    z-index: 2;
    position: relative;
    display: block;
}

.logo-link:hover .logo-symbol {
    transform: scale(1.1) rotate(15deg);
    text-shadow:
        0 0 var(--base-2) rgba(255, 255, 255, 1),
        0 0 var(--base-4) rgba(0, 255, 247, 0.6),
        0 0 var(--base-6) rgba(0, 255, 247, 0.3);
}

/* Simplified logo text - removed excessive glow */
.logo-text {
    font-family: 'Orbitron', monospace;
    font-weight: 800;
    font-size: var(--font-size-4xl);        /* 36px text */
    background: linear-gradient(135deg,
        var(--cyber-cyan) 0%,
        var(--cyber-purple) 50%,
        var(--cyber-blue) 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    letter-spacing: 0.05em;
    transition: all var(--transition-normal);
    z-index: 1;
    position: relative;
}

.logo-link:hover .logo-text {
    transform: scale(1.01);
}

/* Removed logo loading animations for cleaner appearance */

/* ===== NAVIGATION LINKS ===== */
.navbar-nav {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
    justify-content: center;
    max-width: 900px;
    margin: 0 auto;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.875rem 1.5rem;
    border-radius: 14px;
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    min-width: 130px;
    min-height: 48px;
    justify-content: center;
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.nav-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
        rgba(0, 255, 255, 0.1) 0%,
        rgba(139, 92, 246, 0.1) 100%);
    transition: left 0.3s ease;
    z-index: -1;
}

.nav-item:hover::before {
    left: 0;
}

.nav-item:hover {
    color: var(--cyber-cyan);
    background: rgba(0, 255, 255, 0.08);
    border-color: rgba(0, 255, 255, 0.2);
    box-shadow:
        0 4px 20px rgba(0, 255, 255, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.nav-icon {
    font-size: 1.1rem;
    opacity: 0.8;
    transition: all 0.3s ease;
}

.nav-item:hover .nav-icon {
    opacity: 1;
    transform: scale(1.1);
}

.nav-label {
    font-weight: 600;
    letter-spacing: 0.025em;
}

/* ===== ACTIVE NAVIGATION STATES ===== */
.nav-item-active {
    background: linear-gradient(135deg,
        rgba(0, 255, 255, 0.2) 0%,
        rgba(139, 92, 246, 0.2) 100%) !important;
    border-color: rgba(0, 255, 255, 0.5) !important;
    color: var(--cyber-cyan) !important;
    box-shadow:
        0 6px 25px rgba(0, 255, 255, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
    transform: translateY(-1px);
    position: relative;
}

.nav-item-active::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, var(--cyber-cyan), var(--cyber-purple));
    border-radius: 16px;
    z-index: -1;
    opacity: 0.3;
    animation: activeGlow 2s ease-in-out infinite alternate;
}

.nav-item-active .nav-icon {
    opacity: 1;
    transform: scale(1.1);
    text-shadow: 0 0 10px var(--cyber-cyan);
}

.nav-item-active .nav-label {
    font-weight: 700;
    text-shadow: 0 0 8px rgba(0, 255, 255, 0.5);
}

/* Featured item active state enhancement */
.nav-item-featured.nav-item-active {
    background: linear-gradient(135deg,
        rgba(0, 255, 255, 0.25) 0%,
        rgba(139, 92, 246, 0.25) 100%) !important;
    border-color: rgba(0, 255, 255, 0.7) !important;
    box-shadow:
        0 8px 35px rgba(0, 255, 255, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
    transform: translateY(-2px);
}

/* Mobile active states */
.mobile-nav-item-active {
    background: linear-gradient(135deg,
        rgba(0, 255, 255, 0.2) 0%,
        rgba(139, 92, 246, 0.2) 100%) !important;
    border-color: rgba(0, 255, 255, 0.5) !important;
    color: var(--cyber-cyan) !important;
    box-shadow:
        0 6px 25px rgba(0, 255, 255, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
    transform: translateX(8px);
    position: relative;
}

.mobile-nav-item-active::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, var(--cyber-cyan), var(--cyber-purple));
    border-radius: 16px;
    z-index: -1;
    opacity: 0.4;
    animation: activeGlow 2s ease-in-out infinite alternate;
}

.mobile-nav-item-active .mobile-nav-icon {
    text-shadow: 0 0 10px var(--cyber-cyan);
    transform: scale(1.1);
}

/* Featured mobile item active state enhancement */
.mobile-nav-item.featured.mobile-nav-item-active {
    background: linear-gradient(135deg,
        rgba(0, 255, 255, 0.3) 0%,
        rgba(139, 92, 246, 0.3) 100%) !important;
    border-color: rgba(0, 255, 255, 0.7) !important;
    box-shadow:
        0 8px 30px rgba(0, 255, 255, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.25) !important;
    transform: translateX(12px);
}

/* Active glow animation */
@keyframes activeGlow {
    0% { opacity: 0.3; }
    100% { opacity: 0.6; }
}

/* ===== CRITICAL: MISSING ACTIVE NAVIGATION STYLES ===== */
/* Active styles for .nav-link classes used in enhanced navigation template */

.nav-link.active {
    color: var(--cyber-cyan) !important;
    background: rgba(0, 212, 255, 0.15) !important;
    border-bottom: 2px solid var(--cyber-cyan) !important;
    font-weight: 600;
    text-shadow: 0 0 var(--base-unit) rgba(0, 212, 255, 0.5); /* 8px glow */
    box-shadow:
        0 0 var(--base-2) rgba(0, 212, 255, 0.3),   /* 16px outer glow */
        inset 0 1px 0 rgba(255, 255, 255, 0.1);     /* Inner highlight */
    transform: translateY(-1px);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 50%;
    transform: translateX(-50%);
    width: var(--space-6);                          /* 48px indicator */
    height: 2px;
    background: linear-gradient(90deg,
        transparent,
        var(--cyber-cyan),
        transparent);
    border-radius: var(--radius-full);
    animation: activeGlow 2s ease-in-out infinite alternate;
}

/* Enhanced active state for featured navigation items */
.nav-link.nav-link-featured.active {
    background: linear-gradient(135deg,
        rgba(0, 212, 255, 0.2),
        rgba(139, 92, 246, 0.15)) !important;
    border-bottom: 2px solid var(--cyber-cyan) !important;
    box-shadow:
        0 0 var(--base-3) rgba(0, 212, 255, 0.4),   /* 24px enhanced glow */
        0 0 var(--base-6) rgba(139, 92, 246, 0.2),  /* 48px purple glow */
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.nav-link.nav-link-featured.active .nav-badge {
    background: var(--cyber-cyan) !important;
    color: var(--onyx-black) !important;
    box-shadow: 0 0 var(--base-unit) rgba(0, 212, 255, 0.6); /* 8px badge glow */
}

/* ===== MOBILE ACTIVE NAVIGATION STYLES ===== */
/* Active styles for mobile navigation */

.mobile-nav-item.active {
    color: var(--cyber-cyan) !important;
    background: linear-gradient(135deg,
        rgba(0, 212, 255, 0.2) 0%,
        rgba(139, 92, 246, 0.15) 100%) !important;
    border-left: 4px solid var(--cyber-cyan) !important;
    font-weight: 600;
    text-shadow: 0 0 var(--base-unit) rgba(0, 212, 255, 0.5); /* 8px glow */
    box-shadow:
        0 0 var(--base-3) rgba(0, 212, 255, 0.3),   /* 24px outer glow */
        inset 0 1px 0 rgba(255, 255, 255, 0.1);     /* Inner highlight */
    transform: translateX(var(--space-1));          /* 8px slide */
    min-height: var(--space-6);                     /* 48px touch target */
}

.mobile-nav-item.active .mobile-nav-icon {
    color: var(--cyber-cyan) !important;
    text-shadow: 0 0 var(--base-unit) rgba(0, 212, 255, 0.6); /* 8px icon glow */
    transform: scale(1.1);
}

.mobile-nav-item.featured.active {
    background: linear-gradient(135deg,
        rgba(0, 212, 255, 0.25) 0%,
        rgba(139, 92, 246, 0.2) 100%) !important;
    border-left: 4px solid var(--cyber-cyan) !important;
    box-shadow:
        0 0 var(--base-4) rgba(0, 212, 255, 0.4),   /* 32px enhanced glow */
        0 0 var(--base-8) rgba(139, 92, 246, 0.2),  /* 64px purple glow */
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
    transform: translateX(var(--space-2));          /* 16px enhanced slide */
}

/* Featured Navigation Item (Biblical Tokenomics) */
.nav-item-featured {
    background: linear-gradient(135deg,
        rgba(0, 255, 255, 0.15) 0%,
        rgba(139, 92, 246, 0.15) 100%) !important;
    border: 2px solid rgba(0, 255, 255, 0.4) !important;
    color: var(--cyber-cyan) !important;
    position: relative;
    min-width: 180px;
    font-weight: 700;
    box-shadow:
        0 6px 20px rgba(0, 255, 255, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
}

.nav-item-featured:hover {
    background: linear-gradient(135deg,
        rgba(0, 255, 255, 0.22) 0%,
        rgba(139, 92, 246, 0.22) 100%) !important;
    border-color: rgba(0, 255, 255, 0.6) !important;
    box-shadow:
        0 10px 35px rgba(0, 255, 255, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.25) !important;
    transform: translateY(-3px);
}

.nav-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: linear-gradient(135deg, var(--cyber-purple), var(--cyber-cyan));
    color: white;
    font-size: 0.7rem;
    font-weight: 700;
    padding: 0.25rem 0.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(139, 92, 246, 0.4);
    animation: pulse 2s infinite;
}

/* ===== AUTHENTICATION SECTION ===== */
.navbar-auth {
    flex-shrink: 0;
}

/* Guest Authentication Buttons */
.auth-buttons {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.auth-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.875rem 1.25rem;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    min-height: 48px;
    max-width: 160px;
}

.auth-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    transition: left 0.3s ease;
    z-index: -1;
}

.auth-btn:hover::before {
    left: 0;
}

.auth-btn-secondary {
    color: var(--text-secondary);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.auth-btn-secondary::before {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(0, 255, 255, 0.05) 100%);
}

.auth-btn-secondary:hover {
    color: var(--cyber-cyan);
    background: rgba(0, 255, 255, 0.08);
    border-color: rgba(0, 255, 255, 0.2);
    box-shadow: 0 4px 20px rgba(0, 255, 255, 0.15);
    transform: translateY(-2px);
}

.auth-btn-primary {
    color: white;
    background: linear-gradient(135deg,
        var(--cyber-cyan) 0%,
        var(--cyber-purple) 100%);
    border: 1px solid rgba(0, 255, 255, 0.3);
    box-shadow: 0 4px 20px rgba(0, 255, 255, 0.2);
}

.auth-btn-primary::before {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 100%);
}

.auth-btn-primary:hover {
    background: linear-gradient(135deg,
        var(--cyber-cyan) 0%,
        var(--cyber-purple) 100%);
    box-shadow:
        0 6px 30px rgba(0, 255, 255, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transform: translateY(-2px) scale(1.02);
}

.btn-icon {
    font-size: 0.9rem;
    opacity: 0.9;
    transition: all 0.3s ease;
}

.auth-btn:hover .btn-icon {
    opacity: 1;
    transform: scale(1.05);
}

/* User Dropdown */
.user-dropdown {
    position: relative;
}

.user-button {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem 1.25rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    min-width: 220px;
    min-height: 48px;
}

.user-button:hover {
    background: rgba(0, 255, 255, 0.08);
    border-color: rgba(0, 255, 255, 0.3);
    box-shadow: 0 4px 20px rgba(0, 255, 255, 0.2);
    transform: translateY(-1px);
}

.user-avatar {
    width: 42px;
    height: 42px;
    background: linear-gradient(135deg, var(--cyber-cyan), var(--cyber-purple));
    border-radius: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 10px rgba(0, 255, 255, 0.3);
    flex-shrink: 0;
}

.user-initial {
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    font-size: 1.1rem;
    color: var(--onyx-black);
}

.user-details {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    flex: 1;
    min-width: 0;
}

.user-name {
    font-weight: 600;
    font-size: 0.95rem;
    color: var(--text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-status {
    font-size: 0.8rem;
    color: var(--cyber-cyan);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.user-status::before {
    content: '';
    width: 6px;
    height: 6px;
    background: var(--cyber-cyan);
    border-radius: 50%;
    box-shadow: 0 0 6px var(--cyber-cyan);
    animation: pulse 2s infinite;
}

.dropdown-chevron {
    width: 16px;
    height: 16px;
    color: var(--text-tertiary);
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.user-button:hover .dropdown-chevron {
    color: var(--cyber-cyan);
}

/* User Menu Dropdown */
.user-menu {
    position: absolute;
    top: calc(100% + 0.75rem);
    right: 0;
    width: 300px;
    background: linear-gradient(135deg,
        rgba(26, 26, 26, 0.98) 0%,
        rgba(42, 42, 42, 0.95) 100%);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 20px;
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.4),
        0 0 60px rgba(0, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    z-index: 9999;
    overflow: hidden;
}

.menu-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
    background: linear-gradient(135deg,
        rgba(0, 255, 255, 0.05) 0%,
        rgba(139, 92, 246, 0.05) 100%);
}

.menu-user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.menu-avatar {
    width: 52px;
    height: 52px;
    background: linear-gradient(135deg, var(--cyber-cyan), var(--cyber-purple));
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(0, 255, 255, 0.3);
    flex-shrink: 0;
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    font-size: 1.2rem;
    color: var(--onyx-black);
}

.menu-name {
    font-weight: 600;
    font-size: 1rem;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.menu-email {
    font-size: 0.85rem;
    color: var(--text-tertiary);
}

.menu-divider {
    height: 1px;
    background: rgba(255, 255, 255, 0.08);
    margin: 0.5rem 0;
}

.menu-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    color: var(--text-secondary);
    text-decoration: none;
    transition: all 0.3s ease;
    font-weight: 500;
}

.menu-item:hover {
    background: rgba(0, 255, 255, 0.08);
    color: var(--cyber-cyan);
}

.menu-item-danger:hover {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

.menu-icon {
    font-size: 1.1rem;
    opacity: 0.8;
    transition: all 0.3s ease;
    width: 20px;
    text-align: center;
}

.menu-item:hover .menu-icon {
    opacity: 1;
    transform: scale(1.1);
}

/* ===== MOBILE HAMBURGER MENU ===== */
.mobile-menu-toggle {
    display: none !important;
    position: relative;
}

.mobile-menu-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.mobile-menu-btn:hover {
    background: rgba(0, 255, 255, 0.08);
    border-color: rgba(0, 255, 255, 0.3);
}

.hamburger {
    width: 20px;
    height: 16px;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.hamburger span {
    width: 100%;
    height: 2px;
    background: var(--cyber-cyan);
    border-radius: 1px;
    transition: all 0.3s ease;
    transform-origin: center;
}

.hamburger.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.hamburger.active span:nth-child(2) {
    opacity: 0;
}

.hamburger.active span:nth-child(3) {
    transform: rotate(-45deg) translate(5px, -5px);
}

/* Mobile Menu Overlay */
.mobile-menu-overlay {
    position: fixed;
    top: var(--navbar-height, 80px);
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(26, 26, 26, 0.98) 0%,
        rgba(42, 42, 42, 0.95) 100%);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    z-index: 999;
    padding: 2rem;
    overflow-y: auto;
    border-top: 1px solid rgba(0, 255, 255, 0.2);
    box-shadow:
        0 -4px 20px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.05);

    /* Enhanced animation properties */
    opacity: 0;
    transform: translateY(-20px);
    transition: opacity 0.3s ease, transform 0.3s ease;
    will-change: opacity, transform;
}

.mobile-menu-overlay.opening {
    opacity: 1;
    transform: translateY(0);
}

.mobile-menu-overlay.closing {
    opacity: 0;
    transform: translateY(-20px);
}

.mobile-menu-content {
    max-width: 400px;
    margin: 0 auto;
}

.mobile-nav-section {
    margin-bottom: 2rem;
}

.mobile-nav-title {
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    font-size: 1.1rem;
    color: var(--cyber-cyan);
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.mobile-nav-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    margin-bottom: 0.5rem;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 14px;
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    min-height: 56px;
}

.mobile-nav-item:hover {
    background: rgba(0, 255, 255, 0.08);
    border-color: rgba(0, 255, 255, 0.2);
    color: var(--cyber-cyan);
    transform: translateX(4px);
}

.mobile-nav-item.featured {
    background: linear-gradient(135deg,
        rgba(0, 255, 255, 0.18) 0%,
        rgba(139, 92, 246, 0.18) 100%) !important;
    border: 2px solid rgba(0, 255, 255, 0.4) !important;
    color: var(--cyber-cyan) !important;
    font-weight: 700;
    position: relative;
    box-shadow:
        0 6px 20px rgba(0, 255, 255, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
}

.mobile-nav-item.featured::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: linear-gradient(135deg, var(--cyber-cyan), var(--cyber-purple));
    border-radius: 14px;
    z-index: -1;
    opacity: 0.4;
}

.mobile-nav-item.featured:hover {
    background: linear-gradient(135deg,
        rgba(0, 255, 255, 0.25) 0%,
        rgba(139, 92, 246, 0.25) 100%) !important;
    border-color: rgba(0, 255, 255, 0.6) !important;
    box-shadow:
        0 8px 25px rgba(0, 255, 255, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
    transform: translateX(8px);
}

.mobile-nav-icon {
    font-size: 1.2rem;
    width: 24px;
    text-align: center;
}

.mobile-auth-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.mobile-auth-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    padding: 1rem 1.5rem;
    border-radius: 14px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    min-height: 56px;
}

.mobile-auth-btn.secondary {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--text-secondary);
}

.mobile-auth-btn.secondary:hover {
    background: rgba(0, 255, 255, 0.08);
    border-color: rgba(0, 255, 255, 0.3);
    color: var(--cyber-cyan);
}

.mobile-auth-btn.primary {
    background: linear-gradient(135deg, var(--cyber-cyan), var(--cyber-purple));
    border: none;
    color: white;
    box-shadow: 0 4px 20px rgba(0, 255, 255, 0.3);
}

.mobile-auth-btn.primary:hover {
    box-shadow: 0 6px 30px rgba(0, 255, 255, 0.4);
    transform: translateY(-2px);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
    .navbar-container {
        padding: 0 1.5rem;
    }

    .navbar-nav {
        gap: 0.75rem;
        max-width: 750px;
    }

    .nav-item {
        min-width: 110px;
        padding: 0.75rem 1.125rem;
        font-size: 0.85rem;
    }

    .nav-item-featured {
        min-width: 160px;
    }

    .logo-text {
        font-size: 1.75rem; /* 28px */
    }

    .auth-btn {
        padding: 0.75rem 1rem;
        font-size: 0.8rem;
        max-width: 140px;
    }

    .user-button {
        min-width: 180px;
    }
}

/* ===== TABLET OPTIMIZATION (768px-992px) ===== */
@media (max-width: 992px) and (min-width: 769px) {
    .navbar-nav {
        gap: 0.75rem;
        max-width: 700px;
    }

    .nav-item {
        min-width: 80px;
        padding: 0.875rem 1rem;
        font-size: 0.85rem;
        flex-direction: column;
        gap: 0.375rem;
        min-height: 52px;
    }

    .nav-item-featured {
        min-width: 90px;
        padding: 0.875rem 1.125rem;
    }

    .nav-label {
        font-size: 0.75rem;
        font-weight: 600;
        text-align: center;
        line-height: 1.1;
    }

    .nav-icon {
        font-size: 1.5rem;
    }

    .nav-badge {
        top: -6px;
        right: -6px;
        font-size: 0.65rem;
        padding: 0.25rem 0.5rem;
    }

    .auth-btn {
        padding: 0.75rem 1rem;
        font-size: 0.8rem;
        max-width: 130px;
        min-height: 48px;
    }

    .user-button {
        min-width: 160px;
        padding: 0.75rem 1rem;
    }
}

@media (max-width: 992px) and (max-width: 768px) {
    .navbar-nav {
        gap: 0.5rem;
        max-width: 600px;
    }

    .nav-item {
        min-width: 60px;
        padding: 0.75rem 0.875rem;
        font-size: 0.8rem;
        flex-direction: column;
        gap: 0.25rem;
    }

    .nav-item-featured {
        min-width: 70px;
    }

    .nav-label {
        font-size: 0.7rem;
        font-weight: 500;
        text-align: center;
        line-height: 1;
    }

    .nav-icon {
        font-size: 1.4rem;
    }

    .nav-badge {
        top: -4px;
        right: -4px;
        font-size: 0.6rem;
        padding: 0.125rem 0.375rem;
    }

    .auth-btn {
        padding: 0.625rem 0.875rem;
        font-size: 0.75rem;
        max-width: 120px;
    }
}

@media (max-width: 768px) {
    :root {
        --navbar-height: 75px;
    }

    .onnyx-navbar {
        height: var(--navbar-height);
        top: 0;                                     /* Flush to top on mobile too */
    }

    body {
        padding-top: calc(var(--navbar-height) + var(--base-1)) !important;     /* 75px + 8px = 83px total */
    }

    .navbar-container {
        padding: 0 1rem;
        gap: 1rem;
    }

    .navbar-nav {
        display: none !important;
    }

    .mobile-menu-toggle {
        display: block !important;
        order: 2;
    }

    .navbar-auth {
        order: 3;
    }

    .logo-text {
        font-size: 1.5rem; /* 24px */
    }

    .logo-icon {
        width: 44px;
        height: 44px;
    }

    .logo-symbol {
        font-size: 1.75rem;
    }

    .auth-buttons {
        gap: 0.5rem;
    }

    .auth-btn {
        padding: 0.75rem 1rem;
        font-size: 0.8rem;
        min-width: auto;
        max-width: 140px;
    }

    .btn-icon {
        font-size: 0.85rem;
    }

    .user-button {
        min-width: 140px;
        padding: 0.75rem 1rem;
    }

    .user-details {
        display: none;
    }

    .user-menu {
        width: 300px;
        right: -0.5rem;
    }

    /* Enhanced mobile menu button */
    .mobile-menu-btn {
        width: 48px;
        height: 48px;
        background: rgba(0, 255, 255, 0.08);
        border: 1px solid rgba(0, 255, 255, 0.2);
    }

    .mobile-menu-btn:hover {
        background: rgba(0, 255, 255, 0.12);
        border-color: rgba(0, 255, 255, 0.4);
        box-shadow: 0 4px 15px rgba(0, 255, 255, 0.2);
    }
}

@media (max-width: 480px) {
    .navbar-container {
        padding: 0 0.75rem;
        gap: 0.75rem;
    }

    .logo-text {
        font-size: 1.25rem; /* 20px */
    }

    .logo-icon {
        width: 40px;
        height: 40px;
    }

    .logo-symbol {
        font-size: 1.5rem;
    }

    .auth-buttons {
        gap: 0.375rem;
    }

    .auth-btn {
        padding: 0.75rem 0.875rem;
        font-size: 0.75rem;
        gap: 0.375rem;
        min-height: 48px;
        max-width: 120px;
    }

    .auth-btn span:not(.btn-icon) {
        display: none;
    }

    .btn-icon {
        font-size: 1.1rem;
    }

    .user-button {
        min-width: auto;
        padding: 0.75rem;
        min-height: 48px;
    }

    .user-avatar {
        width: 36px;
        height: 36px;
    }

    .user-initial {
        font-size: 1rem;
    }

    .mobile-menu-overlay {
        padding: 1rem;
    }

    .mobile-nav-item {
        padding: 1rem 1.25rem;
        min-height: 56px;
    }

    .mobile-auth-btn {
        padding: 1rem 1.25rem;
        font-size: 0.9rem;
        min-height: 56px;
    }

    /* Enhanced mobile menu button for small screens */
    .mobile-menu-btn {
        width: 48px;
        height: 48px;
    }

    .hamburger {
        width: 22px;
        height: 18px;
    }

    .hamburger span {
        height: 2.5px;
    }
}

/* ===== EXTRA SMALL MOBILE (320px) ===== */
@media (max-width: 320px) {
    .navbar-container {
        padding: 0 0.5rem;
        gap: 0.5rem;
    }

    .logo-text {
        font-size: 1.125rem; /* 18px */
    }

    .logo-icon {
        width: 36px;
        height: 36px;
    }

    .logo-symbol {
        font-size: 1.3rem;
    }

    .auth-buttons {
        gap: 0.25rem;
    }

    .auth-btn {
        padding: 0.625rem 0.75rem;
        font-size: 0.7rem;
        min-height: 48px;
        max-width: 120px;
    }

    .btn-icon {
        font-size: 1rem;
    }

    .user-button {
        padding: 0.625rem;
        min-height: 44px;
    }

    .user-avatar {
        width: 32px;
        height: 32px;
    }

    .user-initial {
        font-size: 0.9rem;
    }

    .mobile-menu-btn {
        width: 44px;
        height: 44px;
    }

    .hamburger {
        width: 20px;
        height: 16px;
    }

    .mobile-menu-overlay {
        padding: 0.75rem;
    }

    .mobile-nav-item {
        padding: 0.875rem 1rem;
        min-height: 48px;
        font-size: 0.9rem;
    }

    .mobile-auth-btn {
        padding: 0.875rem 1rem;
        font-size: 0.85rem;
        min-height: 48px;
    }

    .mobile-nav-title {
        font-size: 1rem;
    }
}

/* ===== EDEN MODE ENHANCEMENTS ===== */

/* Eden Mode specific animations and effects */
/* REMOVED: Eden energy and prosperity streams replaced with standardized cyber-grid system */
/* These background effects have been replaced with the professional cyber-grid pattern
   and step-specific colored particles for consistency across all Eden Mode steps. */

.blockchain-matrix::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(0deg, transparent 24%, rgba(0, 255, 247, 0.05) 25%, rgba(0, 255, 247, 0.05) 26%, transparent 27%, transparent 74%, rgba(0, 255, 247, 0.05) 75%, rgba(0, 255, 247, 0.05) 76%, transparent 77%, transparent),
        linear-gradient(90deg, transparent 24%, rgba(0, 255, 136, 0.05) 25%, rgba(0, 255, 136, 0.05) 26%, transparent 27%, transparent 74%, rgba(0, 255, 136, 0.05) 75%, rgba(0, 255, 136, 0.05) 76%, transparent 77%, transparent);
    background-size: 50px 50px;
    animation: matrixMove 20s linear infinite;
}

@keyframes matrixMove {
    0% { transform: translateX(0) translateY(0); }
    100% { transform: translateX(50px) translateY(50px); }
}

.covenant-completion::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 50% 50%, rgba(0, 255, 136, 0.1) 0%, transparent 70%);
    animation: completionPulse 6s ease-in-out infinite;
}

@keyframes completionPulse {
    0%, 100% { opacity: 0.3; transform: scale(1); }
    50% { opacity: 0.8; transform: scale(1.2); }
}

/* REMOVED: Inconsistent background animations replaced with standardized cyber-grid system */
/* The following background effects have been standardized across all Eden Mode steps:
   - tribal-patterns (replaced with cyber-grid + purple particles)
   - constellation-map (replaced with cyber-grid + cyan particles)
   - covenant-energy (replaced with cyber-grid + green particles)
   - labor-streams (replaced with cyber-grid + flowing particles)

   All Eden Mode steps now use the professional cyber-grid background with
   step-specific particle colors for visual distinction while maintaining consistency.
*/

/* Eden Mode specific element styles */
.nation-card:hover, .labor-category:hover, .sela-path:hover, .sela-option:hover {
    transform: translateY(-1px);                     /* Further reduced for subtlety */
    box-shadow: 0 8px 20px rgba(0, 255, 247, 0.2);  /* Reduced shadow spread */
}

.nation-card.selected, .labor-category.selected, .sela-path.selected, .sela-option.selected {
    border: 2px solid var(--cyber-green);
    box-shadow: 0 0 25px rgba(0, 255, 136, 0.3);
    transform: translateY(-1px);                     /* Further reduced for subtlety */
}

.glow-text {
    text-shadow: 0 0 20px rgba(0, 255, 247, 0.5);
}

.glow-on-hover:hover {
    box-shadow: 0 0 30px rgba(0, 255, 247, 0.4);
}

.glow-on-reveal {
    transition: box-shadow 0.5s ease;
}

.floating-particle {
    position: absolute;
    width: 2px;
    height: 2px;
    border-radius: 50%;
    opacity: 0.6;
    animation: floatParticle 10s linear infinite;
}

@keyframes floatParticle {
    0% { transform: translateY(100vh) rotate(0deg); opacity: 0; }
    10% { opacity: 0.6; }
    90% { opacity: 0.6; }
    100% { transform: translateY(-100vh) rotate(360deg); opacity: 0; }
}

.loading-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.inscription-step {
    transition: opacity 0.5s ease;
}

.step-transition-overlay {
    pointer-events: none;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Eden Mode responsive enhancements */
@media (max-width: 768px) {
    .nation-card, .labor-category, .sela-path, .sela-option {
        margin-bottom: var(--base-unit);
    }

    /* Optimize background animations for mobile performance */
    .cyber-grid {
        background-size: 40px 40px; /* Smaller grid on mobile */
    }

    .blockchain-matrix::before, .covenant-completion::before {
        animation-duration: 15s; /* Slower on mobile for performance */
    }

    /* Reduce particle animations on mobile */
    .animate-ping, .animate-pulse, .animate-bounce {
        animation-duration: 3s; /* Slower animations for better performance */
    }
}

/* Accessibility: Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
    /* Disable all background animations for users who prefer reduced motion */
    .cyber-grid {
        animation: none;
    }

    .blockchain-matrix::before, .covenant-completion::before {
        animation: none;
    }

    .animate-ping, .animate-pulse, .animate-bounce {
        animation: none;
    }

    /* Keep static visual elements but remove motion */
    .floating-particle {
        animation: none;
        opacity: 0.3;
    }
}

/* ===== ANIMATIONS ===== */
@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* ===== ACCESSIBILITY & TOUCH TARGETS ===== */
/* Ensure ALL interactive elements meet minimum touch target requirements */
.nav-item,
.auth-btn,
.user-button,
.mobile-menu-btn,
.mobile-nav-item,
.mobile-auth-btn {
    min-height: 48px;
    min-width: 48px;
    touch-action: manipulation;
    -webkit-tap-highlight-color: rgba(0, 255, 255, 0.2);
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
}

@media (hover: none) and (pointer: coarse) {
    /* Touch device optimizations */
    .nav-item {
        min-height: 52px;
        padding: 1rem 1.5rem;
    }

    .auth-btn {
        min-height: 52px;
        padding: 1rem 1.25rem;
    }

    .user-button {
        min-height: 52px;
        padding: 1rem 1.25rem;
    }

    .mobile-menu-btn {
        width: 52px;
        height: 52px;
    }

    .mobile-nav-item {
        min-height: 56px;
        padding: 1.25rem 1.5rem;
    }

    .mobile-auth-btn {
        min-height: 56px;
        padding: 1.25rem 1.5rem;
    }

    /* Enhanced touch feedback */
    .nav-item:active,
    .auth-btn:active,
    .user-button:active,
    .mobile-menu-btn:active,
    .mobile-nav-item:active,
    .mobile-auth-btn:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
    }
}

/* ===== FOCUS STATES FOR ACCESSIBILITY ===== */
.nav-item:focus,
.auth-btn:focus,
.user-button:focus,
.mobile-menu-btn:focus,
.mobile-nav-item:focus,
.mobile-auth-btn:focus {
    outline: 2px solid var(--cyber-cyan);
    outline-offset: 2px;
}

/* ===== REDUCED MOTION SUPPORT ===== */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .logo-text {
        animation: none;
    }
}







/* ENHANCED COMPONENT SYSTEM */
/* Modern Button System */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-sm);
    padding: var(--space-md) var(--space-xl);
    border-radius: var(--radius-lg);
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    font-size: var(--font-size-base);
    text-decoration: none;
    border: 1px solid transparent;
    cursor: pointer;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
    min-height: 44px; /* Accessibility - minimum touch target */
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left var(--transition-normal);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--cyber-cyan), var(--cyber-purple));
    color: var(--onyx-black);
    box-shadow: var(--shadow-cyber);
    font-weight: 700;
}

.btn-primary:hover {
    background: linear-gradient(135deg,
        rgba(0, 212, 255, 0.9),
        rgba(139, 92, 246, 0.9));
    box-shadow:
        var(--shadow-cyber),
        0 8px 25px rgba(0, 212, 255, 0.4);
    transform: translateY(-2px);
    color: var(--onyx-black);
}

.btn-secondary {
    background: var(--glass-bg);
    color: var(--text-primary);
    border-color: var(--glass-border);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: var(--glass-hover);
    border-color: var(--cyber-cyan);
    color: var(--cyber-cyan);
    box-shadow: var(--shadow-cyber);
    transform: translateY(-2px);
}

.btn-outline {
    background: transparent;
    color: var(--cyber-cyan);
    border-color: var(--cyber-cyan);
}

.btn-outline:hover {
    background: var(--cyber-cyan);
    color: var(--onyx-black);
    box-shadow: var(--shadow-cyber);
    transform: translateY(-2px);
}

.btn-ghost {
    background: transparent;
    color: var(--text-secondary);
    border: none;
}

.btn-ghost:hover {
    background: var(--glass-bg);
    color: var(--cyber-cyan);
    transform: translateY(-1px);
}

.btn-sm {
    padding: var(--space-sm) var(--space-lg);
    font-size: var(--font-size-sm);
    min-height: 36px;
}

.btn-lg {
    padding: var(--space-lg) var(--space-2xl);
    font-size: var(--font-size-lg);
    min-height: 52px;
}

.btn-icon {
    padding: var(--space-md);
    min-width: 44px;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

.btn:disabled:hover {
    transform: none !important;
    box-shadow: none !important;
}

/* ===== PRECISION CARD SYSTEM ===== */
/* Mathematically precise card components with exact measurements */

.card {
    background: var(--glass-bg);
    backdrop-filter: blur(var(--base-2));           /* 16px blur */
    -webkit-backdrop-filter: blur(var(--base-2));
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);                /* 16px radius */
    padding: var(--card-padding);                   /* 24px padding */
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
    min-height: auto;                               /* Prevent stretching */
    height: auto;                                   /* Natural height */
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg,
        transparent,
        var(--cyber-cyan),
        transparent);
    opacity: 0;
    transition: var(--transition-normal);
}

.card:hover {
    background: var(--glass-hover);
    border-color: rgba(0, 212, 255, 0.3);
    transform: translateY(-1px);                    /* Reduced for subtlety */
    box-shadow:
        var(--shadow-xl),
        var(--shadow-cyber);
}

.card:hover::before {
    opacity: 1;
}

.card-header {
    margin-bottom: var(--space-3);                  /* 24px margin */
    padding-bottom: var(--space-3);                 /* 24px padding */
    border-bottom: 1px solid var(--glass-border);
}

.card-title {
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    font-size: var(--font-size-xl);                 /* 20px */
    color: var(--text-primary);
    margin: 0;
    line-height: 1.2;                               /* Precise line height */
}

.card-subtitle {
    font-size: var(--font-size-sm);                 /* 14px */
    color: var(--text-tertiary);
    margin: var(--space-1) 0 0 0;                   /* 8px top margin */
    line-height: 1.4;                               /* Precise line height */
}

.card-body {
    color: var(--text-secondary);
    line-height: 1.6;                               /* Optimal reading line height */
    margin: 0;                                      /* Remove default margins */
    padding: 0;                                     /* Remove default padding */
}

.card-footer {
    margin-top: var(--space-3);                     /* 24px margin */
    padding-top: var(--space-3);                    /* 24px padding */
    border-top: 1px solid var(--glass-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--space-2);                            /* 16px gap */
    min-height: var(--button-height);               /* 44px minimum for touch targets */
}

/* ===== PRECISION GRID SYSTEM ===== */
/* 12-column grid with exact measurements */

.grid-container {
    max-width: var(--container-xl);                 /* 1280px max width */
    margin: 0 auto;
    padding: 0 var(--space-3);                      /* 24px horizontal padding */
}

.grid {
    display: grid;
    gap: var(--grid-gap);                           /* 24px gap */
    align-items: start;                             /* Prevent stretching */
    grid-auto-rows: min-content;                    /* Natural row height */
}

.grid-cols-1 { grid-template-columns: 1fr; }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
.grid-cols-6 { grid-template-columns: repeat(6, 1fr); }
.grid-cols-12 { grid-template-columns: repeat(12, 1fr); }

/* Responsive grid columns */
@media (min-width: 768px) {
    .md\\:grid-cols-1 { grid-template-columns: 1fr; }
    .md\\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
    .md\\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
    .md\\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
    .md\\:grid-cols-6 { grid-template-columns: repeat(6, 1fr); }
}

@media (min-width: 1024px) {
    .lg\\:grid-cols-1 { grid-template-columns: 1fr; }
    .lg\\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
    .lg\\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
    .lg\\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
    .lg\\:grid-cols-6 { grid-template-columns: repeat(6, 1fr); }
}

/* Grid gap variations */
.gap-1 { gap: var(--space-1); }                    /* 8px */
.gap-2 { gap: var(--space-2); }                    /* 16px */
.gap-3 { gap: var(--space-3); }                    /* 24px */
.gap-4 { gap: var(--space-4); }                    /* 32px */
.gap-6 { gap: var(--space-6); }                    /* 48px */
.gap-8 { gap: var(--space-8); }                    /* 64px */

/* Responsive gaps */
@media (max-width: 767px) {
    .grid { gap: var(--grid-gap-sm); }              /* 16px on mobile */
}

@media (min-width: 1024px) {
    .grid { gap: var(--grid-gap-lg); }              /* 32px on desktop */
}

/* ===== PRECISION UTILITY CLASSES ===== */
/* Mathematical spacing, typography, and layout utilities */

/* ===== SPACING UTILITIES ===== */
/* Margin utilities - all based on 8px grid */
.m-0 { margin: 0; }
.m-1 { margin: var(--space-1); }                   /* 8px */
.m-2 { margin: var(--space-2); }                   /* 16px */
.m-3 { margin: var(--space-3); }                   /* 24px */
.m-4 { margin: var(--space-4); }                   /* 32px */
.m-5 { margin: var(--space-5); }                   /* 40px */
.m-6 { margin: var(--space-6); }                   /* 48px */
.m-8 { margin: var(--space-8); }                   /* 64px */
.m-10 { margin: var(--space-10); }                 /* 80px */
.m-12 { margin: var(--space-12); }                 /* 96px */

/* Margin top utilities */
.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--space-1); }
.mt-2 { margin-top: var(--space-2); }
.mt-3 { margin-top: var(--space-3); }
.mt-4 { margin-top: var(--space-4); }
.mt-5 { margin-top: var(--space-5); }
.mt-6 { margin-top: var(--space-6); }
.mt-8 { margin-top: var(--space-8); }
.mt-10 { margin-top: var(--space-10); }
.mt-12 { margin-top: var(--space-12); }

/* Margin bottom utilities */
.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--space-1); }
.mb-2 { margin-bottom: var(--space-2); }
.mb-3 { margin-bottom: var(--space-3); }
.mb-4 { margin-bottom: var(--space-4); }
.mb-5 { margin-bottom: var(--space-5); }
.mb-6 { margin-bottom: var(--space-6); }
.mb-8 { margin-bottom: var(--space-8); }
.mb-10 { margin-bottom: var(--space-10); }
.mb-12 { margin-bottom: var(--space-12); }

/* Margin horizontal utilities */
.mx-0 { margin-left: 0; margin-right: 0; }
.mx-1 { margin-left: var(--space-1); margin-right: var(--space-1); }
.mx-2 { margin-left: var(--space-2); margin-right: var(--space-2); }
.mx-3 { margin-left: var(--space-3); margin-right: var(--space-3); }
.mx-4 { margin-left: var(--space-4); margin-right: var(--space-4); }
.mx-auto { margin-left: auto; margin-right: auto; }

/* Margin vertical utilities */
.my-0 { margin-top: 0; margin-bottom: 0; }
.my-1 { margin-top: var(--space-1); margin-bottom: var(--space-1); }
.my-2 { margin-top: var(--space-2); margin-bottom: var(--space-2); }
.my-3 { margin-top: var(--space-3); margin-bottom: var(--space-3); }
.my-4 { margin-top: var(--space-4); margin-bottom: var(--space-4); }
.my-5 { margin-top: var(--space-5); margin-bottom: var(--space-5); }
.my-6 { margin-top: var(--space-6); margin-bottom: var(--space-6); }
.my-8 { margin-top: var(--space-8); margin-bottom: var(--space-8); }

/* Padding utilities - all based on 8px grid */
.p-0 { padding: 0; }
.p-1 { padding: var(--space-1); }                  /* 8px */
.p-2 { padding: var(--space-2); }                  /* 16px */
.p-3 { padding: var(--space-3); }                  /* 24px */
.p-4 { padding: var(--space-4); }                  /* 32px */
.p-5 { padding: var(--space-5); }                  /* 40px */
.p-6 { padding: var(--space-6); }                  /* 48px */
.p-8 { padding: var(--space-8); }                  /* 64px */
.p-10 { padding: var(--space-10); }                /* 80px */
.p-12 { padding: var(--space-12); }                /* 96px */

/* Padding top utilities */
.pt-0 { padding-top: 0; }
.pt-1 { padding-top: var(--space-1); }
.pt-2 { padding-top: var(--space-2); }
.pt-3 { padding-top: var(--space-3); }
.pt-4 { padding-top: var(--space-4); }
.pt-5 { padding-top: var(--space-5); }
.pt-6 { padding-top: var(--space-6); }
.pt-8 { padding-top: var(--space-8); }
.pt-10 { padding-top: var(--space-10); }
.pt-12 { padding-top: var(--space-12); }

/* Padding bottom utilities */
.pb-0 { padding-bottom: 0; }
.pb-1 { padding-bottom: var(--space-1); }
.pb-2 { padding-bottom: var(--space-2); }
.pb-3 { padding-bottom: var(--space-3); }
.pb-4 { padding-bottom: var(--space-4); }
.pb-5 { padding-bottom: var(--space-5); }
.pb-6 { padding-bottom: var(--space-6); }
.pb-8 { padding-bottom: var(--space-8); }
.pb-10 { padding-bottom: var(--space-10); }
.pb-12 { padding-bottom: var(--space-12); }

/* Padding horizontal utilities */
.px-0 { padding-left: 0; padding-right: 0; }
.px-1 { padding-left: var(--space-1); padding-right: var(--space-1); }
.px-2 { padding-left: var(--space-2); padding-right: var(--space-2); }
.px-3 { padding-left: var(--space-3); padding-right: var(--space-3); }
.px-4 { padding-left: var(--space-4); padding-right: var(--space-4); }
.px-5 { padding-left: var(--space-5); padding-right: var(--space-5); }
.px-6 { padding-left: var(--space-6); padding-right: var(--space-6); }
.px-8 { padding-left: var(--space-8); padding-right: var(--space-8); }

/* Padding vertical utilities */
.py-0 { padding-top: 0; padding-bottom: 0; }
.py-1 { padding-top: var(--space-1); padding-bottom: var(--space-1); }
.py-2 { padding-top: var(--space-2); padding-bottom: var(--space-2); }
.py-3 { padding-top: var(--space-3); padding-bottom: var(--space-3); }
.py-4 { padding-top: var(--space-4); padding-bottom: var(--space-4); }
.py-5 { padding-top: var(--space-5); padding-bottom: var(--space-5); }
.py-6 { padding-top: var(--space-6); padding-bottom: var(--space-6); }
.py-8 { padding-top: var(--space-8); padding-bottom: var(--space-8); }
.py-10 { padding-top: var(--space-10); padding-bottom: var(--space-10); }
.py-12 { padding-top: var(--space-12); padding-bottom: var(--space-12); }

/* ===== TYPOGRAPHY UTILITIES ===== */
/* Precise font sizes based on mathematical scale */
.text-xs { font-size: var(--font-size-xs); }       /* 12px */
.text-sm { font-size: var(--font-size-sm); }       /* 14px */
.text-base { font-size: var(--font-size-base); }   /* 16px */
.text-lg { font-size: var(--font-size-lg); }       /* 18px */
.text-xl { font-size: var(--font-size-xl); }       /* 20px */
.text-2xl { font-size: var(--font-size-2xl); }     /* 24px */
.text-3xl { font-size: var(--font-size-3xl); }     /* 30px */
.text-4xl { font-size: var(--font-size-4xl); }     /* 36px */
.text-5xl { font-size: var(--font-size-5xl); }     /* 48px */
.text-6xl { font-size: var(--font-size-6xl); }     /* 60px */
.text-7xl { font-size: var(--font-size-7xl); }     /* 72px */

/* Font weights */
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-extrabold { font-weight: 800; }
.font-black { font-weight: 900; }

/* Font families */
.font-orbitron { font-family: 'Orbitron', monospace; }
.font-montserrat { font-family: 'Montserrat', sans-serif; }

/* Text colors with precise values */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-muted { color: var(--text-muted); }
.text-cyber-cyan { color: var(--cyber-cyan); }
.text-cyber-purple { color: var(--cyber-purple); }
.text-cyber-blue { color: var(--cyber-blue); }
.text-cyber-green { color: var(--cyber-green); }
.text-cyber-red { color: var(--cyber-red); }
.text-cyber-yellow { color: var(--cyber-yellow); }

/* Text alignment */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

/* Line heights for optimal readability */
.leading-none { line-height: 1; }
.leading-tight { line-height: 1.25; }
.leading-snug { line-height: 1.375; }
.leading-normal { line-height: 1.5; }
.leading-relaxed { line-height: 1.625; }
.leading-loose { line-height: 2; }

/* ===== LAYOUT UTILITIES ===== */
/* Container utilities with precise max-widths */
.container-xs { max-width: var(--container-xs); margin: 0 auto; }    /* 480px */
.container-sm { max-width: var(--container-sm); margin: 0 auto; }    /* 640px */
.container-md { max-width: var(--container-md); margin: 0 auto; }    /* 768px */
.container-lg { max-width: var(--container-lg); margin: 0 auto; }    /* 1024px */
.container-xl { max-width: var(--container-xl); margin: 0 auto; }    /* 1280px */
.container-2xl { max-width: var(--container-2xl); margin: 0 auto; }  /* 1536px */

/* Width utilities */
.w-full { width: 100%; }
.w-auto { width: auto; }
.w-fit { width: fit-content; }
.w-screen { width: 100vw; }

/* Height utilities */
.h-full { height: 100%; }
.h-auto { height: auto; }
.h-fit { height: fit-content; }
.h-screen { height: 100vh; }
.h-nav { height: var(--nav-height); }               /* 64px navigation height */

/* Min/Max height utilities */
.min-h-0 { min-height: 0; }
.min-h-full { min-height: 100%; }
.min-h-screen { min-height: 100vh; }
.max-h-full { max-height: 100%; }
.max-h-screen { max-height: 100vh; }

/* Display utilities */
.block { display: block; }
.inline-block { display: inline-block; }
.inline { display: inline; }
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.grid { display: grid; }
.hidden { display: none; }

/* Flexbox utilities */
.flex-row { flex-direction: row; }
.flex-col { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }
.items-start { align-items: flex-start; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.items-stretch { align-items: stretch; }
.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.justify-evenly { justify-content: space-evenly; }

/* Border radius utilities with precise values */
.rounded-none { border-radius: var(--radius-none); }     /* 0px */
.rounded-sm { border-radius: var(--radius-sm); }         /* 4px */
.rounded { border-radius: var(--radius-md); }            /* 8px */
.rounded-lg { border-radius: var(--radius-lg); }         /* 12px */
.rounded-xl { border-radius: var(--radius-xl); }         /* 16px */
.rounded-2xl { border-radius: var(--radius-2xl); }       /* 24px */
.rounded-3xl { border-radius: var(--radius-3xl); }       /* 32px */
.rounded-full { border-radius: var(--radius-full); }     /* 9999px */

/* Shadow utilities with precise values */
.shadow-none { box-shadow: none; }
.shadow-xs { box-shadow: var(--shadow-xs); }
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }
.shadow-2xl { box-shadow: var(--shadow-2xl); }
.shadow-cyber { box-shadow: var(--shadow-cyber); }
.shadow-purple { box-shadow: var(--shadow-purple); }

/* Position utilities */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }
.static { position: static; }

/* Z-index utilities */
.z-0 { z-index: 0; }
.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }
.z-auto { z-index: auto; }

/* Overflow utilities */
.overflow-auto { overflow: auto; }
.overflow-hidden { overflow: hidden; }
.overflow-visible { overflow: visible; }
.overflow-scroll { overflow: scroll; }
.overflow-x-auto { overflow-x: auto; }
.overflow-y-auto { overflow-y: auto; }
.overflow-x-hidden { overflow-x: hidden; }
.overflow-y-hidden { overflow-y: hidden; }

/* MOBILE MENU OVERLAY */
.mobile-menu-overlay {
    position: fixed;
    top: 70px;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    z-index: 9998;
    overflow-y: auto;
}

.mobile-menu-items {
    padding: 2rem 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    max-width: 400px;
    margin: 0 auto;
}

.mobile-menu-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.mobile-menu-item:hover {
    background: rgba(0, 255, 255, 0.1);
    border-color: rgba(0, 255, 255, 0.3);
    color: var(--cyber-cyan);
    transform: translateX(8px);
}

.mobile-menu-item.primary {
    background: linear-gradient(135deg, var(--cyber-cyan), var(--cyber-purple));
    color: var(--onyx-black);
    border-color: transparent;
    font-weight: 600;
}

.mobile-menu-item.primary:hover {
    background: linear-gradient(135deg,
        rgba(0, 255, 255, 0.9),
        rgba(139, 92, 246, 0.9));
    transform: translateX(8px) scale(1.02);
}

.mobile-menu-item.logout {
    background: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.3);
    color: #ef4444;
}

.mobile-menu-item.logout:hover {
    background: rgba(239, 68, 68, 0.2);
    border-color: rgba(239, 68, 68, 0.5);
    color: #f87171;
}

.mobile-menu-divider {
    height: 1px;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.2) 50%,
        transparent 100%);
    margin: 1rem 0;
}

/* ENHANCED FORM SYSTEM */
.form-group {
    margin-bottom: var(--space-lg);
}

.form-label {
    display: block;
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    font-size: var(--font-size-sm);
    color: var(--text-primary);
    margin-bottom: var(--space-sm);
    letter-spacing: 0.025em;
}

.form-label.required::after {
    content: ' *';
    color: var(--cyber-red);
}

.form-control {
    width: 100%;
    padding: var(--space-md) var(--space-lg);
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    color: var(--text-primary);
    font-family: 'Montserrat', sans-serif;
    font-size: var(--font-size-base);
    transition: var(--transition-normal);
    min-height: 44px; /* Accessibility */
}

.form-control:focus {
    outline: none;
    border-color: var(--cyber-cyan);
    box-shadow:
        0 0 0 3px rgba(0, 212, 255, 0.1),
        var(--shadow-cyber);
    background: var(--glass-hover);
}

.form-control::placeholder {
    color: var(--text-muted);
    opacity: 1;
}

.form-control:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background: rgba(255, 255, 255, 0.02);
}

.form-control.error {
    border-color: var(--cyber-red);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-control.success {
    border-color: var(--cyber-green);
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.form-help {
    font-size: var(--font-size-sm);
    color: var(--text-tertiary);
    margin-top: var(--space-xs);
}

.form-error {
    font-size: var(--font-size-sm);
    color: var(--cyber-red);
    margin-top: var(--space-xs);
    display: flex;
    align-items: center;
    gap: var(--space-xs);
}

.form-success {
    font-size: var(--font-size-sm);
    color: var(--cyber-green);
    margin-top: var(--space-xs);
    display: flex;
    align-items: center;
    gap: var(--space-xs);
}

/* Enhanced Select Styling */
.form-select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%2300d4ff' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right var(--space-md) center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: var(--space-3xl);
    color: #ffffff !important; /* Force white text */
    opacity: 1 !important; /* Force full opacity */
}

.form-select option {
    background: var(--onyx-black) !important;
    color: #ffffff !important;
    padding: 8px 12px;
    border: none;
}

.form-select:focus option {
    background: var(--onyx-gray) !important;
    color: #ffffff !important;
}

/* Additional select styling for better visibility */
select {
    color: #ffffff !important;
    background: var(--glass-bg) !important;
}

select option {
    background: var(--onyx-black) !important;
    color: #ffffff !important;
    padding: 8px 12px;
}

/* Ensure all select elements have proper text color */
.glass-select,
.form-select,
select {
    color: #ffffff !important;
    text-shadow: none !important;
}

/* Checkbox and Radio Styling */
.form-check {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    margin-bottom: var(--space-md);
}

.form-check-input {
    width: 20px;
    height: 20px;
    background: var(--glass-bg);
    border: 2px solid var(--glass-border);
    border-radius: var(--radius-sm);
    transition: var(--transition-normal);
    cursor: pointer;
    position: relative;
    flex-shrink: 0;
}

.form-check-input:checked {
    background: var(--cyber-cyan);
    border-color: var(--cyber-cyan);
    box-shadow: var(--shadow-cyber);
}

.form-check-input:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--onyx-black);
    font-weight: bold;
    font-size: 12px;
}

.form-check-input[type="radio"] {
    border-radius: 50%;
}

.form-check-input[type="radio"]:checked::after {
    content: '';
    width: 8px;
    height: 8px;
    background: var(--onyx-black);
    border-radius: 50%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.form-check-label {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    cursor: pointer;
    user-select: none;
}

/* LAYOUT UTILITIES */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--space-xl);
}

.container-sm {
    max-width: 640px;
}

.container-md {
    max-width: 768px;
}

.container-lg {
    max-width: 1024px;
}

.container-xl {
    max-width: 1280px;
}

.grid {
    display: grid;
    gap: var(--space-xl);
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

.flex {
    display: flex;
}

.flex-col {
    flex-direction: column;
}

.items-center {
    align-items: center;
}

.justify-center {
    justify-content: center;
}

.justify-between {
    justify-content: space-between;
}

.gap-sm { gap: var(--space-sm); }
.gap-md { gap: var(--space-md); }
.gap-lg { gap: var(--space-lg); }
.gap-xl { gap: var(--space-xl); }

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-sm { margin-bottom: var(--space-sm); }
.mb-md { margin-bottom: var(--space-md); }
.mb-lg { margin-bottom: var(--space-lg); }
.mb-xl { margin-bottom: var(--space-xl); }
.mb-2xl { margin-bottom: var(--space-2xl); }

.mt-sm { margin-top: var(--space-sm); }
.mt-md { margin-top: var(--space-md); }
.mt-lg { margin-top: var(--space-lg); }
.mt-xl { margin-top: var(--space-xl); }
.mt-2xl { margin-top: var(--space-2xl); }

/* SPINNER ANIMATION */
.spinner {
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-left: 2px solid var(--cyber-cyan);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Custom scrollbar with cyber theme */
.custom-scrollbar::-webkit-scrollbar {
    width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: var(--onyx-gray);
    border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, var(--cyber-cyan), var(--cyber-purple));
    border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, var(--cyber-purple), var(--cyber-cyan));
}

/* Responsive utilities */
@media (max-width: 640px) {
    .mobile-hidden {
        display: none;
    }

    .glass-card {
        margin: 8px;
        padding: 16px;
    }

    .hero-gradient {
        background-size: 200% 200%;
    }
}

@media (min-width: 641px) {
    .mobile-only {
        display: none;
    }
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }

    .print-break {
        page-break-after: always;
    }

    body {
        background: white !important;
        color: black !important;
    }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .glass-card {
        border: 2px solid white;
    }

    .glass-button {
        border: 2px solid var(--cyber-cyan);
    }
}

/* MOBILE NAVIGATION IMPROVEMENTS - PART 2A SOLUTION */
.mobile-menu-toggle {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 60;
    width: 24px;
    height: 24px;
    background: var(--glass-bg);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid var(--glass-border);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.mobile-menu-toggle:hover {
    background: rgba(255, 255, 255, 0.12);
    border-color: var(--cyber-cyan);
    box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);
}

.mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(26, 26, 26, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    z-index: 55;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease-in-out;
}

.mobile-menu-overlay.active {
    opacity: 1;
    visibility: visible;
}

.mobile-menu-items {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 3rem;
    text-align: center;
}

.mobile-menu-item {
    font-family: 'Orbitron', monospace;
    font-size: 1.5rem;
    font-weight: 500;
    color: var(--text-primary);
    text-decoration: none;
    padding: 1rem 2rem;
    border-radius: 12px;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    transition: all 0.3s ease;
    min-width: 200px;
}

.mobile-menu-item:hover {
    color: var(--cyber-cyan);
    background: rgba(0, 212, 255, 0.1);
    border-color: var(--cyber-cyan);
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
    transform: translateY(-2px);
}

/* Hamburger animation */
.hamburger {
    width: 18px;
    height: 18px;
    position: relative;
    cursor: pointer;
}

.hamburger span {
    display: block;
    position: absolute;
    height: 2px;
    width: 100%;
    background: var(--text-primary);
    border-radius: 1px;
    opacity: 1;
    left: 0;
    transform: rotate(0deg);
    transition: 0.3s ease-in-out;
}

.hamburger span:nth-child(1) {
    top: 0px;
}

.hamburger span:nth-child(2) {
    top: 7px;
}

.hamburger span:nth-child(3) {
    top: 14px;
}

.hamburger.active span:nth-child(1) {
    top: 7px;
    transform: rotate(135deg);
}

.hamburger.active span:nth-child(2) {
    opacity: 0;
    left: -60px;
}

.hamburger.active span:nth-child(3) {
    top: 7px;
    transform: rotate(-135deg);
}

/* VISUAL CONSISTENCY FIXES - COMPREHENSIVE SOLUTION */
/* Fix opacity inconsistencies and ensure proper footer positioning */

/* OPACITY STANDARDIZATION - Remove reduced opacity from all components */
/* All interactive elements should have full opacity for better visibility */

/* Glass card components - Enhanced visibility */
.glass-card {
    background: var(--glass-bg);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid var(--glass-border);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    opacity: 1 !important; /* Force full opacity */
}

.glass-card:hover {
    background: rgba(255, 255, 255, 0.12); /* Increased from 0.1 */
    border-color: rgba(0, 255, 255, 0.4); /* Increased from 0.3 */
    box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.5),
        0 0 20px rgba(0, 255, 255, 0.3), /* Increased from 0.2 */
        inset 0 1px 0 rgba(255, 255, 255, 0.2); /* Increased from 0.15 */
    opacity: 1 !important; /* Force full opacity */
}

/* Button components - Full opacity */
.glass-button {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    transition: all 0.3s ease;
    opacity: 1 !important; /* Force full opacity */
}

.glass-button:hover {
    background: rgba(255, 255, 255, 0.15); /* Increased from 0.12 */
    border-color: var(--cyber-cyan);
    box-shadow:
        0 0 25px rgba(0, 255, 255, 0.5), /* Increased from 0.4 */
        0 0 50px rgba(0, 255, 255, 0.3), /* Increased from 0.2 */
        inset 0 1px 0 rgba(255, 255, 255, 0.25); /* Increased from 0.2 */
    transform: translateY(-2px);
    opacity: 1 !important; /* Force full opacity */
}

.glass-button-primary {
    background: linear-gradient(135deg, rgba(0, 255, 255, 0.3), rgba(154, 0, 255, 0.3)); /* Increased from 0.25 */
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid var(--cyber-cyan);
    color: var(--cyber-cyan);
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.6); /* Increased from 0.5 */
    transition: all 0.3s ease;
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.3); /* Increased from 0.2 */
    opacity: 1 !important; /* Force full opacity */
}

.glass-button-primary:hover {
    background: linear-gradient(135deg, rgba(0, 255, 255, 0.4), rgba(154, 0, 255, 0.4)); /* Increased from 0.35 */
    box-shadow:
        0 0 30px rgba(0, 255, 255, 0.6), /* Increased from 0.5 */
        0 0 60px rgba(0, 255, 255, 0.4), /* Increased from 0.3 */
        inset 0 1px 0 rgba(255, 255, 255, 0.3); /* Increased from 0.2 */
    transform: translateY(-2px) scale(1.02);
    text-shadow: 0 0 15px rgba(0, 255, 255, 0.8); /* Increased from 0.7 */
    opacity: 1 !important; /* Force full opacity */
}

/* Navigation elements - Fully transparent */
.glass-nav {
    background: transparent !important;             /* Fully transparent */
    backdrop-filter: none !important;              /* No blur */
    -webkit-backdrop-filter: none !important;
    border-bottom: none !important;                /* No border line */
    opacity: 1 !important;                         /* Force full opacity */
}

/* Text elements - Enhanced visibility */
.text-text-primary {
    color: var(--text-primary);
    opacity: 1 !important; /* Force full opacity */
}

.text-text-secondary {
    color: var(--text-secondary);
    opacity: 1 !important; /* Force full opacity */
}

.text-text-tertiary {
    color: var(--text-tertiary);
    opacity: 1 !important; /* Force full opacity */
}

/* FORCE FULL OPACITY FOR ALL COMPONENTS - COMPREHENSIVE FIX */
/* Remove ALL opacity reductions from decorative elements */
.opacity-70 { opacity: 1 !important; }
.opacity-60 { opacity: 1 !important; }
.opacity-50 { opacity: 1 !important; } /* Changed from 0.8 to 1.0 */
.opacity-40 { opacity: 1 !important; } /* Changed from 0.7 to 1.0 */
.opacity-30 { opacity: 1 !important; }
.opacity-20 { opacity: 1 !important; }
.opacity-10 { opacity: 1 !important; }

/* Force full opacity on all Tailwind opacity classes */
.opacity-0 { opacity: 0 !important; } /* Keep hidden elements hidden */
.opacity-5 { opacity: 1 !important; }
.opacity-25 { opacity: 1 !important; }
.opacity-75 { opacity: 1 !important; }
.opacity-90 { opacity: 1 !important; }
.opacity-95 { opacity: 1 !important; }
.opacity-100 { opacity: 1 !important; }

/* HERO SECTION OPACITY FIXES - FORCE FULL VISIBILITY */
.hero-section,
.hero-content,
.hero-title,
.hero-subtitle,
.hero-description,
.hero-stats,
.hero-buttons,
.hero-gradient {
    opacity: 1 !important;
}

/* TEXT ELEMENT OPACITY FIXES - ENSURE ALL TEXT IS FULLY VISIBLE */
h1, h2, h3, h4, h5, h6,
p, span, div, a,
.text-lg, .text-xl, .text-2xl, .text-3xl, .text-4xl, .text-5xl,
.text-sm, .text-xs,
.text-gray-300, .text-gray-400, .text-gray-500,
.text-white, .text-primary, .text-secondary {
    opacity: 1 !important;
}

/* COMPONENT OPACITY FIXES - ALL INTERACTIVE ELEMENTS */
.card, .stats-card, .feature-card,
.button, .btn, .glass-button,
.nav-link, .menu-item,
.logo, .icon,
.badge, .tag, .label,
.input, .form-control,
.modal, .popup, .tooltip {
    opacity: 1 !important;
}

/* BACKGROUND AND DECORATIVE ELEMENT FIXES */
.bg-gradient, .gradient-bg,
.particle, .floating-element,
.decoration, .accent,
.border, .divider, .separator {
    opacity: 1 !important;
}

/* ANIMATION AND TRANSITION OPACITY OVERRIDES */
.fade-in, .slide-in, .zoom-in,
.animate-pulse, .animate-ping, .animate-bounce,
.transition-opacity, .transition-all {
    opacity: 1 !important;
}

/* COMPREHENSIVE ELEMENT OPACITY ENFORCEMENT */
/* Force full opacity on all common element types */
* {
    /* Override any inline opacity styles */
}

/* Specific element type overrides */
div, section, article, main, header, footer, nav,
h1, h2, h3, h4, h5, h6, p, span, a, button,
img, svg, canvas, video, iframe,
form, input, textarea, select, label,
table, tr, td, th, thead, tbody, tfoot,
ul, ol, li, dl, dt, dd {
    /* Ensure no element has reduced opacity unless explicitly hidden */
}

/* Override any JavaScript-applied opacity styles */
[style*="opacity"] {
    opacity: 1 !important;
}

/* Exception for completely hidden elements */
[style*="opacity: 0"],
[style*="opacity:0"],
.opacity-0,
.hidden,
[hidden] {
    opacity: 0 !important;
}

/* CRITICAL FOOTER POSITIONING FIXES - EMERGENCY OVERRIDE */
/* Force proper page layout with footer that never overlaps content */

/* Root layout setup for proper flexbox structure */
html {
    height: 100% !important;
    scroll-behavior: smooth;
    scroll-padding-top: calc(var(--nav-height) + var(--base-1)); /* 64px + 8px = 72px total */
}

body {
    height: 100% !important;
    min-height: 100vh !important;
    display: flex !important;
    flex-direction: column !important;
    margin: 0 !important;
    padding: 0 !important;
    padding-top: calc(var(--nav-height) + var(--base-1)) !important; /* 64px + 8px = 72px total */
    position: relative !important;
}

/* CRITICAL: Force footer to bottom */
footer.footer-responsive {
    flex-shrink: 0 !important;
    margin-top: auto !important;
    position: relative !important;
    width: 100% !important;
    z-index: 10 !important;
    background: var(--onyx-black) !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* CRITICAL: Force main content wrapper to expand properly */
div[style*="flex: 1 0 auto"] {
    flex: 1 0 auto !important;
    display: flex !important;
    flex-direction: column !important;
    min-height: calc(100vh - 64px) !important;
    position: relative !important;
}

/* CRITICAL: Force main content area to expand within wrapper */
main[style*="flex: 1 0 auto"] {
    flex: 1 0 auto !important;
    width: 100% !important;
    position: relative !important;
    z-index: 1 !important;
    min-height: calc(100vh - 200px) !important;
}

/* Legacy support for old classes */
.flex-1 {
    flex: 1 0 auto !important;
    display: flex !important;
    flex-direction: column !important;
    min-height: calc(100vh - 64px) !important;
    position: relative !important;
}

main {
    flex: 1 0 auto !important;
    width: 100% !important;
    position: relative !important;
    z-index: 1 !important;
}

/* CRITICAL: Fix page-specific content wrappers that break footer positioning */
.directory-content,
.explorer-content,
.dashboard-content,
.auth-content {
    min-height: auto !important;
    height: auto !important;
    flex: none !important;
    display: block !important;
}

/* Ensure page content doesn't interfere with footer positioning */
.directory-content.hero-gradient,
.explorer-content.hero-gradient,
.dashboard-content.hero-gradient,
.auth-content.hero-gradient {
    padding-bottom: 3rem !important;
    margin-bottom: 0 !important;
}

/* CRITICAL: Override any conflicting height/min-height rules on page content */
.hero-gradient {
    min-height: auto !important;
    height: auto !important;
}

/* Ensure cyber-grid covers full viewport including navbar area */
.cyber-grid {
    position: relative !important;
    z-index: 1 !important;
}

/* Fix cyber-grid to extend to top of viewport */
.cyber-grid::before {
    content: '';
    position: absolute;
    top: -70px; /* Extend above the content to cover navbar area */
    left: 0;
    right: 0;
    height: 70px; /* Height of navbar */
    background-image:
        linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    opacity: 0.3;
    z-index: -1;
}

/* ===== FINAL ROADMAP POSITIONING OVERRIDE ===== */
/* This MUST be the last CSS rule to ensure it takes precedence */
/* Nuclear option to prevent ANY system from overriding roadmap positioning */

.roadmap-timeline-node[class*="absolute"][class*="left-1/2"][class*="transform"] {
    transform: translateX(-12px) !important;
    position: absolute !important;
    left: 50% !important;
    z-index: 20 !important;
}

/* Sacred Economics specific fixes */
.tokenomics-phase-card .flex[class*="items-center"][class*="space-x"] {
    align-items: flex-start !important;
    gap: 1.5rem !important;
}

/* Prevent JavaScript from applying any transforms to roadmap cards */
.roadmap-card-animated[style*="transform"],
.roadmap-card-active[style*="transform"],
.roadmap-card-enhanced[style*="transform"] {
    transform: none !important;
}

/* Force proper spacing for all page content sections */
.py-20 {
    padding-top: 1rem !important;                   /* Further reduced top padding for tighter spacing */
    padding-bottom: 1rem !important;                /* Reduced bottom padding */
}

/* CRITICAL: Fix min-h-screen that breaks footer positioning */
.min-h-screen {
    min-height: auto !important;
    height: auto !important;
}

/* Specific fix for auth pages that use min-h-screen */
.min-h-screen.hero-gradient {
    min-height: auto !important;
    height: auto !important;
    display: block !important;
    flex: none !important;
}

/* TARGETED FIX: Only target page wrapper divs, not content containers */
/* Target only the main page content wrappers that interfere with footer */
main > div.directory-content,
main > div.explorer-content,
main > div.dashboard-content,
main > div.auth-content,
main > div.min-h-screen {
    min-height: auto !important;
    height: auto !important;
    flex: none !important;
}

/* Ensure main page wrapper has proper bottom spacing for footer */
main > div:last-child.directory-content,
main > div:last-child.explorer-content,
main > div:last-child.dashboard-content,
main > div:last-child.auth-content {
    margin-bottom: 0 !important;
    padding-bottom: 1rem !important;                /* Reduced bottom padding */
}

/* CRITICAL: AGGRESSIVE ANTI-STRETCH SOLUTION FOR GLASS-CARD COMPONENTS */
/* This is the comprehensive fix for vertical stretching issues */

/* STEP 1: Force all glass-card components to natural height */
.glass-card {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
    flex: none !important;
    flex-grow: 0 !important;
    flex-shrink: 0 !important;
    align-self: start !important;
    justify-self: stretch !important;
    display: block !important;
}

/* STEP 2: Fix CSS Grid containers that cause stretching */
.grid {
    align-items: start !important;
    align-content: start !important;
    grid-auto-rows: auto !important;
}

/* STEP 3: Target all grid children that contain glass-cards */
.grid > *,
.grid > .glass-card,
.grid > div > .glass-card,
.grid > * > * > .glass-card {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
    flex: none !important;
    align-self: start !important;
    justify-self: stretch !important;
}

/* STEP 4: Fix flexbox containers that stretch children */
.flex,
.flex-col,
.flex-row {
    align-items: flex-start !important;
}

.flex > .glass-card,
.flex-col > .glass-card,
.flex-row > .glass-card,
.flex > * > .glass-card,
.flex-col > * > .glass-card {
    height: auto !important;
    min-height: auto !important;
    flex: none !important;
    align-self: flex-start !important;
}

/* STEP 5: Target specific component classes */
.validator-card,
.search-panel,
.explorer-search,
.stats-card,
.tech-card,
.network-card {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
    flex: none !important;
    align-self: start !important;
}

/* CRITICAL FIX: Validator Card Vertical Stretching Issue - NUCLEAR OPTION */
/* Specific fix for the Sela Validator Directory page cards */

/* EMERGENCY OVERRIDE: Force all cards in validator grid to natural height */
#validators-grid .card,
#validators-grid .validator-card,
.validator-card.card,
.validator-card {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
    display: block !important;
    align-self: start !important;
    flex: none !important;
    flex-grow: 0 !important;
    flex-shrink: 0 !important;
    flex-basis: auto !important;
}

/* Force card internal structure to not stretch */
#validators-grid .card .card-header,
#validators-grid .card .card-body,
#validators-grid .card .card-footer,
.validator-card .card-header,
.validator-card .card-body,
.validator-card .card-footer {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
    flex: none !important;
    display: block !important;
}

/* Override any Tailwind or other CSS that might be stretching */
#validators-grid > div {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
    align-self: start !important;
    justify-self: stretch !important;
}

/* ULTIMATE NUCLEAR OPTION: Target the exact page and override everything */
/* This is the most aggressive fix possible for the validator directory page */
.directory-content #validators-grid {
    display: grid !important;
    grid-template-columns: repeat(1, 1fr) !important;
    gap: 2rem !important;
    align-items: start !important;
    align-content: start !important;
    grid-auto-rows: min-content !important;
    grid-auto-flow: row !important;
}

@media (min-width: 768px) {
    .directory-content #validators-grid {
        grid-template-columns: repeat(2, 1fr) !important;
    }
}

@media (min-width: 1024px) {
    .directory-content #validators-grid {
        grid-template-columns: repeat(3, 1fr) !important;
    }
}

/* Force every single element in the validator grid to natural height */
.directory-content #validators-grid > *,
.directory-content #validators-grid > * > *,
.directory-content #validators-grid .card,
.directory-content #validators-grid .validator-card {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
    align-self: start !important;
    justify-self: stretch !important;
    flex: none !important;
    flex-grow: 0 !important;
    flex-shrink: 0 !important;
    flex-basis: auto !important;
    display: block !important;
}

/* ULTIMATE TAILWIND OVERRIDE - Target specific Tailwind classes */
/* This is the most aggressive fix to override Tailwind CSS grid behavior */
.grid.grid-cols-1.md\:grid-cols-2.lg\:grid-cols-3.gap-8 {
    align-items: start !important;
    align-content: start !important;
    grid-auto-rows: min-content !important;
    grid-auto-flow: row !important;
}

.grid.grid-cols-1.md\:grid-cols-2.lg\:grid-cols-3.gap-8 > * {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
    align-self: start !important;
    justify-self: stretch !important;
}

/* Target the exact card classes used in the template */
.card.group.data-stream.validator-card {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
    align-self: flex-start !important;
    display: block !important;
    flex: none !important;
    flex-grow: 0 !important;
    flex-shrink: 0 !important;
}

/* FLEXBOX LAYOUT FIX - Replace problematic grid with flexbox */
/* This is the definitive solution for validator card stretching */
#validators-grid.flex.flex-wrap {
    display: flex !important;
    flex-wrap: wrap !important;
    align-items: flex-start !important;
    align-content: flex-start !important;
    justify-content: flex-start !important;
    gap: 2rem !important;
}

#validators-grid.flex.flex-wrap > .validator-card {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
    align-self: flex-start !important;
    flex: none !important;
    flex-grow: 0 !important;
    flex-shrink: 0 !important;
    flex-basis: auto !important;
}

/* ULTIMATE NUCLEAR OPTION - OVERRIDE ALL POSSIBLE FRAMEWORKS */
/* This targets Bootstrap, Tailwind, and any other CSS that might cause equal heights */

/* Bootstrap Card Override */
.card-deck .card,
.card-group .card,
.row .card,
.d-flex .card,
.flex .card,
.grid .card {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
    flex: none !important;
    align-self: flex-start !important;
}

/* Bootstrap Grid Override */
.row > [class*="col-"] .card,
.row > [class*="col-"] .validator-card {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
}

/* Tailwind Flexbox Override */
.flex > .card,
.flex > .validator-card,
.flex-wrap > .card,
.flex-wrap > .validator-card {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
    align-self: flex-start !important;
    flex: none !important;
}

/* CSS Grid Override for any framework */
.grid > .card,
.grid > .validator-card,
[class*="grid"] > .card,
[class*="grid"] > .validator-card {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
    align-self: start !important;
    grid-row: auto !important;
}

/* SPECIFIC VALIDATOR DIRECTORY OVERRIDE */
/* This is the most specific possible selector */
.directory-content #validators-grid .card.group.data-stream.validator-card {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
    align-self: flex-start !important;
    flex: none !important;
    flex-grow: 0 !important;
    flex-shrink: 0 !important;
    flex-basis: auto !important;
    display: block !important;
}

/* CRITICAL FIX: Validator Grid Layout - FLEXBOX ONLY */
/* Remove conflicting grid layout that causes equal heights */
#validators-grid {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 2rem !important;
    align-items: flex-start !important;
    align-content: flex-start !important;
    justify-content: flex-start !important;
}

/* Remove grid-based media queries that conflict with flexbox */
/* Cards will use responsive width classes instead */

/* Ensure flexbox children don't stretch */
#validators-grid > .validator-card {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
    align-self: flex-start !important;
    flex: none !important;
    flex-grow: 0 !important;
    flex-shrink: 0 !important;
    flex-basis: auto !important;
}

/* COMPACT VALIDATOR CARD LAYOUT - REDUCE EXCESSIVE SPACING */
/* This is the key fix for the vertical stretching issue */

/* Reduce card padding for validator cards specifically */
.validator-card.card {
    padding: 1rem !important; /* Reduced from 2rem to 1rem */
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
}

/* Compact card sections */
.validator-card .card-header {
    margin-bottom: 0.75rem !important; /* Reduced from 1.5rem */
    padding-bottom: 0.75rem !important; /* Reduced from 1.5rem */
}

.validator-card .card-body {
    padding: 0 !important; /* Remove extra padding */
    margin: 0 !important;
}

.validator-card .card-footer {
    margin-top: 0.75rem !important; /* Reduced from 1.5rem */
    padding-top: 0.75rem !important; /* Reduced from 1.5rem */
}

/* Compact spacing for validator card content */
.validator-card .card-body .space-y-4 {
    display: flex !important;
    flex-direction: column !important;
    gap: 0.5rem !important; /* Reduced from 1rem */
    height: auto !important;
}

.validator-card .card-body .space-y-4 > * {
    flex: none !important;
    height: auto !important;
    min-height: auto !important;
    margin: 0 !important;
}

/* Fix for mining stats grid within validator cards */
.validator-card .grid.grid-cols-2 {
    display: grid !important;
    grid-template-columns: 1fr 1fr !important;
    gap: 0.5rem !important; /* Reduced gap */
    align-items: start !important;
    height: auto !important;
    min-height: auto !important;
    margin-top: 0.5rem !important;
    padding-top: 0.5rem !important;
}

.validator-card .grid.grid-cols-2 > div {
    height: auto !important;
    min-height: auto !important;
    align-self: start !important;
    padding: 0.25rem !important; /* Minimal padding */
}

/* STEP 6: Fix space-y utility classes */
.space-y-4 > *,
.space-y-6 > *,
.space-y-8 > *,
.space-y-12 > * {
    height: auto !important;
    min-height: auto !important;
    flex: none !important;
}

/* VALIDATOR CARD HOVER ANIMATION FIX */
/* Reduce excessive movement and ensure proper button sizing */
.validator-card:hover {
    transform: translateY(-1px) !important; /* Minimal movement */
    transition: all 0.2s ease !important; /* Faster, smoother transition */
}

/* Card footer button improvements */
.validator-card .card-footer {
    display: flex !important;
    gap: 0.75rem !important;
    align-items: center !important;
    justify-content: space-between !important;
}

.validator-card .card-footer .glass-button-primary {
    flex: 1 !important;
    max-width: none !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    padding: 0.5rem 1rem !important;
    font-size: 0.875rem !important;
}

.validator-card .card-footer .glass-button {
    flex-shrink: 0 !important;
    width: auto !important;
    min-width: 2.5rem !important;
    padding: 0.5rem !important;
}

/* STEP 7: Target Tailwind CSS grid classes specifically */
.grid-cols-1 > *,
.grid-cols-2 > *,
.grid-cols-3 > *,
.grid-cols-4 > *,
.md\\:grid-cols-2 > *,
.md\\:grid-cols-3 > *,
.lg\\:grid-cols-3 > *,
.lg\\:grid-cols-4 > * {
    height: auto !important;
    min-height: auto !important;
    align-self: start !important;
}

/* CRITICAL FIX: Why Transparency Matters Section Layout */
/* Specific override for the explorer page transparency section */
.glass-card-enhanced .grid.grid-cols-1.lg\\:grid-cols-2 {
    display: grid !important;
    grid-template-columns: 1fr !important;
    gap: 3rem !important;
    align-items: start !important;
    align-content: start !important;
    grid-auto-rows: auto !important;
    width: 100% !important;
    overflow: visible !important;
}

@media (min-width: 1024px) {
    .glass-card-enhanced .grid.grid-cols-1.lg\\:grid-cols-2 {
        grid-template-columns: 1fr 1fr !important;
    }
}

/* Ensure column content doesn't overflow */
.glass-card-enhanced .grid.grid-cols-1.lg\\:grid-cols-2 > div {
    width: 100% !important;
    max-width: 100% !important;
    overflow: hidden !important;
    word-wrap: break-word !important;
    hyphens: auto !important;
    height: auto !important;
    min-height: auto !important;
    align-self: start !important;
    justify-self: stretch !important;
    position: relative !important;
    z-index: 1 !important;
}

/* Fix text and content within columns */
.glass-card-enhanced .grid.grid-cols-1.lg\\:grid-cols-2 h3,
.glass-card-enhanced .grid.grid-cols-1.lg\\:grid-cols-2 h4,
.glass-card-enhanced .grid.grid-cols-1.lg\\:grid-cols-2 p,
.glass-card-enhanced .grid.grid-cols-1.lg\\:grid-cols-2 .space-y-6,
.glass-card-enhanced .grid.grid-cols-1.lg\\:grid-cols-2 .flex {
    width: 100% !important;
    max-width: 100% !important;
    overflow: visible !important;
    word-wrap: break-word !important;
    position: relative !important;
    z-index: 2 !important;
}

/* Ensure flex items within columns stay contained */
.glass-card-enhanced .grid.grid-cols-1.lg\\:grid-cols-2 .flex.items-start {
    flex-wrap: nowrap !important;
    align-items: flex-start !important;
    gap: 1rem !important;
    width: 100% !important;
    max-width: 100% !important;
}

/* Fix icon containers */
.glass-card-enhanced .grid.grid-cols-1.lg\\:grid-cols-2 .flex-shrink-0 {
    flex-shrink: 0 !important;
    width: 3rem !important;
    height: 3rem !important;
    min-width: 3rem !important;
    min-height: 3rem !important;
}

/* Fix text content containers */
.glass-card-enhanced .grid.grid-cols-1.lg\\:grid-cols-2 .flex > div:not(.flex-shrink-0) {
    flex: 1 !important;
    min-width: 0 !important;
    max-width: calc(100% - 4rem) !important;
    overflow: hidden !important;
    word-wrap: break-word !important;
}

/* STEP 8: Ensure containers don't force height on children */
.container > *,
.max-w-7xl > *,
.mx-auto > * {
    height: auto !important;
    min-height: auto !important;
}

/* STEP 9: NUCLEAR OPTION - Override any remaining stretch behavior */
/* This targets any element that might be causing stretching */
[class*="grid"] > [class*="glass"],
[class*="flex"] > [class*="glass"],
div[class*="grid"] > div,
div[class*="flex"] > div {
    height: auto !important;
    min-height: auto !important;
    flex: none !important;
    align-self: start !important;
}

/* STEP 10: Specific fixes for common layout patterns */
.py-20 .grid > *,
.py-16 .grid > *,
.space-y-8 .grid > *,
.space-y-6 .grid > * {
    height: auto !important;
    min-height: auto !important;
}

/* STEP 11: Force block display for glass-card content */
.glass-card > *,
.glass-card .p-6,
.glass-card .p-8,
.glass-card .p-4 {
    display: block !important;
    height: auto !important;
    min-height: auto !important;
}

/* Content spacing adjustments - simplified for new structure */
/* Main content already has proper padding from pt-16 and pb-16 classes */

/* REMOVED: Conflicting min-height rules that cause stretching */
/* These rules were causing the vertical stretching issue */

/* Ensure long content pages scroll properly */
.content-wrapper {
    flex: 1;
    overflow-y: auto;
    position: relative;
}

/* ===== ENHANCED PRECISION FOOTER SYSTEM ===== */
/* Mathematically precise footer with enhanced visual hierarchy and cyberpunk effects */

.footer-responsive {
    padding: var(--space-6) var(--space-4);        /* 48px vertical, 32px horizontal - reduced spacing */
    background: var(--onyx-black);
    border-top: 1px solid var(--glass-border);
    position: relative;
    z-index: 10;
    margin-top: auto;
    flex-shrink: 0;
}

.footer-grid {
    display: grid;
    grid-template-columns: 1fr;                     /* Mobile-first: single column */
    gap: var(--space-3);                            /* 24px gap - reduced for mobile */
    max-width: var(--container-xl);                 /* 1280px max width */
    margin: 0 auto;
    align-items: stretch;                           /* Equal height sections */
    grid-auto-rows: 1fr;                            /* Equal row heights */
    width: 100%;                                    /* Full width */
    padding: 0 var(--space-3);                      /* 24px horizontal padding */
}

.footer-section {
    background: var(--glass-bg);
    backdrop-filter: blur(var(--base-2));           /* 16px blur */
    -webkit-backdrop-filter: blur(var(--base-2));
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);                /* 16px radius */
    padding: var(--space-4);                        /* 32px padding */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    height: 100%;                                   /* Equal height */
    min-height: 280px;                              /* Reduced for better balance */
    margin-bottom: var(--space-3);                  /* 24px bottom margin for mobile stacking */
    display: flex;
    flex-direction: column;
    justify-content: flex-start;                    /* Align content to top */
}

.footer-section:hover {
    background: var(--glass-hover);
    border-color: rgba(0, 212, 255, 0.6);          /* Enhanced border glow */
    box-shadow:
        var(--shadow-cyber),
        0 0 var(--space-3) rgba(0, 212, 255, 0.3), /* 24px cyber glow */
        0 0 var(--space-6) rgba(139, 92, 246, 0.2); /* 48px purple glow */
    transform: translateY(-2px);                    /* Reduced lift for subtlety */
}

.footer-section h3 {
    color: var(--cyber-cyan);
    font-family: 'Orbitron', monospace;
    font-weight: 700;                               /* Consistent weight */
    font-size: var(--font-size-lg);                 /* 18px consistent size */
    margin-bottom: var(--space-3);                  /* 24px consistent spacing */
    text-shadow: 0 0 var(--base-2) rgba(0, 212, 255, 0.6); /* Enhanced glow */
    letter-spacing: 0.5px;                          /* Improved readability */
    display: flex;
    align-items: center;
    gap: var(--base-unit);                          /* 8px gap between emoji and text */
    flex-shrink: 0;                                 /* Don't shrink header */
}

/* Ensure footer section content fills available space */
.footer-section ul {
    flex: 1;                                        /* Fill remaining space */
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    gap: var(--space-1);                            /* 8px consistent gap */
}

/* Consistent spacing for all footer content */
.footer-section > div:not(.flex) {
    flex: 1;                                        /* Fill remaining space */
    display: flex;
    flex-direction: column;
    gap: var(--space-1);                            /* 8px consistent gap */
}

.footer-link {
    display: flex;
    align-items: center;
    padding: var(--space-2) var(--space-2);         /* 16px padding all around */
    border-radius: var(--radius-lg);                /* 12px radius */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    min-height: var(--button-height);               /* 44px touch target */
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: 500;
    gap: var(--base-unit);                          /* 8px gap - precise alignment */
    position: relative;
    overflow: hidden;
}

.footer-link:hover {
    background: rgba(0, 212, 255, 0.15);
    color: var(--cyber-cyan);
    transform: translateX(var(--space-1));          /* 8px slide */
    box-shadow:
        0 0 var(--base-2) rgba(0, 212, 255, 0.4),   /* 16px cyan glow */
        0 0 var(--base-4) rgba(139, 92, 246, 0.2);  /* 32px purple glow */
    text-shadow: 0 0 var(--base-unit) rgba(0, 212, 255, 0.6); /* Text glow */
}

/* Neon effect animation for footer links */
.footer-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(0, 212, 255, 0.2) 50%,
        transparent 100%);
    transition: left 0.5s ease;
    z-index: -1;
}

.footer-link:hover::before {
    left: 100%;
}

.footer-stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-2) 0;                      /* 16px vertical padding */
    min-height: var(--button-height);               /* 44px touch target */
    border-bottom: 1px solid var(--glass-border);
}

.footer-stat-item:last-child {
    border-bottom: none;
}

.footer-stat-item .stat-label {
    color: var(--text-tertiary);
    font-weight: 500;
    font-size: var(--font-size-sm);                 /* 14px */
}

.footer-stat-item .stat-value {
    color: var(--cyber-cyan);
    font-family: 'Orbitron', monospace;
    font-weight: 600;
    font-size: var(--font-size-base);               /* 16px */
    text-shadow: 0 0 var(--base-unit) rgba(0, 212, 255, 0.4); /* 8px glow */
}

.footer-tech-item {
    display: flex;
    align-items: center;
    padding: var(--space-2) 0;                      /* 16px vertical padding */
    min-height: var(--button-height);               /* 44px touch target */
    gap: var(--base-unit);                          /* 8px gap - precise alignment */
    transition: all 0.3s ease;
    border-radius: var(--radius-md);                /* 8px radius */
    padding-left: var(--space-2);                   /* 16px left padding */
    padding-right: var(--space-2);                  /* 16px right padding */
}

.footer-tech-item:hover {
    background: rgba(0, 212, 255, 0.1);
    transform: translateX(var(--space-1));          /* 8px slide */
}

.footer-tech-dot {
    width: var(--base-unit);                        /* 8px size */
    height: var(--base-unit);
    border-radius: 50%;
    box-shadow: 0 0 var(--base-2) currentColor;     /* 16px glow */
    flex-shrink: 0;                                 /* Prevent shrinking */
}

.footer-tech-label {
    color: var(--text-secondary);
    font-weight: 500;
    font-size: var(--font-size-sm);                 /* 14px */
}

/* Precision Footer Bottom Section */
.footer-bottom {
    margin-top: var(--space-4);                     /* 32px margin - reduced */
    padding-top: var(--space-3);                    /* 24px padding - reduced */
    border-top: 1px solid var(--glass-border);
    text-align: center;
    max-width: var(--container-xl);                 /* Align with grid */
    margin-left: auto;
    margin-right: auto;
    padding-left: var(--space-3);                   /* 24px horizontal padding */
    padding-right: var(--space-3);
}

.footer-bottom p {
    color: var(--text-muted);
    font-size: var(--font-size-sm);                 /* 14px */
    margin-bottom: var(--space-2);                  /* 16px margin */
}

.footer-network-status {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);                            /* 16px gap - reduced */
    margin-top: var(--space-2);                     /* 16px margin */
    flex-wrap: wrap;                                /* Allow wrapping on small screens */
}

.footer-network-indicator {
    display: flex;
    align-items: center;
    gap: var(--base-unit);                          /* 8px gap - precise alignment */
    color: var(--text-secondary);                   /* Better contrast */
    font-size: var(--font-size-xs);                 /* 12px - smaller for hierarchy */
    padding: var(--space-1) var(--space-2);         /* 8px vertical, 16px horizontal */
    border-radius: var(--radius-md);                /* 8px radius */
    background: rgba(0, 212, 255, 0.05);            /* Subtle cyan background */
    border: 1px solid rgba(0, 212, 255, 0.2);       /* Cyan border */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 500;
    font-family: 'Orbitron', monospace;             /* Consistent with theme */
}

.footer-network-indicator:hover {
    background: rgba(0, 212, 255, 0.15);           /* Enhanced cyan background */
    border-color: var(--cyber-cyan);
    box-shadow: 0 0 var(--base-2) rgba(0, 212, 255, 0.4); /* 16px glow */
    color: var(--cyber-cyan);
    transform: scale(1.05);                         /* Subtle scale effect */
}

.footer-network-dot {
    width: var(--base-unit);                        /* 8px size */
    height: var(--base-unit);
    background: var(--cyber-green);
    border-radius: 50%;
    animation: networkPulse 2s ease-in-out infinite;
    box-shadow:
        0 0 var(--base-unit) rgba(16, 185, 129, 0.6), /* 8px inner glow */
        0 0 var(--base-2) rgba(16, 185, 129, 0.3);   /* 16px outer glow */
    flex-shrink: 0;                                 /* Prevent shrinking */
}

/* Enhanced network pulse animation */
@keyframes networkPulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
        box-shadow:
            0 0 var(--base-unit) rgba(16, 185, 129, 0.6),
            0 0 var(--base-2) rgba(16, 185, 129, 0.3);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.2);
        box-shadow:
            0 0 var(--base-2) rgba(16, 185, 129, 0.8),
            0 0 var(--base-3) rgba(16, 185, 129, 0.4);
    }
}

/* ===== BLOCKCHAIN-THEMED ANIMATIONS ===== */

/* Blockchain data flow animation */
@keyframes blockchainFlow {
    0% {
        transform: translateX(-100%);
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Cryptographic hash animation */
@keyframes cryptoHash {
    0%, 100% {
        text-shadow:
            0 0 var(--base-unit) rgba(0, 212, 255, 0.6),
            0 0 var(--base-2) rgba(139, 92, 246, 0.3);
    }
    25% {
        text-shadow:
            0 0 var(--base-2) rgba(0, 212, 255, 0.8),
            0 0 var(--base-3) rgba(139, 92, 246, 0.5);
    }
    50% {
        text-shadow:
            0 0 var(--base-unit) rgba(139, 92, 246, 0.8),
            0 0 var(--base-2) rgba(0, 212, 255, 0.5);
    }
    75% {
        text-shadow:
            0 0 var(--base-2) rgba(139, 92, 246, 0.6),
            0 0 var(--base-3) rgba(0, 212, 255, 0.3);
    }
}

/* Network connectivity animation */
@keyframes networkConnect {
    0% {
        opacity: 0.3;
        transform: scale(0.8);
    }
    50% {
        opacity: 1;
        transform: scale(1.1);
    }
    100% {
        opacity: 0.3;
        transform: scale(0.8);
    }
}

/* Blockchain validation animation */
@keyframes blockValidation {
    0%, 100% {
        border-color: rgba(0, 212, 255, 0.3);
        box-shadow: 0 0 var(--base-unit) rgba(0, 212, 255, 0.2);
    }
    33% {
        border-color: rgba(139, 92, 246, 0.5);
        box-shadow: 0 0 var(--base-2) rgba(139, 92, 246, 0.3);
    }
    66% {
        border-color: rgba(16, 185, 129, 0.5);
        box-shadow: 0 0 var(--base-2) rgba(16, 185, 129, 0.3);
    }
}

/* RESPONSIVE DESIGN IMPROVEMENTS - ENHANCED MOBILE EXPERIENCE */
/* Mobile responsive adjustments */
@media (max-width: 768px) {
    /* Main content adjustments for mobile */
    main {
        min-height: calc(100vh - 64px);
    }

    /* Mobile blockchain data formatting */
    .metric-large {
        font-size: 2rem;
    }

    .metric-medium {
        font-size: 1.5rem;
    }

    .metric-small {
        font-size: 1rem;
    }

    /* Mobile status indicators */
    .status-indicator {
        font-size: 0.625rem;
        padding: 0.125rem 0.5rem;
    }

    /* Mobile grid adjustments */
    .grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .grid.grid-cols-2 {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }

    .grid.grid-cols-3 {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .grid.grid-cols-4 {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }

    /* Precision Mobile Footer Adjustments */
    .footer-responsive {
        padding: var(--space-6) var(--space-3);     /* 48px vertical, 24px horizontal */
    }

    .footer-grid {
        grid-template-columns: 1fr;
        gap: var(--space-3);                        /* 24px gap */
    }

    .footer-section {
        padding: var(--space-4);                    /* 32px padding */
        border-radius: var(--radius-lg);            /* 12px radius */
    }

    .footer-section h3 {
        font-size: var(--font-size-lg);             /* 18px */
        margin-bottom: var(--space-2);              /* 16px margin */
    }

    .footer-link {
        padding: var(--space-2) var(--space-3);     /* 16px vertical, 24px horizontal */
        min-height: var(--button-height);           /* 44px minimum */
    }

    .footer-stat-item,
    .footer-tech-item {
        min-height: var(--button-height);           /* 44px minimum */
        padding: var(--space-2) 0;                  /* 16px vertical */
    }

    /* Mobile navigation improvements */
    .glass-button,
    .glass-button-primary {
        min-height: 52px;
        padding: 1rem 1.5rem;
        font-size: 0.875rem;
    }

    /* Mobile glass card improvements */
    .glass-card,
    .glass-card-enhanced {
        margin: 0.5rem;
        padding: 1.5rem;
        border-radius: 12px;
    }

    /* Mobile table responsiveness */
    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .table-responsive table {
        min-width: 600px;
    }

    /* Mobile hash display */
    .metric-hash {
        font-size: 0.75rem;
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    /* Mobile validator cards */
    .validator-card {
        width: 100% !important;
        margin-bottom: 1rem;
    }

    /* Mobile blockchain explorer adjustments */
    .explorer-search {
        flex-direction: column;
        gap: 1rem;
    }

    .explorer-search input {
        width: 100%;
    }
}

/* Precision Tablet Responsive Adjustments */
@media (min-width: 768px) and (max-width: 1024px) {
    .footer-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--space-4);                        /* 32px gap */
    }

    .footer-section {
        padding: var(--space-5);                    /* 40px padding */
    }

    .footer-section h3 {
        font-size: var(--font-size-xl);             /* 20px */
        margin-bottom: var(--space-3);              /* 24px margin */
    }
}

/* Tablet Responsive Adjustments */
@media (min-width: 768px) {
    .footer-grid {
        grid-template-columns: repeat(2, 1fr);      /* 2 columns on tablet */
        gap: var(--space-3);                        /* 24px gap */
    }

    .footer-section {
        margin-bottom: 0;                           /* Remove bottom margin */
        min-height: 280px;                          /* Consistent height on tablet */
    }
}

/* Desktop Responsive Adjustments */
@media (min-width: 1024px) {
    .footer-grid {
        grid-template-columns: repeat(4, 1fr);      /* 4 columns on desktop */
        gap: var(--space-4);                        /* 32px gap */
    }

    .footer-section {
        padding: var(--space-4);                    /* Consistent 32px padding */
        min-height: 280px;                          /* Consistent height on desktop */
    }

    .footer-section h3 {
        font-size: var(--font-size-lg);             /* Consistent 18px */
        margin-bottom: var(--space-3);              /* 24px margin */
    }
}

/* Precision Large Desktop Adjustments */
@media (min-width: 1440px) {
    .footer-grid {
        max-width: var(--container-xl);             /* 1280px max width */
    }

    .footer-section {
        padding: var(--space-6);                    /* 48px padding for large screens */
    }

    .footer-section h3 {
        font-size: var(--font-size-2xl);            /* 24px for large screens */
        margin-bottom: var(--space-4);              /* 32px margin */
    }
}

/* ACCESSIBILITY IMPROVEMENTS */
/* Enhanced focus states for better keyboard navigation */
.footer-link:focus,
.glass-button:focus,
.glass-button-primary:focus {
    outline: 2px solid var(--cyber-cyan);
    outline-offset: 2px;
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.4);
}

/* BLOCKCHAIN DATA FORMATTING STANDARDS - ONNYX PLATFORM */
/* Consistent formatting for all blockchain metrics and data displays */

/* 1. STANDARDIZED NUMBER FORMATTING */
.metric-value {
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    line-height: 1.2;
    letter-spacing: 0.05em;
}

.metric-large {
    font-size: 2.5rem;
}

.metric-medium {
    font-size: 1.875rem;
}

.metric-small {
    font-size: 1.25rem;
}

.metric-currency::before {
    content: '';
    font-size: 0.8em;
    opacity: 0.8;
}

.metric-percentage::after {
    content: '%';
    font-size: 0.8em;
    opacity: 0.8;
}

.metric-hash {
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    word-break: break-all;
    opacity: 0.9;
}

/* 2. BLOCKCHAIN STATUS INDICATORS */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-active {
    background: rgba(34, 197, 94, 0.2);
    color: #22c55e;
    border: 1px solid rgba(34, 197, 94, 0.3);
}

.status-pending {
    background: rgba(251, 191, 36, 0.2);
    color: #fbbf24;
    border: 1px solid rgba(251, 191, 36, 0.3);
}

.status-inactive {
    background: rgba(107, 114, 128, 0.2);
    color: #9ca3af;
    border: 1px solid rgba(107, 114, 128, 0.3);
}

.status-error {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

/* 3. LOADING STATES FOR BLOCKCHAIN DATA */
.metric-loading {
    background: linear-gradient(90deg,
        rgba(255, 255, 255, 0.1) 25%,
        rgba(255, 255, 255, 0.2) 50%,
        rgba(255, 255, 255, 0.1) 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: 4px;
    height: 1.5em;
    min-width: 4rem;
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* 4. ENHANCED GLASS MORPHISM CONSISTENCY */
.glass-card-enhanced {
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        0 0 20px rgba(0, 212, 255, 0.05);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.glass-card-enhanced:hover {
    background: rgba(255, 255, 255, 0.12);
    border-color: rgba(0, 212, 255, 0.3);
    box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.5),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.15),
        0 0 30px rgba(0, 212, 255, 0.15);
    transform: translateY(-2px);
}

/* ENHANCED SCROLL EFFECTS SYSTEM - ONNYX PLATFORM */
/* Comprehensive scroll experience with cyber-themed animations */

/* 1. SMOOTH SCROLL TRANSITIONS WITH CUSTOM EASING */
html {
    scroll-behavior: smooth;
    scroll-padding-top: 80px;
}

/* Enhanced smooth scrolling with momentum */
@media (prefers-reduced-motion: no-preference) {
    html {
        scroll-behavior: smooth;
    }

    /* Custom easing for scroll transitions */
    * {
        scroll-behavior: smooth;
        scroll-margin-top: 80px;
    }
}

/* Respect user preferences for reduced motion */
@media (prefers-reduced-motion: reduce) {
    html {
        scroll-behavior: auto;
    }

    * {
        scroll-behavior: auto;
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 2. CYBER-THEMED SCROLL PROGRESS BAR */
.scroll-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 0%;
    height: 3px;
    background: linear-gradient(90deg, var(--cyber-cyan), var(--cyber-purple));
    z-index: 9999;
    transition: width 0.1s ease-out;
    box-shadow:
        0 0 10px var(--cyber-cyan),
        0 0 20px rgba(0, 255, 255, 0.5),
        0 0 30px rgba(0, 255, 255, 0.3);
}

.scroll-progress::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 20px;
    height: 100%;
    background: linear-gradient(90deg, transparent, var(--cyber-cyan));
    animation: progressGlow 2s ease-in-out infinite alternate;
}

@keyframes progressGlow {
    0% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* 3. CONTENT FADE-IN ANIMATIONS */
.scroll-animate {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.scroll-animate.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Glass-card specific animations */
.glass-card.scroll-animate {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.glass-card.scroll-animate.visible {
    opacity: 1;
    transform: translateY(0) scale(1);
}

/* Staggered animation for grid items */
.grid .glass-card.scroll-animate {
    transition-delay: calc(var(--animation-order, 0) * 0.1s);
}

/* 4. PROGRESSIVE BLUR EFFECT */
.blur-on-scroll {
    transition: filter 0.3s ease-out;
}

.blur-on-scroll.blurred {
    filter: blur(2px);
    opacity: 0.7;
}

/* 5. SECTION-BASED SCROLL SNAPPING */
.scroll-snap-container {
    scroll-snap-type: y proximity;
    scroll-behavior: smooth;
}

.scroll-snap-section {
    scroll-snap-align: start;
    scroll-snap-stop: normal;
}

/* Major sections get gentle snapping */
.hero-section,
.core-technologies,
.validator-network,
.footer-responsive {
    scroll-snap-align: start;
}

/* 6. ENHANCED PARALLAX EFFECTS (SUBTLE) */
@media (prefers-reduced-motion: no-preference) {
    .parallax-subtle {
        transform: translateZ(0);
        will-change: transform;
    }

    /* Floating particles with enhanced movement */
    .floating-particle {
        animation: floatEnhanced 6s ease-in-out infinite;
    }

    @keyframes floatEnhanced {
        0%, 100% {
            transform: translateY(0px) rotate(0deg);
            opacity: 0.7;
        }
        25% {
            transform: translateY(-10px) rotate(90deg);
            opacity: 1;
        }
        50% {
            transform: translateY(-5px) rotate(180deg);
            opacity: 0.8;
        }
        75% {
            transform: translateY(-15px) rotate(270deg);
            opacity: 0.9;
        }
    }
}

/* 7. SCROLL-TRIGGERED GLOW EFFECTS */
.glow-on-scroll {
    transition: all 0.3s ease;
}

.glow-on-scroll.in-view {
    box-shadow:
        0 0 20px rgba(0, 255, 255, 0.3),
        0 0 40px rgba(0, 255, 255, 0.2),
        inset 0 0 20px rgba(0, 255, 255, 0.1);
}

/* 8. ENHANCED BUTTON HOVER STATES WITH SCROLL CONTEXT */
.glass-button.scroll-enhanced:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow:
        0 10px 30px rgba(0, 0, 0, 0.3),
        0 0 30px var(--cyber-cyan),
        0 0 60px rgba(0, 255, 255, 0.3);
}

/* 9. SCROLL-BASED NAVIGATION ENHANCEMENTS */
.nav-scroll-enhanced {
    transition: all 0.3s ease;
}

.nav-scroll-enhanced.scrolled {
    background: rgba(26, 26, 26, 0.95);
    backdrop-filter: blur(20px);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
}

/* 10. PERFORMANCE OPTIMIZATIONS */
.scroll-optimized {
    will-change: transform, opacity;
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* High contrast mode improvements */
@media (prefers-contrast: high) {
    .footer-section {
        border: 2px solid var(--cyber-cyan);
        background: rgba(0, 0, 0, 0.8);
    }

    .footer-link {
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .footer-link:hover {
        border-color: var(--cyber-cyan);
        background: rgba(0, 212, 255, 0.2);
    }
}

/* Accessibility: Respect reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    .footer-section,
    .footer-section::before,
    .footer-section h3,
    .footer-tech-dot,
    .footer-tech-dot::after,
    .footer-network-dot {
        animation: none !important;
        transition: none !important;
    }

    .footer-section:hover {
        transform: none !important;
    }

    .footer-link:hover {
        transform: none !important;
    }

    .footer-tech-item:hover {
        transform: none !important;
    }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    .footer-section,
    .footer-link,
    .glass-card,
    .glass-button,
    .glass-button-primary {
        transition: none !important;
        animation: none !important;
    }

    .footer-network-dot {
        animation: none !important;
    }

    /* Disable all logo animations for accessibility */
    .onnyx-hero-logo,
    .onnyx-nav-logo,
    .onnyx-page-logo,
    .onnyx-secondary-logo {
        animation: none !important;
        transition: none !important;
    }

    .onnyx-hero-logo {
        filter: drop-shadow(0 0 20px rgba(0, 212, 255, 0.4)) !important;
    }

    .onnyx-nav-logo {
        filter: drop-shadow(0 0 8px rgba(0, 212, 255, 0.3)) !important;
    }

    .onnyx-page-logo {
        filter: drop-shadow(0 0 15px rgba(0, 212, 255, 0.4)) !important;
    }

    .onnyx-secondary-logo {
        filter: drop-shadow(0 0 5px rgba(0, 212, 255, 0.2)) !important;
    }

    .onnyx-hero-logo:hover,
    .onnyx-nav-logo:hover,
    .onnyx-page-logo:hover,
    .onnyx-secondary-logo:hover {
        animation: none !important;
        transform: none !important;
    }

    .onnyx-hero-logo::before,
    .onnyx-hero-logo::after {
        animation: none !important;
        opacity: 0 !important;
    }
}
