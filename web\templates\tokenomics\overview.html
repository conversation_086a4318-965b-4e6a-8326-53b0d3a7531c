{% extends "base.html" %}

{% block title %}Biblical Tokenomics - Onnyx Platform{% endblock %}

{% block head %}
<style>
    .biblical-gradient {
        background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 50%, #1a1a1a 100%);
    }

    .sabbath-glow {
        box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
        border: 1px solid rgba(255, 215, 0, 0.5);
    }

    .deed-score-bar {
        background: linear-gradient(90deg, #00fff7 0%, #9a00ff 100%);
        height: 8px;
        border-radius: 4px;
        transition: all 0.3s ease;
    }

    .principle-card {
        transition: all 0.3s ease;
        border: 1px solid rgba(0, 255, 247, 0.2);
    }

    .principle-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0, 255, 247, 0.2);
        border-color: rgba(0, 255, 247, 0.5);
    }

    .verse-text {
        font-style: italic;
        color: #d1d5db;
        border-left: 3px solid #00fff7;
        padding-left: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen biblical-gradient">
    <!-- Hero Section -->
    <div class="relative overflow-hidden">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
            <!-- Sabbath Status Banner -->
            {% if stats.is_sabbath %}
            <div class="sabbath-glow glass-card p-6 mb-8 text-center">
                <div class="flex items-center justify-center space-x-3 mb-4">
                    <span class="text-4xl">🕊️</span>
                    <h2 class="text-2xl font-orbitron font-bold text-yellow-300">Sabbath Period Active</h2>
                    <span class="text-4xl">🕊️</span>
                </div>
                <p class="text-yellow-200">
                    "Remember the Sabbath day, to keep it holy." - Exodus 20:8<br>
                    Mining operations are paused during this sacred time of rest.
                </p>
            </div>
            {% endif %}

            <!-- Main Hero -->
            <div class="text-center mb-16">
                <h1 class="text-5xl md:text-7xl font-orbitron font-bold mb-6">
                    <span class="bg-gradient-to-r from-cyber-cyan via-cyber-purple to-cyber-blue bg-clip-text text-transparent">
                        Biblical Tokenomics
                    </span>
                </h1>
                <p class="text-xl md:text-2xl text-gray-300 mb-8 max-w-4xl mx-auto">
                    Ancient wisdom meets modern blockchain technology. Experience economic justice through
                    biblical principles integrated into the Onnyx platform.
                </p>
                <div class="verse-text text-lg max-w-3xl mx-auto mb-8">
                    "She considers a field and buys it; out of her earnings she plants a vineyard." - Proverbs 31:16
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    {% if current_user %}
                    <a href="{{ url_for('tokenomics.dashboard') }}"
                       class="px-8 py-4 bg-cyber-cyan text-onyx-black font-semibold rounded-xl hover:bg-cyber-cyan/90 transition-all duration-300 transform hover:scale-105">
                        <span class="mr-2">📊</span>My Tokenomics Dashboard
                    </a>
                    {% else %}
                    <a href="{{ url_for('auth.login') }}"
                       class="px-8 py-4 bg-cyber-cyan text-onyx-black font-semibold rounded-xl hover:bg-cyber-cyan/90 transition-all duration-300 transform hover:scale-105">
                        <span class="mr-2">🔑</span>Access Your Dashboard
                    </a>
                    {% endif %}
                    <a href="{{ url_for('tokenomics.education') }}"
                       class="px-8 py-4 border-2 border-cyber-purple text-cyber-purple font-semibold rounded-xl hover:bg-cyber-purple hover:text-white transition-all duration-300">
                        <span class="mr-2">📚</span>Learn Biblical Principles
                    </a>
                    <a href="{{ url_for('tokenomics.calculator') }}"
                       class="px-8 py-4 border-2 border-cyber-blue text-cyber-blue font-semibold rounded-xl hover:bg-cyber-blue hover:text-white transition-all duration-300">
                        <span class="mr-2">🧮</span>Reward Calculator
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- System Statistics -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <h2 class="text-3xl font-orbitron font-bold text-center mb-12">Live System Statistics</h2>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
            <!-- Gleaning Pool -->
            <div class="glass-card-enhanced p-6 text-center">
                <div class="text-4xl mb-4">🌾</div>
                <h3 class="text-xl font-semibold mb-2">Gleaning Pool</h3>
                <p class="metric-value metric-medium text-cyber-cyan mb-2"
                   data-format="currency"
                   data-value="{{ stats.gleaning_pool_balance }}">{{ "%.2f"|format(stats.gleaning_pool_balance) }}</p>
                <p class="text-sm text-gray-400">ONX Available for Community</p>
            </div>

            <!-- Active Loans -->
            <div class="glass-card-enhanced p-6 text-center">
                <div class="text-4xl mb-4">🤝</div>
                <h3 class="text-xl font-semibold mb-2">Interest-Free Loans</h3>
                <p class="metric-value metric-medium text-cyber-purple mb-2"
                   data-format="number"
                   data-value="{{ stats.active_loans }}">{{ stats.active_loans }}</p>
                <p class="text-sm text-gray-400">Active Lending Agreements</p>
            </div>

            <!-- Righteous Deeds -->
            <div class="glass-card-enhanced p-6 text-center">
                <div class="text-4xl mb-4">⭐</div>
                <h3 class="text-xl font-semibold mb-2">Righteous Deeds</h3>
                <p class="metric-value metric-medium text-cyber-blue mb-2"
                   data-format="number"
                   data-value="{{ stats.total_deeds }}">{{ stats.total_deeds }}</p>
                <p class="text-sm text-gray-400">Community Good Works</p>
            </div>

            <!-- Sabbath Observers -->
            <div class="glass-card-enhanced p-6 text-center">
                <div class="text-4xl mb-4">🕊️</div>
                <h3 class="text-xl font-semibold mb-2">Sabbath Observers</h3>
                <p class="metric-value metric-medium text-yellow-400 mb-2"
                   data-format="number"
                   data-value="{{ stats.sabbath_observers }}">{{ stats.sabbath_observers }}</p>
                <p class="text-sm text-gray-400">Faithful Rest Keepers</p>
            </div>
        </div>
    </div>

    <!-- Biblical Principles -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <h2 class="text-3xl font-orbitron font-bold text-center mb-12">Nine Pillars of Biblical Economics</h2>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Jubilee Reset -->
            <div class="principle-card glass-card p-6">
                <div class="text-4xl mb-4">🔄</div>
                <h3 class="text-xl font-semibold mb-3">Jubilee Reset</h3>
                <p class="text-gray-300 mb-4">
                    Periodic wealth redistribution ensures economic justice and prevents extreme inequality.
                </p>
                <div class="verse-text text-sm">
                    "And you shall consecrate the fiftieth year, and proclaim liberty throughout the land" - Leviticus 25:10
                </div>
            </div>

            <!-- Tiered Mining -->
            <div class="principle-card glass-card p-6">
                <div class="text-4xl mb-4">⭐</div>
                <h3 class="text-xl font-semibold mb-3">Righteous Rewards</h3>
                <p class="text-gray-300 mb-4">
                    Mining rewards increase based on community service and righteous deeds performed.
                </p>
                <div class="verse-text text-sm">
                    "The righteous will flourish like a palm tree" - Psalm 92:12
                </div>
            </div>

            <!-- Gleaning Pool -->
            <div class="principle-card glass-card p-6">
                <div class="text-4xl mb-4">🌾</div>
                <h3 class="text-xl font-semibold mb-3">Gleaning Pool</h3>
                <p class="text-gray-300 mb-4">
                    Community safety net providing support for those in need through automated allocation.
                </p>
                <div class="verse-text text-sm">
                    "When you reap the harvest of your land, do not reap to the very edges" - Leviticus 19:9
                </div>
            </div>

            <!-- Anti-Usury -->
            <div class="principle-card glass-card p-6">
                <div class="text-4xl mb-4">🤝</div>
                <h3 class="text-xl font-semibold mb-3">Interest-Free Lending</h3>
                <p class="text-gray-300 mb-4">
                    Biblical lending system without interest, featuring automatic debt forgiveness.
                </p>
                <div class="verse-text text-sm">
                    "If you lend money to one of my people among you who is needy, do not charge interest" - Exodus 22:25
                </div>
            </div>

            <!-- Firstfruits -->
            <div class="principle-card glass-card p-6">
                <div class="text-4xl mb-4">🎁</div>
                <h3 class="text-xl font-semibold mb-3">Firstfruits Offerings</h3>
                <p class="text-gray-300 mb-4">
                    Voluntary community contributions rewarded with special Etzem tokens.
                </p>
                <div class="verse-text text-sm">
                    "Bring the best of the firstfruits of your soil to the house of the Lord" - Deuteronomy 26:2
                </div>
            </div>

            <!-- Anti-Concentration -->
            <div class="principle-card glass-card p-6">
                <div class="text-4xl mb-4">⚖️</div>
                <h3 class="text-xl font-semibold mb-3">Anti-Concentration</h3>
                <p class="text-gray-300 mb-4">
                    Wealth limits and penalties prevent extreme accumulation and encourage sharing.
                </p>
                <div class="verse-text text-sm">
                    "Each of you must give as you have made up your mind" - 2 Corinthians 9:7
                </div>
            </div>

            <!-- Token Classification -->
            <div class="principle-card glass-card p-6">
                <div class="text-4xl mb-4">🏷️</div>
                <h3 class="text-xl font-semibold mb-3">Token Classification</h3>
                <p class="text-gray-300 mb-4">
                    Biblical categorization system: Avodah (labor), Zedek (righteousness), Yovel (jubilee), Etzem (essence).
                </p>
                <div class="verse-text text-sm">
                    "To everything there is a season" - Ecclesiastes 3:1
                </div>
            </div>

            <!-- Fair Wages -->
            <div class="principle-card glass-card p-6">
                <div class="text-4xl mb-4">💰</div>
                <h3 class="text-xl font-semibold mb-3">Fair Wages</h3>
                <p class="text-gray-300 mb-4">
                    Minimum and maximum reward bounds ensure fair compensation for all participants.
                </p>
                <div class="verse-text text-sm">
                    "The worker deserves his wages" - Luke 10:7
                </div>
            </div>

            <!-- Sabbath Rest -->
            <div class="principle-card glass-card p-6">
                <div class="text-4xl mb-4">🕊️</div>
                <h3 class="text-xl font-semibold mb-3">Sabbath Rest</h3>
                <p class="text-gray-300 mb-4">
                    Mining operations pause during Sabbath periods, honoring the commandment of rest.
                </p>
                <div class="verse-text text-sm">
                    "Remember the Sabbath day, to keep it holy" - Exodus 20:8
                </div>
            </div>
        </div>
    </div>

    <!-- Call to Action -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div class="glass-card p-12 text-center">
            <h2 class="text-3xl font-orbitron font-bold mb-6">Experience Biblical Economics</h2>
            <p class="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
                Join a blockchain platform that values justice, community, and spiritual principles.
                Experience how ancient wisdom can guide modern economic systems.
            </p>

            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                {% if current_user %}
                <a href="{{ url_for('tokenomics.dashboard') }}"
                   class="px-8 py-4 bg-gradient-to-r from-cyber-cyan to-cyber-blue text-white font-semibold rounded-xl hover:from-cyber-cyan/90 hover:to-cyber-blue/90 transition-all duration-300 transform hover:scale-105">
                    <span class="mr-2">🚀</span>Start Your Journey
                </a>
                <a href="{{ url_for('tokenomics.firstfruits') }}"
                   class="px-8 py-4 border-2 border-cyber-purple text-cyber-purple font-semibold rounded-xl hover:bg-cyber-purple hover:text-white transition-all duration-300">
                    <span class="mr-2">🎁</span>Make an Offering
                </a>
                {% else %}
                <a href="{{ url_for('register_choice') }}"
                   class="px-8 py-4 bg-gradient-to-r from-cyber-cyan to-cyber-blue text-white font-semibold rounded-xl hover:from-cyber-cyan/90 hover:to-cyber-blue/90 transition-all duration-300 transform hover:scale-105">
                    <span class="mr-2">✨</span>Verify Your Identity
                </a>
                <a href="{{ url_for('tokenomics.education') }}"
                   class="px-8 py-4 border-2 border-cyber-purple text-cyber-purple font-semibold rounded-xl hover:bg-cyber-purple hover:text-white transition-all duration-300">
                    <span class="mr-2">📚</span>Learn More
                </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Add some interactive effects
    document.addEventListener('DOMContentLoaded', function() {
        // Animate principle cards on scroll
        const cards = document.querySelectorAll('.principle-card');

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, { threshold: 0.1 });

        cards.forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });
    });
</script>
{% endblock %}
