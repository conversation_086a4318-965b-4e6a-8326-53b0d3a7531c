"""
Onnyx Mempool Model

This module provides the Mempool model for the Onnyx blockchain.
"""

import time
import logging
from typing import Dict, Any, List, Optional, ClassVar

from shared.models.base import BaseModel
from shared.models.transaction import Transaction
from shared.db.db import db

# Set up logging
logger = logging.getLogger("onnyx.models.mempool")

class MempoolTransaction(BaseModel):
    """
    Mempool Transaction model for the Onnyx blockchain.
    """
    
    # Table name
    table_name: ClassVar[str] = "mempool"
    
    # Primary key column
    primary_key: ClassVar[str] = "tx_id"
    
    # JSON fields
    json_fields: ClassVar[List[str]] = ["data"]
    
    def __init__(
        self,
        tx_id: str,
        timestamp: int,
        op: str,
        data: Dict[str, Any],
        sender: str,
        signature: str,
        created_at: int = None,
        **kwargs
    ):
        """
        Initialize the Mempool Transaction model.
        
        Args:
            tx_id: The transaction ID
            timestamp: The transaction timestamp
            op: The transaction operation
            data: The transaction data
            sender: The sender identity ID
            signature: The transaction signature
            created_at: The creation timestamp
            **kwargs: Additional attributes
        """
        self.tx_id = tx_id
        self.timestamp = timestamp
        self.op = op
        self.data = data
        self.sender = sender
        self.signature = signature
        self.created_at = created_at or int(time.time())
        
        super().__init__(**kwargs)
    
    @classmethod
    def get_by_op(cls, op: str) -> List['MempoolTransaction']:
        """
        Get mempool transactions by operation.
        
        Args:
            op: The transaction operation
        
        Returns:
            A list of mempool transactions
        """
        return cls.filter("op = ?", (op,))
    
    @classmethod
    def get_by_sender(cls, sender: str) -> List['MempoolTransaction']:
        """
        Get mempool transactions by sender.
        
        Args:
            sender: The sender identity ID
        
        Returns:
            A list of mempool transactions
        """
        return cls.filter("sender = ?", (sender,))
    
    @classmethod
    def get_all_sorted(cls) -> List['MempoolTransaction']:
        """
        Get all mempool transactions sorted by timestamp.
        
        Returns:
            A list of mempool transactions
        """
        query = f"SELECT * FROM {cls.table_name} ORDER BY timestamp ASC"
        rows = db.query(query)
        
        return [cls.from_dict(row) for row in rows]
    
    def to_transaction(self) -> Transaction:
        """
        Convert the mempool transaction to a regular transaction.
        
        Returns:
            A Transaction instance
        """
        return Transaction(
            tx_id=self.tx_id,
            timestamp=self.timestamp,
            op=self.op,
            data=self.data,
            sender=self.sender,
            signature=self.signature,
            status="pending",
            created_at=self.created_at
        )
    
    @classmethod
    def from_transaction(cls, transaction: Transaction) -> 'MempoolTransaction':
        """
        Create a mempool transaction from a regular transaction.
        
        Args:
            transaction: The transaction
        
        Returns:
            A MempoolTransaction instance
        """
        return cls(
            tx_id=transaction.tx_id,
            timestamp=transaction.timestamp,
            op=transaction.op,
            data=transaction.data,
            sender=transaction.sender,
            signature=transaction.signature,
            created_at=transaction.created_at
        )
    
    @classmethod
    def add_transaction(cls, transaction: Transaction) -> 'MempoolTransaction':
        """
        Add a transaction to the mempool.
        
        Args:
            transaction: The transaction
        
        Returns:
            The mempool transaction
        """
        # Create the mempool transaction
        mempool_tx = cls.from_transaction(transaction)
        
        # Save the mempool transaction
        mempool_tx.save()
        
        return mempool_tx
    
    def remove(self) -> None:
        """
        Remove the transaction from the mempool.
        """
        self.delete()
    
    @classmethod
    def clear(cls) -> None:
        """
        Clear the mempool.
        """
        db.execute(f"DELETE FROM {cls.table_name}")
    
    @classmethod
    def get_size(cls) -> int:
        """
        Get the size of the mempool.
        
        Returns:
            The number of transactions in the mempool
        """
        return cls.count()
    
    @classmethod
    def get_transactions_for_block(cls, max_transactions: int) -> List['MempoolTransaction']:
        """
        Get transactions for a new block.
        
        Args:
            max_transactions: The maximum number of transactions to include
        
        Returns:
            A list of mempool transactions
        """
        query = f"SELECT * FROM {cls.table_name} ORDER BY timestamp ASC LIMIT ?"
        rows = db.query(query, (max_transactions,))
        
        return [cls.from_dict(row) for row in rows]
