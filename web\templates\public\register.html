<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Join the Covenant - ONNYX</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    <style>
        .register-container {
            max-width: 600px;
            margin: 0 auto;
            padding: 24px;
        }
        
        .register-card {
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 16px;
            padding: 32px;
            backdrop-filter: blur(16px);
        }
        
        .register-header {
            text-align: center;
            margin-bottom: 32px;
        }
        
        .register-title {
            font-size: 2rem;
            font-weight: 700;
            background: linear-gradient(135deg, #00d4ff, #8a2be2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 16px;
        }
        
        .register-subtitle {
            color: rgba(255, 255, 255, 0.7);
            line-height: 1.6;
        }
        
        .form-group {
            margin-bottom: 24px;
        }
        
        .form-label {
            display: block;
            color: #00d4ff;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .form-input, .form-select {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 8px;
            background: rgba(0, 0, 0, 0.3);
            color: white;
            backdrop-filter: blur(8px);
            font-size: 1rem;
        }
        
        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: #00d4ff;
            box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.2);
        }
        
        .form-help {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.6);
            margin-top: 4px;
        }
        
        .tribe-info {
            background: rgba(138, 43, 226, 0.1);
            border: 1px solid rgba(138, 43, 226, 0.3);
            border-radius: 8px;
            padding: 16px;
            margin-top: 8px;
            display: none;
        }
        
        .tribe-name {
            font-weight: 600;
            color: #8a2be2;
            margin-bottom: 8px;
        }
        
        .tribe-description {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
        }
        
        .btn-register {
            width: 100%;
            background: linear-gradient(135deg, #00d4ff, #8a2be2);
            color: white;
            border: none;
            padding: 16px;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 212, 255, 0.3);
        }
        
        .btn-register:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        
        .success-message {
            background: rgba(0, 255, 0, 0.1);
            border: 1px solid rgba(0, 255, 0, 0.3);
            border-radius: 8px;
            padding: 16px;
            color: #00ff00;
            margin-bottom: 24px;
            display: none;
        }
        
        .error-message {
            background: rgba(255, 0, 0, 0.1);
            border: 1px solid rgba(255, 0, 0, 0.3);
            border-radius: 8px;
            padding: 16px;
            color: #ff0000;
            margin-bottom: 24px;
            display: none;
        }
        
        .next-steps {
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 8px;
            padding: 16px;
            margin-top: 24px;
            display: none;
        }
        
        .next-steps h4 {
            color: #00d4ff;
            margin-bottom: 12px;
        }
        
        .next-steps ul {
            color: rgba(255, 255, 255, 0.7);
            padding-left: 20px;
        }
        
        .next-steps li {
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <img src="{{ url_for('static', filename='images/onnyx_logo.png') }}" alt="ONNYX" class="nav-logo">
                <span class="nav-title">JOIN COVENANT</span>
            </div>
            <div class="nav-links">
                <a href="/public/" class="nav-link">← Back to Landing</a>
            </div>
        </div>
    </nav>

    <div class="register-container">
        <div class="register-card">
            <div class="register-header">
                <h1 class="register-title">Join the Covenant Community</h1>
                <p class="register-subtitle">
                    Begin your journey in the ONNYX biblical blockchain community. 
                    Select your tribe and start at Tier 0 with view-only access.
                </p>
            </div>

            <div id="success-message" class="success-message"></div>
            <div id="error-message" class="error-message"></div>

            <form id="registration-form">
                <div class="form-group">
                    <label class="form-label" for="name">Full Name</label>
                    <input type="text" id="name" name="name" class="form-input" required>
                    <div class="form-help">Your real name for covenant community identification</div>
                </div>

                <div class="form-group">
                    <label class="form-label" for="email">Email Address</label>
                    <input type="email" id="email" name="email" class="form-input" required>
                    <div class="form-help">Used for covenant communications and account recovery</div>
                </div>

                <div class="form-group">
                    <label class="form-label" for="tribe">Choose Your Tribe</label>
                    <select id="tribe" name="tribe" class="form-select" required>
                        <option value="">Select one of the 12 tribes of Israel</option>
                        <option value="JU">Judah - The Royal Tribe</option>
                        <option value="LE">Levi - The Priestly Tribe</option>
                        <option value="EP">Ephraim - The Fruitful Tribe</option>
                        <option value="BE">Benjamin - Son of the Right Hand</option>
                        <option value="SI">Simeon - He Who Hears</option>
                        <option value="MA">Manasseh - Making to Forget</option>
                        <option value="IS">Issachar - There is Recompense</option>
                        <option value="ZE">Zebulun - Dwelling</option>
                        <option value="NA">Naphtali - My Wrestling</option>
                        <option value="GA">Gad - A Troop</option>
                        <option value="AS">Asher - Happy</option>
                        <option value="RE">Reuben - See, a Son</option>
                    </select>
                    <div class="form-help">Your tribal affiliation determines your covenant community and governance participation</div>
                    
                    <div id="tribe-info" class="tribe-info">
                        <div id="tribe-name" class="tribe-name"></div>
                        <div id="tribe-description" class="tribe-description"></div>
                    </div>
                </div>

                <button type="submit" id="register-btn" class="btn-register">
                    Join the Covenant Community
                </button>
            </form>

            <div id="next-steps" class="next-steps">
                <h4>🎉 Registration Successful!</h4>
                <p>Welcome to the ONNYX Covenant Community! Here are your next steps:</p>
                <ul>
                    <li><strong>Current Status:</strong> Tier 0 (View Only Access)</li>
                    <li><strong>Next Step:</strong> Take the Covenant Comprehension Test</li>
                    <li><strong>Goal:</strong> Advance to Tier 1 for full participation</li>
                    <li><strong>Benefits:</strong> Governance voting, anti-usury lending, gleaning pool access</li>
                </ul>
                <a href="/public/covenant-test" class="btn-register" style="margin-top: 16px; text-decoration: none; text-align: center; display: block;">
                    Take Covenant Test Now
                </a>
            </div>
        </div>
    </div>

    <script>
        const tribeDescriptions = {
            'JU': {
                name: 'Judah - The Royal Tribe',
                description: 'The tribe of kings and rulers. Judah holds the scepter and provides leadership to the covenant community. Members often excel in governance and administration.'
            },
            'LE': {
                name: 'Levi - The Priestly Tribe', 
                description: 'The tribe of priests and teachers. Levi serves the covenant through spiritual guidance, education, and maintaining biblical principles.'
            },
            'EP': {
                name: 'Ephraim - The Fruitful Tribe',
                description: 'The doubly fruitful tribe blessed with abundance. Ephraim excels in business, agriculture, and economic development within the covenant.'
            },
            'BE': {
                name: 'Benjamin - Son of the Right Hand',
                description: 'The beloved tribe known for loyalty and strength. Benjamin provides protection and support to the covenant community.'
            },
            'SI': {
                name: 'Simeon - He Who Hears',
                description: 'The tribe that listens and responds. Simeon excels in communication, mediation, and understanding the needs of others.'
            },
            'MA': {
                name: 'Manasseh - Making to Forget',
                description: 'The tribe of new beginnings and fresh starts. Manasseh helps the community move forward and embrace positive change.'
            },
            'IS': {
                name: 'Issachar - There is Recompense',
                description: 'The tribe of wisdom and understanding of times. Issachar provides counsel and strategic thinking to the covenant.'
            },
            'ZE': {
                name: 'Zebulun - Dwelling',
                description: 'The tribe of commerce and trade. Zebulun facilitates business relationships and economic connections within the covenant.'
            },
            'NA': {
                name: 'Naphtali - My Wrestling',
                description: 'The tribe of struggle and perseverance. Naphtali provides strength and determination in challenging times.'
            },
            'GA': {
                name: 'Gad - A Troop',
                description: 'The tribe of warriors and defenders. Gad protects the covenant community and maintains security.'
            },
            'AS': {
                name: 'Asher - Happy',
                description: 'The tribe of joy and abundance. Asher brings happiness, celebration, and positive energy to the covenant.'
            },
            'RE': {
                name: 'Reuben - See, a Son',
                description: 'The firstborn tribe with natural leadership. Reuben provides vision and pioneering spirit to the covenant community.'
            }
        };

        document.getElementById('tribe').addEventListener('change', function() {
            const selectedTribe = this.value;
            const tribeInfo = document.getElementById('tribe-info');
            
            if (selectedTribe && tribeDescriptions[selectedTribe]) {
                const info = tribeDescriptions[selectedTribe];
                document.getElementById('tribe-name').textContent = info.name;
                document.getElementById('tribe-description').textContent = info.description;
                tribeInfo.style.display = 'block';
            } else {
                tribeInfo.style.display = 'none';
            }
        });

        document.getElementById('registration-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('register-btn');
            const successMsg = document.getElementById('success-message');
            const errorMsg = document.getElementById('error-message');
            const nextSteps = document.getElementById('next-steps');
            
            // Hide previous messages
            successMsg.style.display = 'none';
            errorMsg.style.display = 'none';
            nextSteps.style.display = 'none';
            
            // Disable submit button
            submitBtn.disabled = true;
            submitBtn.textContent = 'Registering...';
            
            try {
                const formData = new FormData(this);
                const data = {
                    name: formData.get('name'),
                    email: formData.get('email'),
                    nation_code: formData.get('tribe')
                };
                
                const response = await fetch('/public/api/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    successMsg.innerHTML = `
                        <strong>Welcome to the ${result.tribe} tribe!</strong><br>
                        Your covenant identity has been created. ${result.next_step}
                    `;
                    successMsg.style.display = 'block';
                    nextSteps.style.display = 'block';
                    
                    // Store identity ID for covenant test
                    localStorage.setItem('onnyx_identity_id', result.identity_id);
                    
                    // Hide form
                    this.style.display = 'none';
                } else {
                    errorMsg.textContent = result.error;
                    errorMsg.style.display = 'block';
                }
            } catch (error) {
                errorMsg.textContent = 'Registration failed. Please try again.';
                errorMsg.style.display = 'block';
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = 'Join the Covenant Community';
            }
        });
    </script>
</body>
</html>
