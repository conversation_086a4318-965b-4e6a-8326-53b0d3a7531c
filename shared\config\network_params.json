{"networks": {"mainnet": {"name": "Onnyx Mainnet", "chain_id": "onnyx-main-1", "p2p_port": 8333, "rpc_port": 8332, "seed_nodes": ["seed1.onnyx.chain:8333", "seed2.onnyx.chain:8333", "seed3.onnyx.chain:8333"], "genesis_hash": "0000000000000000000000000000000000000000000000000000000000000000"}, "testnet": {"name": "Onnyx Testnet", "chain_id": "onnyx-test-1", "p2p_port": 18333, "rpc_port": 18332, "seed_nodes": ["test-seed1.onnyx.chain:18333", "test-seed2.onnyx.chain:18333"], "genesis_hash": "0000000000000000000000000000000000000000000000000000000000000000"}, "devnet": {"name": "Onnyx Development Network", "chain_id": "onnyx-dev-1", "p2p_port": 28333, "rpc_port": 28332, "seed_nodes": ["127.0.0.1:28333"], "genesis_hash": "0000000000000000000000000000000000000000000000000000000000000000"}}, "protocol_version": "0.1.0", "min_peer_version": "0.1.0", "max_connections": 125, "connection_timeout_seconds": 30, "handshake_timeout_seconds": 5}