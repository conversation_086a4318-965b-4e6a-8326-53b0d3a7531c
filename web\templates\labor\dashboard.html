{% extends "base.html" %}

{% block title %}Labor Dashboard - ONNYX Platform{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="text-center mb-12">
            <h1 class="text-4xl md:text-5xl font-orbitron font-bold text-primary mb-4">
                Labor Dashboard
            </h1>
            <p class="text-xl text-secondary max-w-3xl mx-auto">
                Track your contributions, earn Mikvah tokens, and build your reputation in the covenant community
            </p>
        </div>

        <!-- Labor & Tokenomics Section -->
        <div class="grid grid-cols-1 xl:grid-cols-3 gap-8 mb-8">
            <!-- Labor Activity Panel -->
            <div class="glass-card p-6">
                <h2 class="text-2xl font-orbitron font-bold text-primary mb-6">Labor Activity</h2>
                
                <!-- Quick Labor Log -->
                <div class="glass-panel p-4 mb-6">
                    <h3 class="text-lg font-semibold text-cyber-cyan mb-3">Log New Labor</h3>
                    <form id="laborForm" class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-secondary mb-2">Labor Type</label>
                            <select id="laborType" class="glass-input w-full">
                                <option value="service">Service</option>
                                <option value="product">Product</option>
                                <option value="teaching">Teaching</option>
                                <option value="healing">Healing</option>
                                <option value="farming">Farming</option>
                                <option value="governance">Governance</option>
                                <option value="community">Community</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-secondary mb-2">Description</label>
                            <textarea id="laborDescription" rows="3" class="glass-input w-full" placeholder="Describe your labor contribution..."></textarea>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-secondary mb-2">Estimated Value</label>
                            <input type="number" id="laborValue" min="1" max="2000" class="glass-input w-full" placeholder="0.00">
                        </div>
                        <button type="submit" class="glass-button-primary w-full py-2 px-4 rounded-lg">
                            Log Labor
                        </button>
                    </form>
                </div>

                <!-- Recent Labor -->
                <div class="glass-panel p-4">
                    <h3 class="text-lg font-semibold text-cyber-cyan mb-3">Recent Labor</h3>
                    <div id="recentLabor" class="space-y-3">
                        <div class="text-center text-secondary py-4">
                            Loading labor history...
                        </div>
                    </div>
                </div>
            </div>

            <!-- Mikvah Token Balance -->
            <div class="glass-card p-6">
                <h2 class="text-2xl font-orbitron font-bold text-primary mb-6">Mikvah Tokens</h2>
                
                <!-- Token Balance Display -->
                <div class="glass-panel p-4 mb-6 text-center">
                    <div class="text-3xl font-bold text-cyber-cyan mb-2" id="mikvahBalance">0.00</div>
                    <div class="text-sm text-secondary">Total Balance</div>
                </div>

                <!-- Yovel Cycle Progress -->
                <div class="glass-panel p-4 mb-6">
                    <h3 class="text-lg font-semibold text-cyber-cyan mb-3">Yovel Cycle Progress</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between text-sm">
                            <span class="text-secondary">Current Cycle Tokens:</span>
                            <span class="text-primary" id="yovelTokens">0.00</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-secondary">Cycle Limit:</span>
                            <span class="text-primary">10,000.00</span>
                        </div>
                        <div class="progress-bar">
                            <div id="yovelProgress" class="progress-fill" style="width: 0%"></div>
                        </div>
                        <div class="text-xs text-secondary text-center" id="yovelRemaining">10,000.00 tokens remaining</div>
                    </div>
                </div>

                <!-- Season Earnings -->
                <div class="glass-panel p-4 text-center">
                    <h3 class="text-lg font-semibold text-cyber-cyan mb-3">Season Earnings</h3>
                    <div class="text-2xl font-bold text-cyber-purple mb-1" id="seasonEarnings">0.00</div>
                    <div class="text-sm text-secondary">This Season (90 days)</div>
                </div>
            </div>

            <!-- Zeman Progress Tracker -->
            <div class="glass-card p-6">
                <h2 class="text-2xl font-orbitron font-bold text-primary mb-6">Zeman Progress</h2>
                
                <!-- Seasonal Activity Goals -->
                <div class="glass-panel p-4 mb-6">
                    <h3 class="text-lg font-semibold text-cyber-cyan mb-3">Seasonal Goals</h3>
                    <div class="space-y-4">
                        <div>
                            <div class="flex justify-between text-sm mb-1">
                                <span class="text-secondary">Labor Contributions</span>
                                <span class="text-primary"><span id="laborCount">0</span>/20</span>
                            </div>
                            <div class="progress-bar">
                                <div id="laborProgress" class="progress-fill" style="width: 0%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between text-sm mb-1">
                                <span class="text-secondary">Verification Rate</span>
                                <span class="text-primary" id="verificationRate">0%</span>
                            </div>
                            <div class="progress-bar">
                                <div id="verificationProgress" class="progress-fill" style="width: 0%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Etzem Growth Chart -->
                <div class="glass-panel p-4 mb-6 text-center">
                    <h3 class="text-lg font-semibold text-cyber-cyan mb-3">Etzem Growth</h3>
                    <div class="text-2xl font-bold text-cyber-cyan mb-1" id="etzemScore">0</div>
                    <div class="text-sm text-secondary">Reputation Points</div>
                    <div class="mt-3">
                        <div class="text-xs text-secondary mb-1">Season Progress: +<span id="seasonEtzem">0</span> points</div>
                        <div class="progress-bar">
                            <div id="etzemProgress" class="progress-fill" style="width: 0%"></div>
                        </div>
                    </div>
                </div>

                <!-- Verification Queue -->
                <div class="glass-panel p-4">
                    <h3 class="text-lg font-semibold text-cyber-cyan mb-3">Verification Queue</h3>
                    <div id="verificationQueue" class="space-y-2">
                        <div class="text-center text-secondary py-2">
                            Loading pending verifications...
                        </div>
                    </div>
                    <button onclick="loadPendingVerifications()" class="glass-button w-full mt-3 py-2 px-4 rounded-lg text-sm">
                        Verify Community Labor
                    </button>
                </div>
            </div>
        </div>

        <!-- Seasonal Summary -->
        <div class="glass-card p-6 mb-8">
            <h2 class="text-2xl font-orbitron font-bold text-primary mb-6">Seasonal Summary</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <!-- Labor Statistics -->
                <div class="glass-panel p-4 text-center">
                    <div class="text-2xl font-bold text-cyber-blue mb-2" id="totalLabor">0</div>
                    <div class="text-sm text-secondary">Total Labor</div>
                </div>
                
                <!-- Value Created -->
                <div class="glass-panel p-4 text-center">
                    <div class="text-2xl font-bold text-cyber-cyan mb-2" id="totalValue">0.00</div>
                    <div class="text-sm text-secondary">Value Created</div>
                </div>
                
                <!-- Tokens Earned -->
                <div class="glass-panel p-4 text-center">
                    <div class="text-2xl font-bold text-cyber-purple mb-2" id="tokensEarned">0.00</div>
                    <div class="text-sm text-secondary">Tokens Earned</div>
                </div>
                
                <!-- Community Impact -->
                <div class="glass-panel p-4 text-center">
                    <div class="text-2xl font-bold text-cyber-pink mb-2" id="communityImpact">0</div>
                    <div class="text-sm text-secondary">Community Impact</div>
                </div>
            </div>
        </div>

        <!-- Labor Types Information -->
        <div class="glass-card p-6">
            <h2 class="text-2xl font-orbitron font-bold text-primary mb-6">Labor Types & Rewards</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="glass-panel p-4">
                    <h3 class="text-lg font-semibold text-cyber-cyan mb-2">Teaching</h3>
                    <p class="text-sm text-secondary mb-2">Knowledge sharing and education</p>
                    <div class="text-xs text-cyber-blue">1.5x multiplier</div>
                </div>
                
                <div class="glass-panel p-4">
                    <h3 class="text-lg font-semibold text-cyber-cyan mb-2">Healing</h3>
                    <p class="text-sm text-secondary mb-2">Medical and wellness services</p>
                    <div class="text-xs text-cyber-blue">1.4x multiplier</div>
                </div>
                
                <div class="glass-panel p-4">
                    <h3 class="text-lg font-semibold text-cyber-cyan mb-2">Community</h3>
                    <p class="text-sm text-secondary mb-2">Community service and mutual aid</p>
                    <div class="text-xs text-cyber-blue">1.3x multiplier</div>
                </div>
                
                <div class="glass-panel p-4">
                    <h3 class="text-lg font-semibold text-cyber-cyan mb-2">Governance</h3>
                    <p class="text-sm text-secondary mb-2">Community governance and administration</p>
                    <div class="text-xs text-cyber-blue">1.2x multiplier</div>
                </div>
                
                <div class="glass-panel p-4">
                    <h3 class="text-lg font-semibold text-cyber-cyan mb-2">Farming</h3>
                    <p class="text-sm text-secondary mb-2">Agricultural and food production</p>
                    <div class="text-xs text-cyber-blue">1.1x multiplier</div>
                </div>
                
                <div class="glass-panel p-4">
                    <h3 class="text-lg font-semibold text-cyber-cyan mb-2">Service</h3>
                    <p class="text-sm text-secondary mb-2">General service provision</p>
                    <div class="text-xs text-cyber-blue">1.0x multiplier</div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Get current user identity ID from session
const currentIdentityId = '{{ session.identity_id }}';

// Labor form submission
document.getElementById('laborForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const laborData = {
        labor_type: document.getElementById('laborType').value,
        description: document.getElementById('laborDescription').value,
        value_estimate: parseFloat(document.getElementById('laborValue').value) || 0
    };
    
    if (!laborData.description || laborData.description.length < 10) {
        alert('Please provide a description of at least 10 characters.');
        return;
    }
    
    if (laborData.value_estimate <= 0) {
        alert('Please provide a valid estimated value.');
        return;
    }
    
    try {
        const response = await fetch('/api/labor/log', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(laborData)
        });
        
        const result = await response.json();
        
        if (result.success) {
            alert(`Labor logged successfully! Potential reward: ${result.potential_mikvah_tokens} tokens, ${result.potential_etzem_points} Etzem points.`);
            
            // Reset form
            document.getElementById('laborForm').reset();
            
            // Refresh data
            loadLaborData();
            loadTokenBalance();
        } else {
            alert(`Error: ${result.error}`);
        }
    } catch (error) {
        console.error('Error logging labor:', error);
        alert('Failed to log labor. Please try again.');
    }
});

// Load labor data
async function loadLaborData() {
    try {
        const response = await fetch(`/api/labor/history/${currentIdentityId}`);
        const data = await response.json();
        
        if (data.labor_records) {
            displayRecentLabor(data.labor_records.slice(0, 5));
        }
        
        if (data.statistics) {
            updateLaborStatistics(data.statistics);
        }
    } catch (error) {
        console.error('Error loading labor data:', error);
    }
}

// Load token balance
async function loadTokenBalance() {
    try {
        const response = await fetch(`/api/labor/statistics/${currentIdentityId}`);
        const data = await response.json();
        
        if (data.token_balance) {
            updateTokenDisplay(data.token_balance);
        }
        
        if (data.statistics) {
            updateSeasonalStats(data.statistics);
        }
    } catch (error) {
        console.error('Error loading token balance:', error);
    }
}

// Load pending verifications
async function loadPendingVerifications() {
    try {
        const response = await fetch('/api/labor/pending-verification');
        const data = await response.json();
        
        if (data.pending_labor) {
            displayPendingVerifications(data.pending_labor.slice(0, 3));
        }
    } catch (error) {
        console.error('Error loading pending verifications:', error);
    }
}

// Display recent labor
function displayRecentLabor(laborRecords) {
    const container = document.getElementById('recentLabor');
    
    if (laborRecords.length === 0) {
        container.innerHTML = '<div class="text-center text-secondary py-4">No labor records yet</div>';
        return;
    }
    
    container.innerHTML = laborRecords.map(labor => `
        <div class="flex justify-between items-center p-3 glass-panel rounded-lg">
            <div>
                <div class="text-sm font-medium text-primary">${labor.labor_type}</div>
                <div class="text-xs text-secondary">${labor.description.substring(0, 30)}...</div>
            </div>
            <div class="text-right">
                <div class="text-sm font-medium ${getStatusColor(labor.verification_status)}">${labor.verification_status}</div>
                <div class="text-xs text-secondary">${labor.value_estimate} value</div>
            </div>
        </div>
    `).join('');
}

// Update token display
function updateTokenDisplay(tokenBalance) {
    document.getElementById('mikvahBalance').textContent = tokenBalance.total_balance.toFixed(2);
    document.getElementById('yovelTokens').textContent = tokenBalance.yovel_balance.toFixed(2);
    document.getElementById('seasonEarnings').textContent = tokenBalance.season_earnings.toFixed(2);
    
    const yovelProgress = (tokenBalance.yovel_balance / tokenBalance.yovel_limit) * 100;
    document.getElementById('yovelProgress').style.width = `${Math.min(yovelProgress, 100)}%`;
    document.getElementById('yovelRemaining').textContent = `${tokenBalance.yovel_remaining.toFixed(2)} tokens remaining`;
}

// Update seasonal statistics
function updateSeasonalStats(statistics) {
    if (statistics.current_season) {
        const season = statistics.current_season;
        document.getElementById('laborCount').textContent = season.labor_count;
        document.getElementById('verificationRate').textContent = `${season.verification_rate.toFixed(1)}%`;
        document.getElementById('seasonEtzem').textContent = season.total_etzem;
        
        // Update progress bars
        const laborProgress = (season.labor_count / 20) * 100;
        document.getElementById('laborProgress').style.width = `${Math.min(laborProgress, 100)}%`;
        document.getElementById('verificationProgress').style.width = `${season.verification_rate}%`;
        
        // Update seasonal summary
        document.getElementById('totalLabor').textContent = season.labor_count;
        document.getElementById('totalValue').textContent = season.total_value.toFixed(2);
        document.getElementById('tokensEarned').textContent = season.total_etzem;
        document.getElementById('communityImpact').textContent = season.verified_count;
    }
}

// Display pending verifications
function displayPendingVerifications(pendingLabor) {
    const container = document.getElementById('verificationQueue');
    
    if (pendingLabor.length === 0) {
        container.innerHTML = '<div class="text-center text-secondary py-2">No pending verifications</div>';
        return;
    }
    
    container.innerHTML = pendingLabor.map(labor => `
        <div class="flex justify-between items-center p-2 glass-panel rounded text-xs">
            <div>
                <div class="text-primary">${labor.submitter_name}</div>
                <div class="text-secondary">${labor.labor_type}</div>
            </div>
            <button onclick="verifyLabor('${labor.labor_id}', 'approve')" class="bg-green-600 text-white px-2 py-1 rounded text-xs hover:bg-green-700">
                ✓
            </button>
        </div>
    `).join('');
}

// Verify labor
async function verifyLabor(laborId, verificationType) {
    try {
        const response = await fetch(`/api/labor/verify/${laborId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                verification_type: verificationType,
                notes: 'Dashboard verification'
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            alert('Labor verified successfully!');
            loadPendingVerifications();
            loadTokenBalance();
        } else {
            alert(`Error: ${result.error}`);
        }
    } catch (error) {
        console.error('Error verifying labor:', error);
        alert('Failed to verify labor. Please try again.');
    }
}

// Helper function for status colors
function getStatusColor(status) {
    switch (status) {
        case 'verified': return 'text-green-400';
        case 'pending': return 'text-yellow-400';
        case 'disputed': return 'text-orange-400';
        case 'rejected': return 'text-red-400';
        default: return 'text-secondary';
    }
}

// Load initial data
document.addEventListener('DOMContentLoaded', function() {
    if (currentIdentityId) {
        loadLaborData();
        loadTokenBalance();
        loadPendingVerifications();
    }
});

// Auto-refresh data every 60 seconds
setInterval(function() {
    if (currentIdentityId) {
        loadLaborData();
        loadTokenBalance();
        loadPendingVerifications();
    }
}, 60000);
</script>
{% endblock %}
