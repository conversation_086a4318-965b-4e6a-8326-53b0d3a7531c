"""
Onnyx Etzem Model

This module defines the Etzem model for the Onnyx blockchain.
"""

import json
import time
import logging
import sqlite3
from typing import Dict, Any, List, Optional, Union

from shared.config.config import onnyx_config
from shared.models.base_model import BaseModel

# Set up logging
logger = logging.getLogger("onnyx.models.etzem")

class Etzem(BaseModel):
    """
    Etzem model for the Onnyx blockchain.
    
    An Etzem represents a trust score for an identity in the Onnyx ecosystem.
    """
    
    TABLE_NAME = "etzem_scores"
    PRIMARY_KEY = "identity_id"
    
    def __init__(
        self,
        identity_id: str,
        etzem_score: int,
        components: Dict[str, int],
        badges: List[str],
        last_updated: Optional[int] = None
    ):
        """
        Initialize an Etzem.
        
        Args:
            identity_id: The identity ID
            etzem_score: The Etzem score
            components: The components of the Etzem score
            badges: The badges earned based on the Etzem score
            last_updated: The timestamp when the Etzem score was last updated (optional)
        """
        self.identity_id = identity_id
        self.etzem_score = etzem_score
        self.components = components
        self.badges = badges
        self.last_updated = last_updated or int(time.time())
    
    @classmethod
    def create_table(cls) -> None:
        """Create the Etzem table if it doesn't exist."""
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()
        
        cursor.execute(f"""
        CREATE TABLE IF NOT EXISTS {cls.TABLE_NAME} (
            {cls.PRIMARY_KEY} TEXT PRIMARY KEY,
            etzem_score INTEGER NOT NULL,
            components TEXT NOT NULL,
            badges TEXT NOT NULL,
            last_updated INTEGER NOT NULL
        )
        """)
        
        conn.commit()
        conn.close()
    
    @classmethod
    def create(
        cls,
        identity_id: str,
        etzem_score: int,
        components: Dict[str, int],
        badges: List[str],
        last_updated: Optional[int] = None
    ) -> "Etzem":
        """
        Create a new Etzem.
        
        Args:
            identity_id: The identity ID
            etzem_score: The Etzem score
            components: The components of the Etzem score
            badges: The badges earned based on the Etzem score
            last_updated: The timestamp when the Etzem score was last updated (optional)
        
        Returns:
            The created Etzem
        """
        # Create the table if it doesn't exist
        cls.create_table()
        
        # Create the Etzem
        etzem = cls(
            identity_id=identity_id,
            etzem_score=etzem_score,
            components=components,
            badges=badges,
            last_updated=last_updated
        )
        
        # Save the Etzem to the database
        etzem.save()
        
        return etzem
    
    def save(self) -> None:
        """Save the Etzem to the database."""
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()
        
        cursor.execute(f"""
        INSERT OR REPLACE INTO {self.TABLE_NAME} (
            {self.PRIMARY_KEY},
            etzem_score,
            components,
            badges,
            last_updated
        ) VALUES (?, ?, ?, ?, ?)
        """, (
            self.identity_id,
            self.etzem_score,
            json.dumps(self.components),
            json.dumps(self.badges),
            self.last_updated
        ))
        
        conn.commit()
        conn.close()
    
    @classmethod
    def get_by_id(cls, identity_id: str) -> Optional["Etzem"]:
        """
        Get an Etzem by identity ID.
        
        Args:
            identity_id: The identity ID
        
        Returns:
            The Etzem, or None if not found
        """
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()
        
        cursor.execute(f"""
        SELECT
            {cls.PRIMARY_KEY},
            etzem_score,
            components,
            badges,
            last_updated
        FROM {cls.TABLE_NAME}
        WHERE {cls.PRIMARY_KEY} = ?
        """, (identity_id,))
        
        row = cursor.fetchone()
        conn.close()
        
        if row:
            return cls(
                identity_id=row[0],
                etzem_score=row[1],
                components=json.loads(row[2]),
                badges=json.loads(row[3]),
                last_updated=row[4]
            )
        
        return None
    
    @classmethod
    def get_all(cls) -> List["Etzem"]:
        """
        Get all Etzem scores.
        
        Returns:
            A list of all Etzem scores
        """
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()
        
        cursor.execute(f"""
        SELECT
            {cls.PRIMARY_KEY},
            etzem_score,
            components,
            badges,
            last_updated
        FROM {cls.TABLE_NAME}
        """)
        
        rows = cursor.fetchall()
        conn.close()
        
        return [
            cls(
                identity_id=row[0],
                etzem_score=row[1],
                components=json.loads(row[2]),
                badges=json.loads(row[3]),
                last_updated=row[4]
            )
            for row in rows
        ]
    
    @classmethod
    def get_leaderboard(cls, limit: int = 10) -> List["Etzem"]:
        """
        Get the Etzem leaderboard.
        
        Args:
            limit: The maximum number of Etzem scores to return
        
        Returns:
            A list of Etzem scores, sorted by score (descending)
        """
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()
        
        cursor.execute(f"""
        SELECT
            {cls.PRIMARY_KEY},
            etzem_score,
            components,
            badges,
            last_updated
        FROM {cls.TABLE_NAME}
        ORDER BY etzem_score DESC
        LIMIT ?
        """, (limit,))
        
        rows = cursor.fetchall()
        conn.close()
        
        return [
            cls(
                identity_id=row[0],
                etzem_score=row[1],
                components=json.loads(row[2]),
                badges=json.loads(row[3]),
                last_updated=row[4]
            )
            for row in rows
        ]
    
    @classmethod
    def find_by_badge(cls, badge: str) -> List["Etzem"]:
        """
        Find Etzem scores by badge.
        
        Args:
            badge: The badge to search for
        
        Returns:
            A list of Etzem scores with the badge
        """
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()
        
        cursor.execute(f"""
        SELECT
            {cls.PRIMARY_KEY},
            etzem_score,
            components,
            badges,
            last_updated
        FROM {cls.TABLE_NAME}
        """)
        
        rows = cursor.fetchall()
        conn.close()
        
        # Filter Etzem scores with the badge
        result = []
        for row in rows:
            badges = json.loads(row[3])
            if badge in badges:
                result.append(cls(
                    identity_id=row[0],
                    etzem_score=row[1],
                    components=json.loads(row[2]),
                    badges=badges,
                    last_updated=row[4]
                ))
        
        return result
    
    def add_badge(self, badge: str) -> None:
        """
        Add a badge to the Etzem.
        
        Args:
            badge: The badge to add
        """
        if badge not in self.badges:
            self.badges.append(badge)
            self.last_updated = int(time.time())
            self.save()
    
    def remove_badge(self, badge: str) -> None:
        """
        Remove a badge from the Etzem.
        
        Args:
            badge: The badge to remove
        """
        if badge in self.badges:
            self.badges.remove(badge)
            self.last_updated = int(time.time())
            self.save()
    
    def update_score(self, etzem_score: int, components: Dict[str, int]) -> None:
        """
        Update the Etzem score.
        
        Args:
            etzem_score: The new Etzem score
            components: The new components of the Etzem score
        """
        self.etzem_score = etzem_score
        self.components = components
        self.last_updated = int(time.time())
        self.save()
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the Etzem to a dictionary.
        
        Returns:
            The Etzem as a dictionary
        """
        return {
            "identity_id": self.identity_id,
            "etzem_score": self.etzem_score,
            "components": self.components,
            "badges": self.badges,
            "last_updated": self.last_updated
        }
