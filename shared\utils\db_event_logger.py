"""
Onnyx Database-Backed Event Logger Module

This module provides a SQLite-backed event logger implementation.
"""

import os
import json
import time
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from shared.db.db import db

# Set up logging
logger = logging.getLogger("onnyx.analytics.db_event_logger")

class DBEventLogger:
    """
    DBEventLogger logs blockchain events for analytics purposes using a SQLite database.
    """
    
    def __init__(self):
        """Initialize the DBEventLogger."""
        # Check if the event_logs table exists
        if not db.table_exists("event_logs"):
            logger.warning("Event logs table does not exist.")
    
    def log_block(self, block: Dict[str, Any], txs: List[Dict[str, Any]], proposer: str) -> Dict[str, Any]:
        """
        Log a block and its transactions.
        
        Args:
            block: The block to log
            txs: The transactions in the block
            proposer: The identity ID of the block proposer
        
        Returns:
            The log entry
        """
        try:
            # Create the log entry
            log_entry = {
                "block_index": block["index"],
                "block_hash": block["hash"],
                "timestamp": block["timestamp"],
                "timestamp_human": datetime.fromtimestamp(block["timestamp"]).strftime("%Y-%m-%d %H:%M:%S"),
                "proposer": proposer,
                "tx_count": len(txs),
                "token_mints": sum(1 for t in txs if t.get("op") == "OP_MINT"),
                "token_transfers": sum(1 for t in txs if t.get("op") == "OP_SEND"),
                "token_burns": sum(1 for t in txs if t.get("op") == "OP_BURN"),
                "proposals": sum(1 for t in txs if t.get("op") == "OP_SCROLL"),
                "votes": sum(1 for t in txs if t.get("op") == "OP_VOTE"),
                "identities": sum(1 for t in txs if t.get("op") == "OP_IDENTITY"),
                "reputation_grants": sum(1 for t in txs if t.get("op") == "OP_GRANT_REPUTATION"),
                "stakes": sum(1 for t in txs if t.get("op") == "OP_STAKE"),
                "rewards": sum(1 for t in txs if t.get("op") == "OP_REWARD"),
                "notable_events": json.dumps(self._extract_notable_events(txs))
            }
            
            # Insert the log entry into the database
            db.insert("event_logs", log_entry)
            
            logger.info(f"Logged block {block['index']} with {len(txs)} transactions")
            
            return log_entry
        except Exception as e:
            logger.error(f"Error logging block: {str(e)}")
            raise
    
    def _extract_notable_events(self, txs: List[Dict[str, Any]]) -> List[str]:
        """
        Extract notable events from transactions.
        
        Args:
            txs: The transactions to extract events from
        
        Returns:
            A list of notable events
        """
        notable_events = []
        
        for tx in txs:
            op = tx.get("op")
            
            if not op:
                continue
            
            if op == "OP_MINT":
                token_id = tx.get("token_id") or tx.get("data", {}).get("token_id")
                amount = tx.get("amount") or tx.get("data", {}).get("amount")
                to = tx.get("to") or tx.get("data", {}).get("to")
                
                if token_id and amount and to:
                    notable_events.append(f"MINT: {amount} {token_id} to {to}")
            
            elif op == "OP_SEND":
                token_id = tx.get("token_id") or tx.get("data", {}).get("token_id")
                amount = tx.get("amount") or tx.get("data", {}).get("amount")
                from_id = tx.get("from") or tx.get("data", {}).get("from")
                to = tx.get("to") or tx.get("data", {}).get("to")
                
                if token_id and amount and from_id and to:
                    notable_events.append(f"TRANSFER: {amount} {token_id} from {from_id} to {to}")
            
            elif op == "OP_BURN":
                token_id = tx.get("token_id") or tx.get("data", {}).get("token_id")
                amount = tx.get("amount") or tx.get("data", {}).get("amount")
                from_id = tx.get("from") or tx.get("data", {}).get("from")
                
                if token_id and amount and from_id:
                    notable_events.append(f"BURN: {amount} {token_id} by {from_id}")
            
            elif op == "OP_SCROLL":
                title = tx.get("data", {}).get("title")
                category = tx.get("data", {}).get("category")
                from_id = tx.get("from") or tx.get("data", {}).get("from")
                
                if title and from_id:
                    notable_events.append(f"PROPOSAL: '{title}' ({category}) by {from_id}")
            
            elif op == "OP_VOTE":
                scroll_id = tx.get("data", {}).get("scroll_id")
                vote = tx.get("data", {}).get("vote")
                from_id = tx.get("from") or tx.get("data", {}).get("from")
                
                if scroll_id and vote and from_id:
                    notable_events.append(f"VOTE: {from_id} voted {vote} on {scroll_id}")
            
            elif op == "OP_IDENTITY":
                identity_id = tx.get("data", {}).get("identity_id")
                name = tx.get("data", {}).get("name")
                
                if identity_id and name:
                    notable_events.append(f"IDENTITY: {name} ({identity_id}) registered")
            
            elif op == "OP_GRANT_REPUTATION":
                to = tx.get("to") or tx.get("data", {}).get("to")
                amount = tx.get("amount") or tx.get("data", {}).get("amount")
                from_id = tx.get("from") or tx.get("data", {}).get("from")
                
                if to and amount and from_id:
                    notable_events.append(f"REPUTATION: {amount} granted to {to} by {from_id}")
            
            elif op == "OP_STAKE":
                token_id = tx.get("token_id") or tx.get("data", {}).get("token_id")
                amount = tx.get("amount") or tx.get("data", {}).get("amount")
                from_id = tx.get("from") or tx.get("data", {}).get("from")
                
                if token_id and amount and from_id:
                    notable_events.append(f"STAKE: {amount} {token_id} by {from_id}")
            
            elif op == "OP_REWARD":
                token_id = tx.get("token_id") or tx.get("data", {}).get("token_id")
                amount = tx.get("amount") or tx.get("data", {}).get("amount")
                to = tx.get("to") or tx.get("data", {}).get("to")
                
                if token_id and amount and to:
                    notable_events.append(f"REWARD: {amount} {token_id} to {to}")
        
        return notable_events
    
    def get_logs(self, limit: Optional[int] = None, offset: int = 0) -> List[Dict[str, Any]]:
        """
        Get event logs.
        
        Args:
            limit: Maximum number of logs to return (optional)
            offset: Offset for pagination
        
        Returns:
            A list of event logs
        """
        try:
            # Query the database for the logs
            logs = db.query(
                "SELECT * FROM event_logs ORDER BY block_index DESC LIMIT ? OFFSET ?",
                (limit or 100, offset)
            )
            
            # Process the logs
            for log in logs:
                # Parse the notable_events field
                log["notable_events"] = json.loads(log["notable_events"]) if log["notable_events"] else []
            
            return logs
        except Exception as e:
            logger.error(f"Error getting logs: {str(e)}")
            return []
    
    def get_log(self, block_index: int) -> Optional[Dict[str, Any]]:
        """
        Get an event log by block index.
        
        Args:
            block_index: The block index
        
        Returns:
            The event log or None if not found
        """
        try:
            # Query the database for the log
            log = db.query_one("SELECT * FROM event_logs WHERE block_index = ?", (block_index,))
            
            if log:
                # Parse the notable_events field
                log["notable_events"] = json.loads(log["notable_events"]) if log["notable_events"] else []
            
            return log
        except Exception as e:
            logger.error(f"Error getting log: {str(e)}")
            return None
    
    def generate_summary(self, days: int = 7) -> Dict[str, Any]:
        """
        Generate a summary of event logs for a given number of days.
        
        Args:
            days: Number of days to include in the summary
        
        Returns:
            A summary of event logs
        """
        try:
            # Calculate the cutoff timestamp
            now = int(time.time())
            cutoff = now - (days * 86400)  # Convert days to seconds
            
            # Query the database for the summary
            summary = db.query_one(
                """
                SELECT
                    COUNT(*) as blocks,
                    SUM(tx_count) as transactions,
                    SUM(token_mints) as token_mints,
                    SUM(token_transfers) as token_transfers,
                    SUM(token_burns) as token_burns,
                    SUM(proposals) as proposals,
                    SUM(votes) as votes,
                    SUM(identities) as identities,
                    SUM(reputation_grants) as reputation_grants,
                    SUM(stakes) as stakes,
                    SUM(rewards) as rewards,
                    MIN(timestamp) as start_timestamp,
                    MAX(timestamp) as end_timestamp
                FROM event_logs
                WHERE timestamp >= ?
                """,
                (cutoff,)
            )
            
            if not summary:
                return {
                    "blocks": 0,
                    "transactions": 0,
                    "token_mints": 0,
                    "token_transfers": 0,
                    "token_burns": 0,
                    "proposals": 0,
                    "votes": 0,
                    "identities": 0,
                    "reputation_grants": 0,
                    "stakes": 0,
                    "rewards": 0,
                    "unique_proposers": [],
                    "unique_proposer_count": 0,
                    "start_timestamp": 0,
                    "end_timestamp": 0
                }
            
            # Get unique proposers
            proposers = db.query(
                """
                SELECT DISTINCT proposer
                FROM event_logs
                WHERE timestamp >= ?
                """,
                (cutoff,)
            )
            
            unique_proposers = [p["proposer"] for p in proposers]
            
            # Add human-readable timestamps
            summary["start_timestamp_human"] = datetime.fromtimestamp(
                summary["start_timestamp"]
            ).strftime("%Y-%m-%d %H:%M:%S") if summary["start_timestamp"] else ""
            
            summary["end_timestamp_human"] = datetime.fromtimestamp(
                summary["end_timestamp"]
            ).strftime("%Y-%m-%d %H:%M:%S") if summary["end_timestamp"] else ""
            
            # Add unique proposers
            summary["unique_proposers"] = unique_proposers
            summary["unique_proposer_count"] = len(unique_proposers)
            
            return summary
        except Exception as e:
            logger.error(f"Error generating summary: {str(e)}")
            return {
                "blocks": 0,
                "transactions": 0,
                "token_mints": 0,
                "token_transfers": 0,
                "token_burns": 0,
                "proposals": 0,
                "votes": 0,
                "identities": 0,
                "reputation_grants": 0,
                "stakes": 0,
                "rewards": 0,
                "unique_proposers": [],
                "unique_proposer_count": 0,
                "start_timestamp": 0,
                "end_timestamp": 0
            }

# Create a global instance of the DBEventLogger
db_event_logger = DBEventLogger()
