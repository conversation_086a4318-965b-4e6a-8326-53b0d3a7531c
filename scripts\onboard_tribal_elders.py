#!/usr/bin/env python3
"""
ONNYX Tribal Elder Onboarding System
Creates the initial Council of Twelve Tribes
"""

import sys
import os
import json
import time
import uuid
import hashlib

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from shared.db.db import db

# The Twelve Tribes with their voting weights
TWELVE_TRIBES = [
    {"code": "JU", "name": "<PERSON>", "full_name": "The Royal Tribe", "voting_weight": 2, "role": "<PERSON>"},
    {"code": "LE", "name": "<PERSON>", "full_name": "The Priestly Tribe", "voting_weight": 2, "role": "<PERSON><PERSON>"},
    {"code": "EP", "name": "<PERSON><PERSON><PERSON><PERSON>", "full_name": "The Fruitful Tribe", "voting_weight": 2, "role": "Fruitful"},
    {"code": "BE", "name": "<PERSON>", "full_name": "Son of the Right Hand", "voting_weight": 1, "role": "Standard"},
    {"code": "SI", "name": "<PERSON><PERSON><PERSON>", "full_name": "He Who Hears", "voting_weight": 1, "role": "Standard"},
    {"code": "MA", "name": "Manasseh", "full_name": "Making to Forget", "voting_weight": 1, "role": "Standard"},
    {"code": "IS", "name": "Issachar", "full_name": "There is Recompense", "voting_weight": 1, "role": "Standard"},
    {"code": "ZE", "name": "Zebulun", "full_name": "Dwelling", "voting_weight": 1, "role": "Standard"},
    {"code": "NA", "name": "Naphtali", "full_name": "My Wrestling", "voting_weight": 1, "role": "Standard"},
    {"code": "GA", "name": "Gad", "full_name": "A Troop", "voting_weight": 1, "role": "Standard"},
    {"code": "AS", "name": "Asher", "full_name": "Happy", "voting_weight": 1, "role": "Standard"},
    {"code": "RE", "name": "Reuben", "full_name": "See, a Son", "voting_weight": 1, "role": "Standard"}
]

def create_tribal_elder_identity(tribe_info, elder_name=None):
    """Create a soulbound identity for a tribal elder."""
    
    if not elder_name:
        elder_name = f"Elder of {tribe_info['name']}"
    
    # Generate identity
    identity_id = str(uuid.uuid4())
    public_key = hashlib.sha256(f"{identity_id}:{tribe_info['code']}".encode()).hexdigest()
    
    # Create metadata
    metadata = {
        "tribe_code": tribe_info["code"],
        "tribe_name": tribe_info["name"],
        "tribal_role": "Elder",
        "council_member": True,
        "voting_weight": tribe_info["voting_weight"],
        "tribal_authority": tribe_info["role"],
        "founded_selas": [],
        "joined_selas": [],
        "badges": ["Tribal_Elder", "Council_Member", f"{tribe_info['name']}_Elder"],
        "reputation": 1000,  # High starting reputation for elders
        "covenant_comprehension_score": 5,  # Perfect score
        "onboarding_completed": True
    }
    
    current_time = int(time.time())
    
    try:
        # Insert into identities table
        db.execute("""
            INSERT INTO identities (
                identity_id, name, email, public_key, nation_id, metadata,
                status, created_at, updated_at, nation_of_origin, role_class,
                etzem_score, verification_level, covenant_accepted, vault_status,
                nation_code, nation_name
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            identity_id,
            elder_name,
            f"{tribe_info['code'].lower()}.<EMAIL>",
            public_key,
            tribe_info["code"],
            json.dumps(metadata),
            "active",
            current_time,
            current_time,
            tribe_info["code"],
            "Tribal_Elder",
            1000,  # High Etzem score
            1,     # Tier 1 verification
            True,  # Covenant accepted
            "Active",
            tribe_info["code"],
            tribe_info["name"]
        ))
        
        print(f"✅ Created identity for {elder_name} ({tribe_info['name']})")
        print(f"   Identity ID: {identity_id}")
        print(f"   Voting Weight: {tribe_info['voting_weight']}")
        print(f"   Tribal Role: {tribe_info['role']}")
        
        return identity_id
        
    except Exception as e:
        print(f"❌ Error creating identity for {tribe_info['name']}: {e}")
        return None

def register_tribal_council():
    """Register all twelve tribal elders and form the council."""
    print("👑 FORMING THE COUNCIL OF TWELVE TRIBES")
    print("=" * 50)
    
    council_members = []
    total_voting_weight = 0
    
    for tribe in TWELVE_TRIBES:
        print(f"\n🏛️ Registering Elder of {tribe['name']}...")
        
        identity_id = create_tribal_elder_identity(tribe)
        if identity_id:
            council_members.append({
                "identity_id": identity_id,
                "tribe_code": tribe["code"],
                "tribe_name": tribe["name"],
                "voting_weight": tribe["voting_weight"],
                "role": tribe["role"]
            })
            total_voting_weight += tribe["voting_weight"]
        else:
            print(f"❌ Failed to register elder for {tribe['name']}")
            return False
    
    print(f"\n📊 COUNCIL FORMATION SUMMARY:")
    print(f"   👥 Total Elders: {len(council_members)}")
    print(f"   🗳️ Total Voting Weight: {total_voting_weight}")
    print(f"   👑 Royal Tribes (2 votes): Judah, Levi, Ephraim")
    print(f"   🏛️ Standard Tribes (1 vote): {len([t for t in TWELVE_TRIBES if t['voting_weight'] == 1])}")
    
    # Create council record
    try:
        council_data = {
            "formation_date": int(time.time()),
            "total_members": len(council_members),
            "total_voting_weight": total_voting_weight,
            "members": council_members,
            "status": "active",
            "quorum_requirement": 0.6,  # 60% quorum
            "majority_requirement": 0.67  # 2/3 majority
        }
        
        # Store council configuration
        db.execute("""
            INSERT OR REPLACE INTO system_config (key, value, updated_at)
            VALUES (?, ?, ?)
        """, (
            "tribal_council",
            json.dumps(council_data),
            int(time.time())
        ))
        
        print(f"\n🎉 COUNCIL OF TWELVE TRIBES SUCCESSFULLY FORMED!")
        print(f"✅ All {len(council_members)} tribal elders registered")
        print(f"✅ Council configuration saved to database")
        print(f"✅ Ready for governance activation")
        
        return True
        
    except Exception as e:
        print(f"❌ Error saving council configuration: {e}")
        return False

def verify_council_formation():
    """Verify that the council was properly formed."""
    print("\n🔍 VERIFYING COUNCIL FORMATION...")
    
    try:
        # Check tribal elders
        elders = db.query("""
            SELECT nation_code, name, verification_level, covenant_accepted, role_class
            FROM identities 
            WHERE role_class = 'Tribal_Elder'
            ORDER BY nation_code
        """)
        
        print(f"📊 Found {len(elders)} tribal elders:")
        for elder in elders:
            print(f"   ✅ {elder['nation_code']}: {elder['name']} (Tier {elder['verification_level']})")
        
        # Check council configuration
        council_config = db.query_one("""
            SELECT value FROM system_config WHERE key = 'tribal_council'
        """)
        
        if council_config:
            council_data = json.loads(council_config['value'])
            print(f"\n🏛️ Council Configuration:")
            print(f"   👥 Members: {council_data['total_members']}")
            print(f"   🗳️ Voting Weight: {council_data['total_voting_weight']}")
            print(f"   📊 Quorum: {council_data['quorum_requirement']*100}%")
            print(f"   ✅ Status: {council_data['status']}")
            
            if len(elders) == 12 and council_data['total_members'] == 12:
                print(f"\n🎉 COUNCIL VERIFICATION SUCCESSFUL!")
                print(f"✅ All 12 tribes represented")
                print(f"✅ Council ready for governance")
                return True
            else:
                print(f"\n⚠️ Council incomplete: {len(elders)}/12 elders")
                return False
        else:
            print(f"\n❌ Council configuration not found")
            return False
            
    except Exception as e:
        print(f"❌ Verification error: {e}")
        return False

def main():
    """Main function to onboard tribal elders."""
    print("🌟 ONNYX TRIBAL ELDER ONBOARDING")
    print("=" * 50)
    
    # Check if council already exists
    try:
        existing_elders = db.query("SELECT COUNT(*) as count FROM identities WHERE role_class = 'Tribal_Elder'")
        elder_count = existing_elders[0]['count'] if existing_elders else 0
        
        if elder_count > 0:
            print(f"⚠️ Found {elder_count} existing tribal elders")
            response = input("Do you want to proceed and create additional elders? (y/N): ")
            if response.lower() != 'y':
                print("Onboarding cancelled")
                return False
    except Exception as e:
        print(f"Warning: Could not check existing elders: {e}")
    
    # Register the council
    success = register_tribal_council()
    
    if success:
        # Verify formation
        verification_success = verify_council_formation()
        
        if verification_success:
            print(f"\n🚀 READY FOR STEP 9: GENESIS VOICE SCROLLS DEPLOYMENT")
            print(f"Next actions:")
            print(f"1. Deploy the 3 Genesis Voice Scrolls")
            print(f"2. Begin first governance voting cycle")
            print(f"3. Establish ongoing council procedures")
            return True
        else:
            print(f"\n⚠️ Council verification failed")
            return False
    else:
        print(f"\n❌ Council formation failed")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
