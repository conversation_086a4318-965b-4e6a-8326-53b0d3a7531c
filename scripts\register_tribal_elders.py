#!/usr/bin/env python3
"""
Register All 12 Tribal Elders
Creates the complete Council of Twelve Tribes via API calls
"""

import sys
import os
import json
import time
import requests

# The Twelve Tribes with their details
TWELVE_TRIBES = [
    {"code": "JU", "name": "<PERSON>", "elder_name": "Elder <PERSON><PERSON><PERSON> ben <PERSON>", "voting_weight": 2, "role": "<PERSON>"},
    {"code": "LE", "name": "<PERSON>", "elder_name": "Elder <PERSON> ben <PERSON>", "voting_weight": 2, "role": "<PERSON><PERSON>"},
    {"code": "EP", "name": "<PERSON><PERSON><PERSON><PERSON>", "elder_name": "Elder <PERSON><PERSON><PERSON> ben <PERSON>", "voting_weight": 2, "role": "Fruitful"},
    {"code": "BE", "name": "<PERSON>", "elder_name": "<PERSON> ben <PERSON>", "voting_weight": 1, "role": "Standard"},
    {"code": "SI", "name": "<PERSON><PERSON><PERSON>", "elder_name": "<PERSON><PERSON> ben <PERSON>", "voting_weight": 1, "role": "Standard"},
    {"code": "<PERSON>", "name": "<PERSON><PERSON><PERSON>", "elder_name": "<PERSON> <PERSON><PERSON><PERSON> ben <PERSON>", "voting_weight": 1, "role": "Standard"},
    {"code": "IS", "name": "Issachar", "elder_name": "Elder Issachar ben <PERSON>", "voting_weight": 1, "role": "Standard"},
    {"code": "ZE", "name": "<PERSON><PERSON><PERSON>n", "elder_name": "Elder <PERSON>ebulun ben <PERSON>", "voting_weight": 1, "role": "Standard"},
    {"code": "N<PERSON>", "name": "Naphtali", "elder_name": "Elder Naphtali ben Jacob", "voting_weight": 1, "role": "Standard"},
    {"code": "GA", "name": "Gad", "elder_name": "Elder Gad ben Jacob", "voting_weight": 1, "role": "Standard"},
    {"code": "AS", "name": "Asher", "elder_name": "Elder Asher ben Jacob", "voting_weight": 1, "role": "Standard"},
    {"code": "RE", "name": "Reuben", "elder_name": "Elder Reuben ben Jacob", "voting_weight": 1, "role": "Standard"}
]

def register_tribal_elder(tribe):
    """Register a single tribal elder via API."""
    print(f"👑 Registering Elder of {tribe['name']}...")
    
    try:
        response = requests.post(
            'http://localhost:5000/genesis/api/create-elder',
            json={
                'tribe_code': tribe['code'],
                'elder_name': tribe['elder_name']
            },
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"   ✅ {result['tribe']} elder registered")
                print(f"   📊 Voting Weight: {result['voting_weight']}")
                print(f"   🏛️ Role: {result['role']}")
                return True
            else:
                print(f"   ❌ API Error: {result.get('error', 'Unknown error')}")
                return False
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Request Error: {e}")
        return False

def register_all_elders():
    """Register all 12 tribal elders."""
    print("🌟 REGISTERING COUNCIL OF TWELVE TRIBES")
    print("=" * 50)
    
    registered_count = 0
    total_voting_weight = 0
    
    for tribe in TWELVE_TRIBES:
        if register_tribal_elder(tribe):
            registered_count += 1
            total_voting_weight += tribe['voting_weight']
            time.sleep(1)  # Brief pause between registrations
        else:
            print(f"⚠️ Failed to register {tribe['name']} elder")
    
    print(f"\n📊 REGISTRATION SUMMARY:")
    print(f"   👥 Elders Registered: {registered_count}/12")
    print(f"   🗳️ Total Voting Weight: {total_voting_weight}")
    
    if registered_count == 12:
        print(f"\n🎉 COUNCIL OF TWELVE TRIBES COMPLETE!")
        print(f"✅ All tribal elders successfully registered")
        print(f"✅ Ready for Genesis Voice Scrolls deployment")
        return True
    else:
        print(f"\n⚠️ Council formation incomplete: {registered_count}/12")
        return False

def verify_council_status():
    """Verify the council formation via API."""
    print(f"\n🔍 Verifying Council Formation...")
    
    try:
        response = requests.get('http://localhost:5000/genesis/api/elders', timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            elders = result.get('elders', [])
            
            print(f"📊 Found {len(elders)} registered elders:")
            for elder in elders:
                print(f"   ✅ {elder['tribe_code']}: {elder['name']}")
            
            if len(elders) >= 12:
                print(f"\n🎉 Council verification successful!")
                return True
            else:
                print(f"\n⚠️ Council incomplete: {len(elders)}/12")
                return False
        else:
            print(f"❌ API Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Verification Error: {e}")
        return False

def main():
    """Main function to register all tribal elders."""
    print("🔥 STEP 10 PHASE 1B: TRIBAL ELDER REGISTRATION")
    print("=" * 60)
    
    # Register all elders
    if register_all_elders():
        # Verify registration
        if verify_council_status():
            print(f"\n🚀 READY FOR PHASE 1C: GENESIS VOICE SCROLLS DEPLOYMENT")
            return True
    
    print(f"\n❌ Tribal elder registration incomplete")
    return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
