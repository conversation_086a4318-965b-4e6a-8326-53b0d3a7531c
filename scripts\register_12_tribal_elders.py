#!/usr/bin/env python3
"""
Register 12 Tribal Elders
Creates the Council of Twelve Tribes with proper voting weights
"""

import sys
import os
import json
import time
import uuid
import hashlib
import sqlite3

# The Twelve Tribes with their voting weights
TWELVE_TRIBES = [
    {"code": "J<PERSON>", "name": "<PERSON>", "full_name": "The Royal Tribe", "voting_weight": 2, "role": "<PERSON>"},
    {"code": "LE", "name": "<PERSON>", "full_name": "The Priestly Tribe", "voting_weight": 2, "role": "<PERSON><PERSON>"},
    {"code": "EP", "name": "<PERSON><PERSON><PERSON><PERSON>", "full_name": "The Fruitful Tribe", "voting_weight": 2, "role": "Fruitful"},
    {"code": "BE", "name": "<PERSON>", "full_name": "Son of the Right Hand", "voting_weight": 1, "role": "Standard"},
    {"code": "SI", "name": "<PERSON><PERSON><PERSON>", "full_name": "<PERSON> Who Hears", "voting_weight": 1, "role": "Standard"},
    {"code": "<PERSON>", "name": "<PERSON><PERSON><PERSON>", "full_name": "Making to Forget", "voting_weight": 1, "role": "Standard"},
    {"code": "IS", "name": "Issachar", "full_name": "There is Recompense", "voting_weight": 1, "role": "Standard"},
    {"code": "ZE", "name": "Zebulun", "full_name": "Dwelling", "voting_weight": 1, "role": "Standard"},
    {"code": "NA", "name": "Naphtali", "full_name": "My Wrestling", "voting_weight": 1, "role": "Standard"},
    {"code": "GA", "name": "Gad", "full_name": "A Troop", "voting_weight": 1, "role": "Standard"},
    {"code": "AS", "name": "Asher", "full_name": "Happy", "voting_weight": 1, "role": "Standard"},
    {"code": "DA", "name": "Dan", "full_name": "Judge", "voting_weight": 1, "role": "Standard"}
]

def get_db_connection():
    """Get database connection."""
    db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'shared', 'db', 'db', 'onnyx.db')
    return sqlite3.connect(db_path)

def create_tribal_elder(tribe_info):
    """Create a tribal elder identity."""
    elder_name = f"Elder {tribe_info['name']} ben Jacob"
    
    # Generate identity
    identity_id = f"elder_{tribe_info['code'].lower()}_{uuid.uuid4().hex[:8]}"
    public_key = hashlib.sha256(f"{identity_id}:{tribe_info['code']}".encode()).hexdigest()
    
    # Create metadata
    metadata = {
        "tribe_code": tribe_info["code"],
        "tribe_name": tribe_info["name"],
        "tribal_role": "Elder",
        "council_member": True,
        "voting_weight": tribe_info["voting_weight"],
        "tribal_authority": tribe_info["role"],
        "founded_selas": [],
        "joined_selas": [],
        "badges": ["Tribal_Elder", "Council_Member", f"{tribe_info['name']}_Elder"],
        "reputation": 1000,
        "covenant_comprehension_score": 5,
        "onboarding_completed": True
    }
    
    current_time = int(time.time())
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Insert into identities table
        cursor.execute("""
            INSERT INTO identities (
                identity_id, name, email, public_key, metadata,
                status, created_at, updated_at, nation_of_origin, role_class,
                etzem_score, verification_level, covenant_accepted, vault_status,
                nation_code, nation_name, tribal_affiliation
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            identity_id,
            elder_name,
            f"{tribe_info['code'].lower()}.<EMAIL>",
            public_key,
            json.dumps(metadata),
            "active",
            current_time,
            current_time,
            tribe_info["code"],
            "Tribal_Elder",
            1000,  # High Etzem score
            3,     # Tier 3 verification (highest)
            True,  # Covenant accepted
            "Active",
            tribe_info["code"],
            tribe_info["name"],
            tribe_info["code"]
        ))
        
        conn.commit()
        conn.close()
        
        print(f"✅ Created elder for {tribe_info['name']} tribe")
        print(f"   Name: {elder_name}")
        print(f"   Identity ID: {identity_id}")
        print(f"   Voting Weight: {tribe_info['voting_weight']}")
        print(f"   Role: {tribe_info['role']}")
        
        return identity_id
        
    except Exception as e:
        print(f"❌ Error creating elder for {tribe_info['name']}: {e}")
        return None

def register_tribal_council():
    """Register all twelve tribal elders and form the council."""
    print("👑 FORMING THE COUNCIL OF TWELVE TRIBES")
    print("=" * 50)
    
    council_members = []
    total_voting_weight = 0
    
    for tribe in TWELVE_TRIBES:
        print(f"\n🏛️ Registering Elder of {tribe['name']}...")
        
        identity_id = create_tribal_elder(tribe)
        if identity_id:
            council_members.append({
                "identity_id": identity_id,
                "tribe_code": tribe["code"],
                "tribe_name": tribe["name"],
                "voting_weight": tribe["voting_weight"],
                "role": tribe["role"]
            })
            total_voting_weight += tribe["voting_weight"]
        else:
            print(f"❌ Failed to register elder for {tribe['name']}")
            return False
    
    print(f"\n📊 COUNCIL FORMATION SUMMARY:")
    print(f"   👥 Total Elders: {len(council_members)}")
    print(f"   🗳️ Total Voting Weight: {total_voting_weight}")
    print(f"   👑 Royal Tribes (2 votes): Judah, Levi, Ephraim")
    print(f"   🏛️ Standard Tribes (1 vote): {len([t for t in TWELVE_TRIBES if t['voting_weight'] == 1])}")
    
    # Create council record
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        council_data = {
            "formation_date": int(time.time()),
            "total_members": len(council_members),
            "total_voting_weight": total_voting_weight,
            "members": council_members,
            "status": "active",
            "quorum_requirement": 0.6,  # 60% quorum
            "majority_requirement": 0.67  # 2/3 majority
        }
        
        # Store council configuration
        cursor.execute("""
            INSERT OR REPLACE INTO system_config (key, value, updated_at)
            VALUES (?, ?, ?)
        """, (
            "tribal_council",
            json.dumps(council_data),
            int(time.time())
        ))
        
        conn.commit()
        conn.close()
        
        print(f"\n🎉 COUNCIL OF TWELVE TRIBES SUCCESSFULLY FORMED!")
        print(f"✅ All {len(council_members)} tribal elders registered")
        print(f"✅ Council configuration saved to database")
        print(f"✅ Ready for governance activation")
        
        return True
        
    except Exception as e:
        print(f"❌ Error saving council configuration: {e}")
        return False

def verify_council():
    """Verify the council was properly formed."""
    print("\n🔍 VERIFYING COUNCIL FORMATION...")
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Check tribal elders
        cursor.execute("""
            SELECT nation_code, name, verification_level, covenant_accepted, role_class, tribal_affiliation
            FROM identities 
            WHERE role_class = 'Tribal_Elder'
            ORDER BY nation_code
        """)
        elders = cursor.fetchall()
        
        print(f"📊 Found {len(elders)} tribal elders:")
        for elder in elders:
            print(f"   ✅ {elder[0]}: {elder[1]} (Tier {elder[2]})")
        
        # Check council configuration
        cursor.execute("SELECT value FROM system_config WHERE key = 'tribal_council'")
        council_result = cursor.fetchone()
        
        conn.close()
        
        if council_result:
            council_data = json.loads(council_result[0])
            print(f"\n🏛️ Council Configuration:")
            print(f"   👥 Members: {council_data['total_members']}")
            print(f"   🗳️ Voting Weight: {council_data['total_voting_weight']}")
            print(f"   📊 Quorum: {council_data['quorum_requirement']*100}%")
            print(f"   ✅ Status: {council_data['status']}")
            
            if len(elders) == 12 and council_data['total_members'] == 12:
                print(f"\n🎉 COUNCIL VERIFICATION SUCCESSFUL!")
                print(f"✅ All 12 tribes represented")
                print(f"✅ Council ready for governance")
                return True
            else:
                print(f"\n⚠️ Council incomplete: {len(elders)}/12 elders")
                return False
        else:
            print(f"\n❌ Council configuration not found")
            return False
            
    except Exception as e:
        print(f"❌ Verification error: {e}")
        return False

def main():
    """Main function."""
    print("🌟 ONNYX TRIBAL ELDER REGISTRATION")
    print("=" * 50)
    
    # Check if elders already exist
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM identities WHERE role_class = 'Tribal_Elder'")
        elder_count = cursor.fetchone()[0]
        conn.close()
        
        if elder_count > 0:
            print(f"⚠️ Found {elder_count} existing tribal elders")
            response = input("Do you want to proceed and create additional elders? (y/N): ")
            if response.lower() != 'y':
                print("Registration cancelled")
                return False
    except Exception as e:
        print(f"Warning: Could not check existing elders: {e}")
    
    # Register the council
    if register_tribal_council():
        if verify_council():
            print(f"\n🚀 TRIBAL ELDERS SUCCESSFULLY REGISTERED!")
            return True
        else:
            print(f"\n⚠️ Council verification failed")
            return False
    else:
        print(f"\n❌ Council formation failed")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
