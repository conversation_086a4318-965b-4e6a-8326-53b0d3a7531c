"""
ONNYX Tribal Elder Validator Node
Implements tribal elder validation nodes with weighted voting and biblical governance
"""

import asyncio
import json
import time
import logging
import uuid
from typing import Dict, List, Optional
from dataclasses import dataclass

from network.p2p.peer_manager import P<PERSON><PERSON>anager, NodeType
from network.consensus.proof_of_covenant import ProofOf<PERSON>ovenant, VoteType
from network.discovery.bootstrap import DiscoveryClient
from network.sync.blockchain_sync import BlockchainSync

logger = logging.getLogger(__name__)

@dataclass
class TribalElderConfig:
    tribal_code: str
    elder_name: str
    voting_weight: int
    port: int
    bootstrap_nodes: List[tuple]
    identity_id: str

class TribalElderNode:
    """
    Tribal Elder Validator Node for ONNYX covenant blockchain
    Implements weighted voting and biblical governance validation
    """
    
    def __init__(self, config: TribalElderConfig):
        self.config = config
        self.node_id = f"elder_{config.tribal_code.lower()}_{int(time.time())}"
        self.running = False
        
        # Initialize P2P components
        self.peer_manager = PeerManager(
            node_id=self.node_id,
            node_type=NodeType.TRIBAL_ELDER,
            port=config.port
        )
        
        # Initialize consensus
        self.consensus = ProofOfCovenant(self.peer_manager)
        
        # Initialize blockchain sync
        self.blockchain_sync = BlockchainSync(self.peer_manager)
        
        # Initialize discovery client
        self.discovery_client = DiscoveryClient(config.bootstrap_nodes)
        
        # Tribal elder specific attributes
        self.tribal_code = config.tribal_code
        self.voting_weight = config.voting_weight
        self.elder_name = config.elder_name
        self.identity_id = config.identity_id
        
        # Governance state
        self.active_votes: Dict[str, dict] = {}
        self.governance_history: List[dict] = []
        
        # Biblical compliance tracking
        self.sabbath_enforcement = True
        self.anti_usury_enforcement = True
        self.gleaning_pool_participation = True
        
        logger.info(f"Initialized Tribal Elder Node: {self.elder_name} ({self.tribal_code})")
    
    async def start(self) -> bool:
        """Start the tribal elder node"""
        try:
            logger.info(f"Starting Tribal Elder Node: {self.elder_name}")
            
            # Start P2P server
            await self.peer_manager.start_server()
            
            # Register with bootstrap nodes
            peer_info = {
                "peer_id": self.node_id,
                "address": "127.0.0.1",  # In production, would be actual IP
                "port": self.config.port,
                "node_type": "tribal_elder",
                "tribal_code": self.tribal_code,
                "voting_weight": self.voting_weight,
                "covenant_tier": 3,  # Maximum tier for elders
                "reputation": 150  # High reputation for elders
            }
            
            registration_success = await self.discovery_client.register_with_bootstrap(peer_info)
            if not registration_success:
                logger.warning("Failed to register with bootstrap nodes")
            
            # Discover and connect to other peers
            await self._discover_and_connect_peers()
            
            # Start blockchain synchronization
            await self.blockchain_sync.start_sync()
            
            # Start governance monitoring
            asyncio.create_task(self._governance_monitoring_loop())
            
            # Start biblical compliance monitoring
            asyncio.create_task(self._biblical_compliance_loop())
            
            self.running = True
            logger.info(f"Tribal Elder Node {self.elder_name} started successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start tribal elder node: {e}")
            return False
    
    async def stop(self):
        """Stop the tribal elder node"""
        try:
            self.running = False
            await self.peer_manager.stop_server()
            logger.info(f"Tribal Elder Node {self.elder_name} stopped")
        except Exception as e:
            logger.error(f"Error stopping tribal elder node: {e}")
    
    async def _discover_and_connect_peers(self):
        """Discover and connect to other network peers"""
        try:
            # Discover peers from bootstrap nodes
            peers = await self.discovery_client.discover_peers(
                requester_id=self.node_id,
                node_type="tribal_elder",
                max_peers=20
            )
            
            # Connect to discovered peers
            connection_count = 0
            for peer in peers:
                if peer['peer_id'] != self.node_id:
                    success = await self.peer_manager.connect_to_peer(
                        peer['address'], peer['port']
                    )
                    if success:
                        connection_count += 1
            
            logger.info(f"Connected to {connection_count} peers")
            
            # Get tribal elder information
            tribal_elders = await self.discovery_client.get_tribal_elders()
            logger.info(f"Discovered {len(tribal_elders)} tribal elders in network")
            
        except Exception as e:
            logger.error(f"Peer discovery error: {e}")
    
    async def vote_on_proposal(self, proposal_id: str, vote_type: VoteType, 
                              justification: str = None) -> bool:
        """Cast a vote on a governance proposal"""
        try:
            # Generate signature (simplified for demo)
            signature = f"elder_{self.tribal_code}_{proposal_id}_{vote_type.value}_{int(time.time())}"
            
            # Cast vote through consensus mechanism
            success = await self.consensus.cast_vote(
                proposal_id=proposal_id,
                vote_type=vote_type,
                voter_id=self.node_id,
                tribal_code=self.tribal_code,
                signature=signature,
                justification=justification
            )
            
            if success:
                # Record vote in local history
                vote_record = {
                    "proposal_id": proposal_id,
                    "vote_type": vote_type.value,
                    "justification": justification,
                    "timestamp": int(time.time()),
                    "tribal_code": self.tribal_code,
                    "voting_weight": self.voting_weight
                }
                
                self.active_votes[proposal_id] = vote_record
                self.governance_history.append(vote_record)
                
                logger.info(f"Elder {self.tribal_code} voted {vote_type.value} on proposal {proposal_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Voting error: {e}")
            return False
    
    async def propose_voice_scroll(self, title: str, content: dict, 
                                  biblical_references: List[str]) -> Optional[str]:
        """Propose a new voice scroll for governance"""
        try:
            scroll_data = {
                "title": title,
                "content": content,
                "biblical_references": biblical_references,
                "voting_requirements": {
                    "minimum_participation": 0.75,  # 75% of elders must participate
                    "approval_threshold": 0.75,     # 75% approval required
                    "voting_period_days": 7
                }
            }
            
            scroll_id = await self.consensus.propose_voice_scroll(scroll_data)
            
            if scroll_id:
                logger.info(f"Elder {self.tribal_code} proposed voice scroll: {title}")
                
                # Record proposal in governance history
                proposal_record = {
                    "type": "voice_scroll_proposal",
                    "scroll_id": scroll_id,
                    "title": title,
                    "proposer": self.tribal_code,
                    "timestamp": int(time.time())
                }
                self.governance_history.append(proposal_record)
            
            return scroll_id
            
        except Exception as e:
            logger.error(f"Voice scroll proposal error: {e}")
            return None
    
    async def validate_block_proposal(self, block_data: dict) -> bool:
        """Validate a block proposal for biblical compliance"""
        try:
            # Check biblical compliance
            compliance_score = await self._calculate_biblical_compliance(block_data)
            
            # Minimum compliance threshold for elder approval
            min_compliance = 0.8
            
            if compliance_score >= min_compliance:
                # Auto-approve highly compliant blocks
                await self.vote_on_proposal(
                    proposal_id=block_data.get('proposal_id'),
                    vote_type=VoteType.APPROVE,
                    justification=f"Biblical compliance score: {compliance_score:.2f}"
                )
                return True
            else:
                # Reject non-compliant blocks
                await self.vote_on_proposal(
                    proposal_id=block_data.get('proposal_id'),
                    vote_type=VoteType.REJECT,
                    justification=f"Insufficient biblical compliance: {compliance_score:.2f}"
                )
                return False
                
        except Exception as e:
            logger.error(f"Block validation error: {e}")
            return False
    
    async def _calculate_biblical_compliance(self, block_data: dict) -> float:
        """Calculate biblical compliance score for a block"""
        try:
            score = 1.0
            transactions = block_data.get('transactions', [])
            
            for tx in transactions:
                tx_data = tx.get('data', {})
                
                # Check for usury violations (major penalty)
                if tx.get('op') == 'OP_LEND' and tx_data.get('interest_rate', 0) > 0:
                    score -= 0.3
                    logger.warning(f"Usury violation detected in transaction {tx.get('transaction_id')}")
                
                # Check for Sabbath violations
                if self._is_sabbath_violation(tx):
                    score -= 0.2
                    logger.warning(f"Sabbath violation detected in transaction {tx.get('transaction_id')}")
                
                # Check for gleaning pool compliance
                if tx.get('op') == 'OP_HARVEST' and not tx_data.get('gleaning_reserved'):
                    score -= 0.1
                    logger.warning(f"Gleaning pool violation in transaction {tx.get('transaction_id')}")
                
                # Bonus for covenant-compliant transactions
                if tx.get('op') in ['OP_GLEANING_DISTRIBUTE', 'OP_YOVEL_REDISTRIBUTE', 'OP_COVENANT_BLESSING']:
                    score += 0.1
                
                # Check for widow/orphan/stranger protection
                if tx.get('op') == 'OP_PROTECTION' and tx_data.get('protected_class') in ['widow', 'orphan', 'stranger']:
                    score += 0.05
            
            return max(0.0, min(1.0, score))
            
        except Exception as e:
            logger.error(f"Biblical compliance calculation error: {e}")
            return 0.0
    
    def _is_sabbath_violation(self, transaction: dict) -> bool:
        """Check if transaction violates Sabbath principles"""
        if not self.sabbath_enforcement:
            return False
        
        tx_time = transaction.get('timestamp', 0)
        day_of_week = time.gmtime(tx_time).tm_wday
        
        # Saturday (5) is Sabbath - restrict certain operations
        if day_of_week == 5:
            forbidden_ops = ['OP_MINE', 'OP_TRADE', 'OP_BUSINESS', 'OP_WORK']
            if transaction.get('op') in forbidden_ops:
                return True
        
        return False
    
    async def _governance_monitoring_loop(self):
        """Monitor governance proposals and participate in voting"""
        while self.running:
            try:
                # Check for new proposals
                await self._check_new_proposals()
                
                # Check for voting deadlines
                await self._check_voting_deadlines()
                
                # Update governance statistics
                await self._update_governance_stats()
                
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                logger.error(f"Governance monitoring error: {e}")
                await asyncio.sleep(60)
    
    async def _biblical_compliance_loop(self):
        """Monitor network for biblical compliance violations"""
        while self.running:
            try:
                # Check recent transactions for compliance
                await self._monitor_transaction_compliance()
                
                # Check for Yovel cycle compliance
                await self._monitor_yovel_compliance()
                
                # Check for gleaning pool distributions
                await self._monitor_gleaning_compliance()
                
                await asyncio.sleep(300)  # Check every 5 minutes
                
            except Exception as e:
                logger.error(f"Biblical compliance monitoring error: {e}")
                await asyncio.sleep(300)
    
    async def _check_new_proposals(self):
        """Check for new governance proposals requiring votes"""
        try:
            # Get active proposals from consensus
            active_proposals = self.consensus.active_proposals
            voice_scroll_proposals = self.consensus.voice_scroll_proposals
            
            # Check block proposals
            for proposal_id, proposal in active_proposals.items():
                if proposal_id not in self.active_votes and proposal.proposer_id != self.node_id:
                    # New proposal - evaluate and vote
                    await self.validate_block_proposal({
                        'proposal_id': proposal_id,
                        'transactions': proposal.transactions,
                        'biblical_compliance_score': proposal.biblical_compliance_score
                    })
            
            # Check voice scroll proposals
            for scroll_id, proposal in voice_scroll_proposals.items():
                if scroll_id not in self.active_votes and proposal.proposer_id != self.node_id:
                    # New voice scroll - evaluate based on biblical principles
                    await self._evaluate_voice_scroll(scroll_id, proposal)
            
        except Exception as e:
            logger.error(f"New proposal check error: {e}")
    
    async def _evaluate_voice_scroll(self, scroll_id: str, proposal):
        """Evaluate a voice scroll proposal for biblical alignment"""
        try:
            # Analyze content for biblical alignment
            content = proposal.content
            biblical_refs = proposal.biblical_references
            
            # Simple evaluation criteria
            approval_score = 0.5  # Start neutral
            
            # Check for biblical references
            if len(biblical_refs) >= 3:
                approval_score += 0.2
            
            # Check for covenant principles in content
            covenant_keywords = ['covenant', 'biblical', 'torah', 'justice', 'righteousness', 'mercy']
            content_text = str(content).lower()
            
            for keyword in covenant_keywords:
                if keyword in content_text:
                    approval_score += 0.05
            
            # Check for anti-biblical content
            forbidden_keywords = ['usury', 'interest', 'exploitation', 'oppression']
            for keyword in forbidden_keywords:
                if keyword in content_text:
                    approval_score -= 0.2
            
            # Make voting decision
            if approval_score >= 0.7:
                vote_type = VoteType.APPROVE
                justification = f"Strong biblical alignment (score: {approval_score:.2f})"
            elif approval_score >= 0.4:
                vote_type = VoteType.ABSTAIN
                justification = f"Neutral biblical alignment (score: {approval_score:.2f})"
            else:
                vote_type = VoteType.REJECT
                justification = f"Poor biblical alignment (score: {approval_score:.2f})"
            
            await self.vote_on_proposal(scroll_id, vote_type, justification)
            
        except Exception as e:
            logger.error(f"Voice scroll evaluation error: {e}")
    
    async def _check_voting_deadlines(self):
        """Check for approaching voting deadlines"""
        try:
            current_time = int(time.time())
            
            # Check voice scroll deadlines
            for scroll_id, proposal in self.consensus.voice_scroll_proposals.items():
                if scroll_id not in self.active_votes:
                    time_remaining = proposal.voting_deadline - current_time
                    
                    # Vote if deadline is approaching (within 1 hour)
                    if 0 < time_remaining < 3600:
                        logger.warning(f"Voting deadline approaching for {scroll_id}, casting default vote")
                        await self.vote_on_proposal(
                            scroll_id, 
                            VoteType.ABSTAIN, 
                            "Default vote due to approaching deadline"
                        )
            
        except Exception as e:
            logger.error(f"Voting deadline check error: {e}")
    
    async def _monitor_transaction_compliance(self):
        """Monitor recent transactions for biblical compliance"""
        try:
            # This would integrate with the blockchain to check recent transactions
            # For now, we'll simulate monitoring
            logger.debug(f"Elder {self.tribal_code} monitoring transaction compliance")
            
        except Exception as e:
            logger.error(f"Transaction compliance monitoring error: {e}")
    
    async def _monitor_yovel_compliance(self):
        """Monitor Yovel (Jubilee) cycle compliance"""
        try:
            # Check if we're in a Yovel year and ensure proper wealth redistribution
            logger.debug(f"Elder {self.tribal_code} monitoring Yovel compliance")
            
        except Exception as e:
            logger.error(f"Yovel compliance monitoring error: {e}")
    
    async def _monitor_gleaning_compliance(self):
        """Monitor gleaning pool compliance"""
        try:
            # Check that gleaning pools are being properly maintained and distributed
            logger.debug(f"Elder {self.tribal_code} monitoring gleaning compliance")
            
        except Exception as e:
            logger.error(f"Gleaning compliance monitoring error: {e}")
    
    async def _update_governance_stats(self):
        """Update governance participation statistics"""
        try:
            stats = {
                "total_votes_cast": len(self.governance_history),
                "active_proposals": len(self.active_votes),
                "tribal_code": self.tribal_code,
                "voting_weight": self.voting_weight,
                "last_activity": int(time.time())
            }
            
            # Store stats for reporting
            setattr(self, '_governance_stats', stats)
            
        except Exception as e:
            logger.error(f"Governance stats update error: {e}")
    
    # Public API Methods
    def get_node_status(self) -> dict:
        """Get comprehensive node status"""
        return {
            "node_id": self.node_id,
            "tribal_code": self.tribal_code,
            "elder_name": self.elder_name,
            "voting_weight": self.voting_weight,
            "running": self.running,
            "connected_peers": len(self.peer_manager.get_connected_peers()),
            "tribal_elders_connected": len(self.peer_manager.get_tribal_elders()),
            "active_votes": len(self.active_votes),
            "governance_history": len(self.governance_history),
            "sync_status": self.blockchain_sync.get_sync_status(),
            "consensus_stats": self.consensus.get_consensus_stats(),
            "network_stats": self.peer_manager.get_network_stats()
        }
    
    def get_governance_history(self) -> List[dict]:
        """Get governance participation history"""
        return self.governance_history.copy()
    
    def get_biblical_compliance_settings(self) -> dict:
        """Get biblical compliance enforcement settings"""
        return {
            "sabbath_enforcement": self.sabbath_enforcement,
            "anti_usury_enforcement": self.anti_usury_enforcement,
            "gleaning_pool_participation": self.gleaning_pool_participation
        }
    
    def update_compliance_settings(self, settings: dict):
        """Update biblical compliance enforcement settings"""
        if "sabbath_enforcement" in settings:
            self.sabbath_enforcement = settings["sabbath_enforcement"]
        if "anti_usury_enforcement" in settings:
            self.anti_usury_enforcement = settings["anti_usury_enforcement"]
        if "gleaning_pool_participation" in settings:
            self.gleaning_pool_participation = settings["gleaning_pool_participation"]
        
        logger.info(f"Elder {self.tribal_code} updated compliance settings: {settings}")

# Utility Functions
def create_tribal_elder_configs() -> List[TribalElderConfig]:
    """Create configurations for all 12 tribal elders"""
    tribal_data = [
        ("JU", "Elder Yehudah ben David", 2, 8767),    # Judah - Royal
        ("LE", "Elder Levi ben Aaron", 2, 8768),       # Levi - Priestly  
        ("EP", "Elder Ephraim ben Joseph", 2, 8769),   # Ephraim - Fruitful
        ("BE", "Elder Benjamin ben Jacob", 1, 8770),   # Benjamin
        ("SI", "Elder Simeon ben Jacob", 1, 8771),     # Simeon
        ("MA", "Elder Manasseh ben Joseph", 1, 8772),  # Manasseh
        ("IS", "Elder Issachar ben Jacob", 1, 8773),   # Issachar
        ("ZE", "Elder Zebulun ben Jacob", 1, 8774),    # Zebulun
        ("NA", "Elder Naphtali ben Jacob", 1, 8775),   # Naphtali
        ("GA", "Elder Gad ben Jacob", 1, 8776),        # Gad
        ("AS", "Elder Asher ben Jacob", 1, 8777),      # Asher
        ("RE", "Elder Reuben ben Jacob", 1, 8778),     # Reuben
    ]
    
    bootstrap_nodes = [("127.0.0.1", 8766)]  # Bootstrap node address
    configs = []
    
    for tribal_code, elder_name, voting_weight, port in tribal_data:
        config = TribalElderConfig(
            tribal_code=tribal_code,
            elder_name=elder_name,
            voting_weight=voting_weight,
            port=port,
            bootstrap_nodes=bootstrap_nodes,
            identity_id=f"elder_{tribal_code.lower()}_{uuid.uuid4().hex[:8]}"
        )
        configs.append(config)
    
    return configs
