{% extends "base.html" %}

{% block title %}Blockchain Inscription - Eden Mode Complete{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-onyx-black via-onyx-dark to-onyx-black relative overflow-hidden">
    <!-- Ambient Background Effects -->
    <div class="absolute inset-0 opacity-20">
        <div class="blockchain-matrix"></div>
        <div class="covenant-completion"></div>
    </div>

    <!-- Progress Indicator - Non-sticky -->
    <div class="flex justify-center mb-8">
        <div class="glass-card-enhanced px-8 py-4 rounded-2xl">
            <div class="flex items-center space-x-4">
                <div class="w-8 h-8 rounded-full bg-cyber-green flex items-center justify-center text-onyx-black font-bold">✓</div>
                <div class="w-8 h-1 bg-cyber-green"></div>
                <div class="w-8 h-8 rounded-full bg-cyber-green flex items-center justify-center text-onyx-black font-bold">✓</div>
                <div class="w-8 h-1 bg-cyber-green"></div>
                <div class="w-8 h-8 rounded-full bg-cyber-green flex items-center justify-center text-onyx-black font-bold">✓</div>
                <div class="w-8 h-1 bg-cyber-green"></div>
                <div class="w-8 h-8 rounded-full bg-cyber-green flex items-center justify-center text-onyx-black font-bold">✓</div>
                <div class="w-8 h-1 bg-cyber-cyan"></div>
                <div class="w-8 h-8 rounded-full bg-cyber-cyan flex items-center justify-center text-onyx-black font-bold">5</div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container-xl mx-auto px-6 pt-32 pb-16">
        <div class="max-w-5xl mx-auto">

            <!-- Header -->
            <div class="text-center mb-16 fade-in-up">
                <div class="mb-8">
                    <div class="inline-block glass-card-premium p-6 rounded-3xl mb-6">
                        <svg class="w-16 h-16 text-cyber-green mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                                  d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                    </div>
                    <h1 class="text-6xl md:text-7xl font-orbitron font-bold text-cyber-green mb-6 glow-text">
                        Blockchain Inscription
                    </h1>
                    <p class="text-2xl text-text-secondary max-w-4xl mx-auto leading-relaxed">
                        Your covenant identity is complete. Now we inscribe your awakening
                        <span class="text-cyber-cyan font-semibold">permanently into the blockchain</span>
                        for all generations to witness.
                    </p>
                </div>
            </div>

            <!-- Covenant Summary -->
            <div class="glass-card-premium p-8 rounded-3xl mb-12 fade-in-up" data-delay="300">
                <div class="text-center mb-8">
                    <h2 class="text-3xl font-orbitron font-bold text-cyber-cyan mb-6">
                        📜 Your Covenant Identity
                    </h2>
                    <p class="text-lg text-text-secondary max-w-3xl mx-auto">
                        "And I will establish my covenant between me and thee and thy seed after thee
                        in their generations for an everlasting covenant"
                    </p>
                    <p class="text-cyber-purple font-orbitron mt-4">— Genesis 17:7</p>
                </div>

                <div id="covenantSummary" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <!-- Dynamic content will be inserted here -->
                </div>
            </div>

            <!-- Blockchain Inscription Process -->
            <div class="space-y-12">
                <!-- Transaction Preparation -->
                <div class="glass-card-premium p-8 rounded-3xl fade-in-up" data-delay="600">
                    <h2 class="text-3xl font-orbitron font-bold text-cyber-cyan mb-8 text-center">
                        ⛓️ Preparing Blockchain Transaction
                    </h2>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <div class="space-y-6">
                            <div class="glass-card-enhanced p-6 rounded-xl">
                                <h3 class="text-xl font-orbitron font-bold text-cyber-purple mb-4">
                                    🔐 Cryptographic Identity
                                </h3>
                                <div class="space-y-4 text-sm">
                                    <div class="flex justify-between items-center">
                                        <span class="text-text-secondary">Public Key:</span>
                                        <span class="text-cyber-cyan font-mono" id="publicKey">Generating...</span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-text-secondary">Address:</span>
                                        <span class="text-cyber-green font-mono" id="walletAddress">Generating...</span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-text-secondary">CIPP Tier:</span>
                                        <span class="text-cyber-purple font-bold">Tier 1 (Verified)</span>
                                    </div>
                                </div>
                            </div>

                            <div class="glass-card-enhanced p-6 rounded-xl">
                                <h3 class="text-xl font-orbitron font-bold text-cyber-purple mb-4">
                                    📊 Transaction Details
                                </h3>
                                <div class="space-y-4 text-sm">
                                    <div class="flex justify-between items-center">
                                        <span class="text-text-secondary">Transaction Type:</span>
                                        <span class="text-cyber-cyan">COVENANT_IDENTITY_CREATE</span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-text-secondary">Gas Fee:</span>
                                        <span class="text-cyber-green">0.001 ONX</span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-text-secondary">Block Confirmation:</span>
                                        <span class="text-cyber-purple" id="blockStatus">Pending...</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="space-y-6">
                            <div class="glass-card-enhanced p-6 rounded-xl">
                                <h3 class="text-xl font-orbitron font-bold text-cyber-purple mb-4">
                                    🌟 Biblical Tokenomics Activation
                                </h3>
                                <div class="space-y-3 text-text-secondary">
                                    <p class="flex items-center space-x-3">
                                        <span class="text-cyber-green">✓</span>
                                        <span>Yovel cycle participation enabled</span>
                                    </p>
                                    <p class="flex items-center space-x-3">
                                        <span class="text-cyber-green">✓</span>
                                        <span>Sabbath enforcement activated</span>
                                    </p>
                                    <p class="flex items-center space-x-3">
                                        <span class="text-cyber-green">✓</span>
                                        <span>Gleaning pool access granted</span>
                                    </p>
                                    <p class="flex items-center space-x-3">
                                        <span class="text-cyber-green">✓</span>
                                        <span>Anti-usury lending available</span>
                                    </p>
                                    <p class="flex items-center space-x-3">
                                        <span class="text-cyber-green">✓</span>
                                        <span>Tiered mining rewards active</span>
                                    </p>
                                </div>
                            </div>

                            <div class="glass-card-enhanced p-6 rounded-xl">
                                <h3 class="text-xl font-orbitron font-bold text-cyber-purple mb-4">
                                    🎯 Next Steps
                                </h3>
                                <div class="space-y-3 text-text-secondary text-sm">
                                    <p>• Access your covenant dashboard</p>
                                    <p>• Begin mining operations</p>
                                    <p>• Connect with your Sela community</p>
                                    <p>• Participate in governance</p>
                                    <p>• Contribute to biblical economy</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Inscription Status -->
                <div class="glass-card-premium p-8 rounded-3xl fade-in-up" data-delay="900">
                    <div class="text-center">
                        <h2 class="text-3xl font-orbitron font-bold text-cyber-cyan mb-8">
                            🔄 Inscription Status
                        </h2>

                        <div id="inscriptionProgress" class="space-y-8">
                            <!-- Progress steps will be dynamically updated -->
                            <div class="inscription-step" data-step="1">
                                <div class="flex items-center justify-center space-x-4 mb-4">
                                    <div class="w-12 h-12 rounded-full bg-cyber-cyan flex items-center justify-center">
                                        <div class="loading-spinner w-6 h-6 border-2 border-onyx-black border-t-transparent rounded-full animate-spin"></div>
                                    </div>
                                    <div class="text-left">
                                        <h3 class="text-xl font-orbitron font-bold text-cyber-cyan">Generating Keys</h3>
                                        <p class="text-text-secondary">Creating cryptographic identity...</p>
                                    </div>
                                </div>
                            </div>

                            <div class="inscription-step opacity-50" data-step="2">
                                <div class="flex items-center justify-center space-x-4 mb-4">
                                    <div class="w-12 h-12 rounded-full bg-glass-border flex items-center justify-center">
                                        <span class="text-2xl">2</span>
                                    </div>
                                    <div class="text-left">
                                        <h3 class="text-xl font-orbitron font-bold text-text-tertiary">Creating Transaction</h3>
                                        <p class="text-text-secondary">Preparing covenant data for blockchain...</p>
                                    </div>
                                </div>
                            </div>

                            <div class="inscription-step opacity-50" data-step="3">
                                <div class="flex items-center justify-center space-x-4 mb-4">
                                    <div class="w-12 h-12 rounded-full bg-glass-border flex items-center justify-center">
                                        <span class="text-2xl">3</span>
                                    </div>
                                    <div class="text-left">
                                        <h3 class="text-xl font-orbitron font-bold text-text-tertiary">Mining Block</h3>
                                        <p class="text-text-secondary">Validators confirming your covenant...</p>
                                    </div>
                                </div>
                            </div>

                            <div class="inscription-step opacity-50" data-step="4">
                                <div class="flex items-center justify-center space-x-4 mb-4">
                                    <div class="w-12 h-12 rounded-full bg-glass-border flex items-center justify-center">
                                        <span class="text-2xl">4</span>
                                    </div>
                                    <div class="text-left">
                                        <h3 class="text-xl font-orbitron font-bold text-text-tertiary">Covenant Complete</h3>
                                        <p class="text-text-secondary">Welcome to the ONNYX covenant community!</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Completion Ceremony -->
                <div id="completionCeremony" class="glass-card-premium p-8 rounded-3xl hidden fade-in-up">
                    <div class="text-center">
                        <div class="mb-8">
                            <div class="inline-block glass-card-premium p-6 rounded-3xl mb-6">
                                <div class="text-6xl">🎉</div>
                            </div>
                            <h2 class="text-4xl font-orbitron font-bold text-cyber-green mb-6">
                                Covenant Awakening Complete!
                            </h2>
                            <p class="text-xl text-text-secondary max-w-3xl mx-auto leading-relaxed">
                                Your identity has been permanently inscribed into the ONNYX blockchain.
                                You are now a verified member of the covenant community.
                            </p>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                            <div class="glass-card-enhanced p-6 rounded-xl">
                                <div class="text-3xl mb-3">🏛️</div>
                                <h3 class="text-lg font-orbitron font-bold text-cyber-cyan mb-2">Heritage Confirmed</h3>
                                <p class="text-text-secondary text-sm" id="confirmedHeritage">Loading...</p>
                            </div>
                            <div class="glass-card-enhanced p-6 rounded-xl">
                                <div class="text-3xl mb-3">⛓️</div>
                                <h3 class="text-lg font-orbitron font-bold text-cyber-cyan mb-2">Blockchain Address</h3>
                                <p class="text-text-secondary text-sm font-mono" id="confirmedAddress">Loading...</p>
                            </div>
                            <div class="glass-card-enhanced p-6 rounded-xl">
                                <div class="text-3xl mb-3">🏢</div>
                                <h3 class="text-lg font-orbitron font-bold text-cyber-cyan mb-2">Sela Status</h3>
                                <p class="text-text-secondary text-sm" id="confirmedSela">Loading...</p>
                            </div>
                        </div>

                        <div class="space-y-4" id="dashboardButtonContainer">
                            <!-- Dashboard button will be dynamically inserted here -->
                            <p class="text-text-tertiary text-sm">
                                Your journey into biblical economics and covenant community begins now.
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Navigation (Hidden during inscription) -->
            <div id="navigationControls" class="flex justify-between items-center mt-16 fade-in-up" data-delay="1200">
                <button id="backButton"
                        class="glass-button px-8 py-4 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                    ← Back to Eden Mode
                </button>

                <button id="inscribeButton"
                        class="glass-button-primary px-8 py-4 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105 glow-on-hover">
                    🔥 Inscribe My Covenant
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Eden Mode Step 5 Controller
class EdenModeStep5 {
    constructor() {
        this.inscriptionInProgress = false;
        this.currentStep = 1;
        this.maxSteps = 4;
        this.sessionData = {};
        this.generatedKeys = null;

        this.init();
    }

    init() {
        this.loadSessionData();
        this.initializeAnimations();
        this.displayCovenantSummary();
        this.setupNavigation();
        this.generateCryptographicKeys();
    }

    loadSessionData() {
        const keys = [
            'edenMode_selectedNation',
            'edenMode_selectedTribe',
            'edenMode_fullName',
            'edenMode_email',
            'edenMode_laborCategory',
            'edenMode_selaChoice',
            'edenMode_businessName',
            'edenMode_businessType',
            'edenMode_selectedSela'
        ];

        keys.forEach(key => {
            const value = sessionStorage.getItem(key);
            if (value) {
                this.sessionData[key.replace('edenMode_', '')] = value;
            }
        });

        console.log('📊 Final Session Data:', this.sessionData);
    }

    displayCovenantSummary() {
        const covenantSummary = document.getElementById('covenantSummary');

        const nationSymbols = {
            'JUDAH': '👑', 'BENJAMIN': '⚔️', 'LEVI': '📿',
            'EPHRAIM': '🌾', 'MANASSEH': '💼', 'REUBEN': '🌊',
            'SIMEON': '🎵', 'ZEBULUN': '🚢', 'ISSACHAR': '📊',
            'DAN': '⚖️', 'GAD': '🛡️', 'ASHER': '🍯', 'NAPHTALI': '🦌'
        };

        const laborIcons = {
            'spiritual': '📿', 'physical': '🔨', 'intellectual': '🧠',
            'service': '🤝', 'creative': '🎨', 'governance': '⚖️'
        };

        covenantSummary.innerHTML = `
            <div class="glass-card-enhanced p-6 rounded-xl text-center">
                <div class="text-4xl mb-3">${nationSymbols[this.sessionData.selectedNation] || '🏛️'}</div>
                <h3 class="text-lg font-orbitron font-bold text-cyber-cyan mb-2">Heritage</h3>
                <p class="text-text-secondary text-sm">${this.sessionData.selectedNation}</p>
                ${this.sessionData.selectedTribe ? `<p class="text-cyber-purple text-xs">Tribe of ${this.sessionData.selectedTribe}</p>` : ''}
            </div>
            <div class="glass-card-enhanced p-6 rounded-xl text-center">
                <div class="text-4xl mb-3">👤</div>
                <h3 class="text-lg font-orbitron font-bold text-cyber-cyan mb-2">Identity</h3>
                <p class="text-text-secondary text-sm">${this.sessionData.fullName}</p>
                <p class="text-cyber-purple text-xs">${this.sessionData.email}</p>
            </div>
            <div class="glass-card-enhanced p-6 rounded-xl text-center">
                <div class="text-4xl mb-3">${laborIcons[this.sessionData.laborCategory] || '🛠️'}</div>
                <h3 class="text-lg font-orbitron font-bold text-cyber-cyan mb-2">Calling</h3>
                <p class="text-text-secondary text-sm">${this.sessionData.laborCategory ? this.sessionData.laborCategory.charAt(0).toUpperCase() + this.sessionData.laborCategory.slice(1) : 'Labor'} Ministry</p>
            </div>
            <div class="glass-card-enhanced p-6 rounded-xl text-center">
                <div class="text-4xl mb-3">${this.sessionData.selaChoice === 'create' ? '🏗️' : this.sessionData.selaChoice === 'join' ? '🤝' : '🌟'}</div>
                <h3 class="text-lg font-orbitron font-bold text-cyber-cyan mb-2">Covenant Path</h3>
                <p class="text-text-secondary text-sm">
                    ${this.sessionData.selaChoice === 'create' ?
                        (this.sessionData.businessName || 'New Business') :
                        this.sessionData.selaChoice === 'join' ?
                        'Joining Sela Business' :
                        'Community Member'}
                </p>
                ${this.sessionData.businessType ? `<p class="text-cyber-purple text-xs">${this.sessionData.businessType}</p>` : ''}
            </div>
        `;
    }

    initializeAnimations() {
        const elements = document.querySelectorAll('.fade-in-up');

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const delay = entry.target.dataset.delay || 0;
                    setTimeout(() => {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }, delay);
                }
            });
        }, { threshold: 0.1 });

        elements.forEach(element => {
            element.style.opacity = '0';
            element.style.transform = 'translateY(40px)';
            element.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
            observer.observe(element);
        });
    }

    generateCryptographicKeys() {
        // Simulate key generation
        setTimeout(() => {
            const publicKey = this.generateRandomKey(32);
            const walletAddress = this.generateWalletAddress();

            document.getElementById('publicKey').textContent = publicKey.substring(0, 16) + '...';
            document.getElementById('walletAddress').textContent = walletAddress;

            this.generatedKeys = {
                publicKey: publicKey,
                walletAddress: walletAddress
            };

            this.completeStep(1);
        }, 2000);
    }

    generateRandomKey(length) {
        const chars = '0123456789abcdef';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }

    generateWalletAddress() {
        return 'ONX' + this.generateRandomKey(34);
    }

    setupNavigation() {
        const backButton = document.getElementById('backButton');
        const inscribeButton = document.getElementById('inscribeButton');
        const enterDashboard = document.getElementById('enterDashboard');

        backButton.addEventListener('click', () => {
            if (!this.inscriptionInProgress) {
                window.location.href = '/auth/eden-mode/step4';
            }
        });

        inscribeButton.addEventListener('click', () => {
            this.startInscription();
        });

        if (enterDashboard) {
            enterDashboard.addEventListener('click', () => {
                window.location.href = '/dashboard';
            });
        }
    }

    async startInscription() {
        if (this.inscriptionInProgress) return;

        this.inscriptionInProgress = true;

        // Hide navigation
        document.getElementById('navigationControls').style.display = 'none';

        // Start inscription process
        await this.processInscription();
    }

    async processInscription() {
        try {
            // Step 2: Create Transaction
            await this.simulateStep(2, 'Creating Transaction', 'Preparing covenant data for blockchain...', 3000);

            // Step 3: Mine Block
            await this.simulateStep(3, 'Mining Block', 'Validators confirming your covenant...', 4000);

            // Step 4: Complete
            await this.simulateStep(4, 'Covenant Complete', 'Welcome to the ONNYX covenant community!', 2000);

            // Submit to backend
            await this.submitIdentityCreation();

            // Show completion ceremony
            this.showCompletionCeremony();

        } catch (error) {
            console.error('Inscription failed:', error);
            this.handleInscriptionError(error);
        }
    }

    async simulateStep(stepNumber, title, description, duration) {
        return new Promise((resolve) => {
            const step = document.querySelector(`[data-step="${stepNumber}"]`);

            // Activate current step
            step.classList.remove('opacity-50');
            const circle = step.querySelector('.w-12');
            const titleEl = step.querySelector('h3');
            const descEl = step.querySelector('p');

            circle.className = 'w-12 h-12 rounded-full bg-cyber-cyan flex items-center justify-center';
            circle.innerHTML = '<div class="loading-spinner w-6 h-6 border-2 border-onyx-black border-t-transparent rounded-full animate-spin"></div>';
            titleEl.className = 'text-xl font-orbitron font-bold text-cyber-cyan';
            titleEl.textContent = title;
            descEl.textContent = description;

            setTimeout(() => {
                // Complete step
                circle.className = 'w-12 h-12 rounded-full bg-cyber-green flex items-center justify-center';
                circle.innerHTML = '<svg class="w-6 h-6 text-onyx-black" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>';
                titleEl.className = 'text-xl font-orbitron font-bold text-cyber-green';

                resolve();
            }, duration);
        });
    }

    async submitIdentityCreation() {
        try {
            const identityData = {
                ...this.sessionData,
                publicKey: this.generatedKeys.publicKey,
                walletAddress: this.generatedKeys.walletAddress,
                cippTier: 1,
                covenantAccepted: true
            };

            const response = await fetch('/auth/eden-mode/create-identity', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(identityData)
            });

            const result = await response.json();

            if (result.success) {
                console.log('✅ Identity created successfully:', result);

                // Update block status
                document.getElementById('blockStatus').textContent = `Block #${result.blockNumber || 'Confirmed'}`;

                // Dispatch success event
                document.dispatchEvent(new CustomEvent('identityCreated', {
                    detail: result
                }));

                return result;
            } else {
                throw new Error(result.error || 'Identity creation failed');
            }
        } catch (error) {
            console.error('❌ Identity creation error:', error);
            throw error;
        }
    }

    showCompletionCeremony() {
        const ceremony = document.getElementById('completionCeremony');
        ceremony.classList.remove('hidden');

        // Update confirmation details
        document.getElementById('confirmedHeritage').textContent =
            `${this.sessionData.selectedNation}${this.sessionData.selectedTribe ? ` (${this.sessionData.selectedTribe})` : ''}`;
        document.getElementById('confirmedAddress').textContent = this.generatedKeys.walletAddress;
        document.getElementById('confirmedSela').textContent =
            this.sessionData.selaChoice === 'create' ?
            `Creator: ${this.sessionData.businessName || 'New Business'}` :
            this.sessionData.selaChoice === 'join' ?
            'Member: Joining Sela Business' :
            'Independent Community Member';

        // Scroll to ceremony
        setTimeout(() => {
            ceremony.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }, 500);

        // Add dashboard redirect button after ceremony
        setTimeout(() => {
            const dashboardContainer = document.getElementById('dashboardButtonContainer');
            if (dashboardContainer) {
                const enterDashboard = document.createElement('button');
                enterDashboard.id = 'enterDashboard';
                enterDashboard.className = 'glass-button-primary px-12 py-4 rounded-2xl font-orbitron font-bold text-xl transition-all duration-500 hover:scale-105 glow-on-hover';
                enterDashboard.innerHTML = '🚪 Enter Your Covenant Dashboard';
                enterDashboard.onclick = () => {
                    window.location.href = '/dashboard/';
                };

                // Insert before the description text
                dashboardContainer.insertBefore(enterDashboard, dashboardContainer.firstChild);
            }
        }, 2000);

        // Clear session data
        this.clearSessionData();
    }

    handleInscriptionError(error) {
        alert(`Inscription failed: ${error.message}. Please try again.`);

        // Reset UI
        this.inscriptionInProgress = false;
        document.getElementById('navigationControls').style.display = 'flex';

        // Reset steps
        document.querySelectorAll('.inscription-step').forEach((step, index) => {
            if (index > 0) {
                step.classList.add('opacity-50');
                const circle = step.querySelector('.w-12');
                circle.className = 'w-12 h-12 rounded-full bg-glass-border flex items-center justify-center';
                circle.innerHTML = `<span class="text-2xl">${index + 1}</span>`;
            }
        });
    }

    completeStep(stepNumber) {
        const step = document.querySelector(`[data-step="${stepNumber}"]`);
        const circle = step.querySelector('.w-12');
        const title = step.querySelector('h3');

        circle.className = 'w-12 h-12 rounded-full bg-cyber-green flex items-center justify-center';
        circle.innerHTML = '<svg class="w-6 h-6 text-onyx-black" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>';
        title.className = 'text-xl font-orbitron font-bold text-cyber-green';
    }

    clearSessionData() {
        // Clear Eden Mode session data
        const keys = [
            'edenMode_selectedNation',
            'edenMode_selectedTribe',
            'edenMode_covenantAccepted',
            'edenMode_fullName',
            'edenMode_email',
            'edenMode_laborCategory',
            'edenMode_selaChoice',
            'edenMode_businessName',
            'edenMode_businessType',
            'edenMode_selectedSela'
        ];

        // Add community member key to clear
        keys.push('edenMode_communityMember');

        keys.forEach(key => {
            sessionStorage.removeItem(key);
        });

        console.log('🧹 Session data cleared');
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    new EdenModeStep5();
});
</script>

<style>
.glow-text {
    text-shadow: 0 0 20px rgba(0, 255, 136, 0.5);
}

.glow-on-hover:hover {
    box-shadow: 0 0 30px rgba(0, 255, 247, 0.4);
}

.blockchain-matrix::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(0deg, transparent 24%, rgba(0, 255, 247, 0.05) 25%, rgba(0, 255, 247, 0.05) 26%, transparent 27%, transparent 74%, rgba(0, 255, 247, 0.05) 75%, rgba(0, 255, 247, 0.05) 76%, transparent 77%, transparent),
        linear-gradient(90deg, transparent 24%, rgba(0, 255, 136, 0.05) 25%, rgba(0, 255, 136, 0.05) 26%, transparent 27%, transparent 74%, rgba(0, 255, 136, 0.05) 75%, rgba(0, 255, 136, 0.05) 76%, transparent 77%, transparent);
    background-size: 50px 50px;
    animation: matrixMove 20s linear infinite;
}

@keyframes matrixMove {
    0% { transform: translateX(0) translateY(0); }
    100% { transform: translateX(50px) translateY(50px); }
}

.covenant-completion::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 50% 50%, rgba(0, 255, 136, 0.1) 0%, transparent 70%);
    animation: completionPulse 6s ease-in-out infinite;
}

@keyframes completionPulse {
    0%, 100% { opacity: 0.3; transform: scale(1); }
    50% { opacity: 0.8; transform: scale(1.2); }
}

.loading-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.inscription-step {
    transition: opacity 0.5s ease;
}
</style>
{% endblock %}
