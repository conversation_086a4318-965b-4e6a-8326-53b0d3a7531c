/**
 * ONNYX Platform Enhanced Scroll Effects
 * Comprehensive scroll experience with cyber-themed animations
 */

class ONNYXScrollEffects {
    constructor() {
        this.scrollProgress = null;
        this.intersectionObserver = null;
        this.blurObserver = null;
        this.isReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

        this.init();
    }

    init() {
        this.createScrollProgressBar();
        this.initIntersectionObserver();
        this.initBlurEffects();
        this.initSmoothScrolling();
        this.initNavigationEffects();
        this.initParticleEnhancements();
        this.bindEvents();
    }

    // 1. SCROLL PROGRESS BAR
    createScrollProgressBar() {
        if (this.isReducedMotion) return;

        this.scrollProgress = document.createElement('div');
        this.scrollProgress.className = 'scroll-progress';
        document.body.appendChild(this.scrollProgress);
    }

    updateScrollProgress() {
        if (!this.scrollProgress || this.isReducedMotion) return;

        requestAnimationFrame(() => {
            const scrollTop = window.pageYOffset;
            const docHeight = document.documentElement.scrollHeight - window.innerHeight;
            const scrollPercent = (scrollTop / docHeight) * 100;

            this.scrollProgress.style.width = `${Math.min(scrollPercent, 100)}%`;
        });
    }

    // 2. INTERSECTION OBSERVER FOR FADE-IN ANIMATIONS
    initIntersectionObserver() {
        if (this.isReducedMotion) return;

        const options = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        this.intersectionObserver = new IntersectionObserver((entries) => {
            entries.forEach((entry, index) => {
                if (entry.isIntersecting) {
                    // Add staggered delay for grid items
                    const delay = entry.target.dataset.animationOrder
                        ? parseInt(entry.target.dataset.animationOrder) * 100
                        : index * 100;

                    setTimeout(() => {
                        entry.target.classList.add('visible');
                        entry.target.classList.add('in-view');
                    }, delay);
                }
            });
        }, options);

        // Observe all elements with scroll-animate class
        this.observeScrollAnimateElements();
    }

    observeScrollAnimateElements() {
        // Skip roadmap elements to prevent conflicts
        const elements = document.querySelectorAll('.glass-card:not([class*="roadmap"]), .scroll-animate:not([class*="roadmap"]), .glow-on-scroll:not([class*="roadmap"])');

        elements.forEach((element, index) => {
            // Skip any roadmap-related elements
            if (element.closest('[class*="roadmap"]') || element.classList.toString().includes('roadmap')) {
                return;
            }

            // Add scroll-animate class if not present
            if (!element.classList.contains('scroll-animate')) {
                element.classList.add('scroll-animate');
            }

            // Add animation order for staggered effects
            if (element.closest('.grid')) {
                element.dataset.animationOrder = index % 3; // Stagger by 3s for grid layouts
            }

            // Add performance optimization classes
            element.classList.add('scroll-optimized');

            this.intersectionObserver.observe(element);
        });
    }

    // 3. PROGRESSIVE BLUR EFFECTS
    initBlurEffects() {
        if (this.isReducedMotion) return;

        const blurOptions = {
            threshold: [0, 0.25, 0.5, 0.75, 1],
            rootMargin: '0px'
        };

        this.blurObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                const element = entry.target;

                if (entry.intersectionRatio < 0.25) {
                    element.classList.add('blurred');
                } else {
                    element.classList.remove('blurred');
                }
            });
        }, blurOptions);

        // Observe elements that should blur when scrolling out
        const blurElements = document.querySelectorAll('.glass-card, .tech-card, .validator-card');
        blurElements.forEach(element => {
            element.classList.add('blur-on-scroll');
            this.blurObserver.observe(element);
        });
    }

    // 4. ENHANCED SMOOTH SCROLLING
    initSmoothScrolling() {
        // Add scroll-snap to main container
        const mainContent = document.querySelector('main');
        if (mainContent) {
            mainContent.classList.add('scroll-snap-container');
        }

        // Add scroll-snap to major sections
        const sections = document.querySelectorAll('.hero-gradient, .py-20, .footer-responsive');
        sections.forEach(section => {
            section.classList.add('scroll-snap-section');
        });

        // Enhanced anchor link scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = anchor.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);

                if (targetElement) {
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }

    // 5. NAVIGATION SCROLL EFFECTS
    initNavigationEffects() {
        const nav = document.querySelector('nav, .glass-nav');
        if (nav) {
            nav.classList.add('nav-scroll-enhanced');
        }
    }

    updateNavigationState() {
        const nav = document.querySelector('.nav-scroll-enhanced');
        if (!nav) return;

        const scrollTop = window.pageYOffset;

        if (scrollTop > 100) {
            nav.classList.add('scrolled');
        } else {
            nav.classList.remove('scrolled');
        }
    }

    // 6. PARTICLE ENHANCEMENTS
    initParticleEnhancements() {
        if (this.isReducedMotion) return;

        const particles = document.querySelectorAll('.animate-ping, .animate-pulse, .animate-bounce');
        particles.forEach(particle => {
            particle.classList.add('floating-particle');
        });
    }

    // 7. BUTTON ENHANCEMENTS
    enhanceButtons() {
        const buttons = document.querySelectorAll('.glass-button, .glass-button-primary');
        buttons.forEach(button => {
            button.classList.add('scroll-enhanced');
        });
    }

    // 8. EVENT BINDING
    bindEvents() {
        // Throttled scroll event
        let scrollTimeout;
        window.addEventListener('scroll', () => {
            if (scrollTimeout) {
                clearTimeout(scrollTimeout);
            }

            scrollTimeout = setTimeout(() => {
                this.updateScrollProgress();
                this.updateNavigationState();
            }, 10);
        }, { passive: true });

        // Handle reduced motion preference changes
        const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
        mediaQuery.addListener((e) => {
            this.isReducedMotion = e.matches;
            if (this.isReducedMotion) {
                this.disableAnimations();
            } else {
                this.enableAnimations();
            }
        });

        // Re-observe elements when new content is loaded
        const mutationObserver = new MutationObserver((mutations) => {
            mutations.forEach(mutation => {
                if (mutation.addedNodes.length > 0) {
                    this.observeNewElements();
                }
            });
        });

        mutationObserver.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // 9. UTILITY METHODS
    observeNewElements() {
        if (this.isReducedMotion) return;

        const newElements = document.querySelectorAll('.glass-card:not(.scroll-optimized)');
        newElements.forEach((element, index) => {
            element.classList.add('scroll-animate', 'scroll-optimized');
            if (this.intersectionObserver) {
                this.intersectionObserver.observe(element);
            }
            if (this.blurObserver) {
                element.classList.add('blur-on-scroll');
                this.blurObserver.observe(element);
            }
        });
    }

    disableAnimations() {
        document.body.classList.add('reduced-motion');
        if (this.scrollProgress) {
            this.scrollProgress.style.display = 'none';
        }
    }

    enableAnimations() {
        document.body.classList.remove('reduced-motion');
        if (this.scrollProgress) {
            this.scrollProgress.style.display = 'block';
        }
    }

    // 10. CLEANUP
    destroy() {
        if (this.intersectionObserver) {
            this.intersectionObserver.disconnect();
        }
        if (this.blurObserver) {
            this.blurObserver.disconnect();
        }
        if (this.scrollProgress) {
            this.scrollProgress.remove();
        }
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.onnyx_scroll_effects = new ONNYXScrollEffects();
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ONNYXScrollEffects;
}
