/**
 * ONNYX Navigation Testing Script
 * Comprehensive testing for responsive navigation across all breakpoints
 */

class NavigationTester {
    constructor() {
        this.breakpoints = {
            'Extra Small Mobile': 320,
            'Small Mobile': 480,
            'Mobile': 768,
            'Tablet': 992,
            'Desktop': 1200,
            'Large Desktop': 1400
        };

        this.testResults = {};
        this.init();
    }

    init() {
        console.log('🚀 ONNYX Navigation Testing Suite Initialized');
        console.log('💡 Use window.navTester.runAllTests() to run comprehensive tests');
        console.log('💡 Use window.navTester.testBreakpoint(width, name) to test specific breakpoints');
        // Remove automatic UI creation and initial tests
    }

    createTestControls() {
        // Test controls removed - use console commands instead
        console.log('📱 Navigation Tester - Console Commands Available:');
        console.log('  - navTester.runAllTests() - Run comprehensive tests');
        console.log('  - navTester.testBreakpoint(width, name) - Test specific breakpoint');
        console.log('  - navTester.testResults - View last test results');
    }

    testBreakpoint(width, name) {
        console.log(`🔍 Testing ${name} breakpoint (${width}px)`);

        // Resize viewport
        window.resizeTo(width + 100, 800);

        setTimeout(() => {
            const results = this.analyzeNavigation(width, name);
            this.displayResults(name, results);
            this.testResults[name] = results;
        }, 500);
    }

    analyzeNavigation(width, breakpointName) {
        const results = {
            breakpoint: breakpointName,
            width: width,
            tests: {},
            issues: [],
            passed: 0,
            total: 0
        };

        // Test 1: Navigation visibility
        const navbar = document.querySelector('.navbar-nav');
        const mobileToggle = document.querySelector('.mobile-menu-toggle');

        if (width <= 768) {
            results.tests.mobileMenuVisible = mobileToggle && window.getComputedStyle(mobileToggle).display !== 'none';
            results.tests.desktopNavHidden = navbar && window.getComputedStyle(navbar).display === 'none';
        } else {
            results.tests.desktopNavVisible = navbar && window.getComputedStyle(navbar).display !== 'none';
            results.tests.mobileMenuHidden = !mobileToggle || window.getComputedStyle(mobileToggle).display === 'none';
        }

        // Test 2: Touch target sizes
        const touchTargets = document.querySelectorAll('.nav-item, .auth-btn, .user-button, .mobile-menu-btn, .mobile-nav-item');
        let validTouchTargets = 0;
        let totalVisibleTargets = 0;

        touchTargets.forEach(target => {
            const styles = window.getComputedStyle(target);
            if (styles.display !== 'none' && styles.visibility !== 'hidden') {
                totalVisibleTargets++;
                const rect = target.getBoundingClientRect();
                if (rect.height >= 44 && rect.width >= 44) {
                    validTouchTargets++;
                }
            }
        });

        results.tests.touchTargetsValid = totalVisibleTargets === 0 || validTouchTargets === totalVisibleTargets;

        // Test 3: Biblical Tokenomics prominence
        let featuredItem;
        if (width <= 768) {
            featuredItem = document.querySelector('.mobile-nav-item.featured');
        } else {
            featuredItem = document.querySelector('.nav-item-featured');
        }

        if (featuredItem && window.getComputedStyle(featuredItem).display !== 'none') {
            const styles = window.getComputedStyle(featuredItem);
            results.tests.biblicalTokenomicsProminent =
                (styles.background.includes('gradient') || styles.background.includes('linear-gradient')) &&
                (styles.borderColor.includes('255') || styles.borderWidth !== '1px') &&
                styles.boxShadow !== 'none';
        } else {
            results.tests.biblicalTokenomicsProminent = false;
        }

        // Test 4: Authentication button sizing
        const authBtns = document.querySelectorAll('.auth-btn');
        let properSizedAuthBtns = 0;

        authBtns.forEach(btn => {
            const rect = btn.getBoundingClientRect();
            const maxWidth = width <= 320 ? 120 : width <= 480 ? 120 : width <= 768 ? 140 : 160;
            if (rect.width <= maxWidth + 10) { // 10px tolerance
                properSizedAuthBtns++;
            }
        });

        results.tests.authButtonsSized = authBtns.length === 0 || properSizedAuthBtns === authBtns.length;

        // Test 5: Logo scaling
        const logoText = document.querySelector('.logo-text');
        if (logoText) {
            const fontSize = parseFloat(window.getComputedStyle(logoText).fontSize);
            const expectedSize = width <= 320 ? 18 : width <= 480 ? 20 : width <= 768 ? 24 : 28;
            results.tests.logoScaling = Math.abs(fontSize - expectedSize) <= 4;
        }

        // Count passed tests
        Object.values(results.tests).forEach(passed => {
            results.total++;
            if (passed) results.passed++;
        });

        // Generate issues
        Object.entries(results.tests).forEach(([test, passed]) => {
            if (!passed) {
                results.issues.push(this.getIssueDescription(test, breakpointName));
            }
        });

        return results;
    }

    getIssueDescription(test, breakpoint) {
        const descriptions = {
            mobileMenuVisible: `Mobile menu toggle not visible on ${breakpoint}`,
            desktopNavHidden: `Desktop navigation not hidden on ${breakpoint}`,
            desktopNavVisible: `Desktop navigation not visible on ${breakpoint}`,
            mobileMenuHidden: `Mobile menu toggle not hidden on ${breakpoint}`,
            touchTargetsValid: `Touch targets too small on ${breakpoint} (minimum 44px required)`,
            biblicalTokenomicsProminent: `Biblical Tokenomics not prominent enough on ${breakpoint}`,
            authButtonsSized: `Authentication buttons not properly sized on ${breakpoint}`,
            logoScaling: `Logo not scaling correctly on ${breakpoint}`
        };

        return descriptions[test] || `Unknown issue with ${test} on ${breakpoint}`;
    }

    displayResults(breakpointName, results) {
        const passRate = ((results.passed / results.total) * 100).toFixed(1);
        const status = passRate >= 80 ? '✅' : passRate >= 60 ? '⚠️' : '❌';

        console.log(`📊 ${breakpointName} Results:`);
        console.log(`${status} Pass Rate: ${passRate}% (${results.passed}/${results.total})`);
        if (results.issues.length > 0) {
            console.log(`Issues: ${results.issues.length}`);
            results.issues.forEach(issue => console.log(`  - ${issue}`));
        }
        console.log('Full results:', results);
    }

    runAllTests() {
        console.log('🧪 Running comprehensive navigation tests...');

        let testIndex = 0;
        const breakpointEntries = Object.entries(this.breakpoints);

        const runNextTest = () => {
            if (testIndex < breakpointEntries.length) {
                const [name, width] = breakpointEntries[testIndex];
                this.testBreakpoint(width, name);
                testIndex++;
                setTimeout(runNextTest, 1000);
            } else {
                this.generateReport();
            }
        };

        runNextTest();
    }

    generateReport() {
        console.log('📋 Generating comprehensive test report...');

        const report = {
            timestamp: new Date().toISOString(),
            totalBreakpoints: Object.keys(this.testResults).length,
            overallIssues: [],
            recommendations: []
        };

        // Analyze results
        Object.values(this.testResults).forEach(result => {
            report.overallIssues.push(...result.issues);
        });

        // Generate recommendations
        if (report.overallIssues.length === 0) {
            report.recommendations.push('🎉 All navigation tests passed! Excellent responsive design.');
        } else {
            report.recommendations.push('🔧 Review and fix the identified issues for optimal user experience.');
        }

        console.log('📊 Final Navigation Test Report:', report);

        // Store results for console access
        window.lastNavigationTestReport = report;
        console.log('\n💡 Access full report via: window.lastNavigationTestReport');
    }

    runInitialTests() {
        // Initial tests removed - use console commands instead
        console.log('🔧 Navigation testing ready. Use console commands to run tests manually.');
    }
}

// Initialize the navigation tester
const navTester = new NavigationTester();

// Make it globally accessible
window.navTester = navTester;
