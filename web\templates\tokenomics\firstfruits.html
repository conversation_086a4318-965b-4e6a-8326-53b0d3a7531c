{% extends "base.html" %}

{% block title %}Firstfruits Offerings - Biblical Tokenomics{% endblock %}

{% block head %}
<style>
    .offering-form {
        background: linear-gradient(135deg, rgba(154, 0, 255, 0.1) 0%, rgba(0, 255, 247, 0.1) 100%);
        border: 1px solid rgba(154, 0, 255, 0.3);
    }
    
    .token-selector {
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .token-selector:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 255, 247, 0.2);
    }
    
    .token-selector.selected {
        border-color: #00fff7;
        background: rgba(0, 255, 247, 0.1);
    }
    
    .offering-preview {
        background: linear-gradient(135deg, rgba(0, 255, 247, 0.1) 0%, rgba(154, 0, 255, 0.1) 100%);
        border: 1px solid rgba(0, 255, 247, 0.3);
    }
    
    .etzem-reward {
        background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 255, 255, 0.1) 100%);
        border: 1px solid rgba(255, 215, 0, 0.3);
    }
    
    .verse-highlight {
        background: linear-gradient(90deg, rgba(0, 255, 247, 0.1) 0%, transparent 100%);
        border-left: 3px solid #00fff7;
        padding: 1rem;
        font-style: italic;
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-onyx-black">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-orbitron font-bold mb-4">
                <span class="bg-gradient-to-r from-cyber-purple to-cyber-cyan bg-clip-text text-transparent">
                    Firstfruits Offerings
                </span>
            </h1>
            <p class="text-xl text-gray-300 mb-6">
                Honor God with the first of your harvest and support the community
            </p>
            
            <!-- Biblical Verse -->
            <div class="verse-highlight max-w-2xl mx-auto mb-8">
                "Bring the best of the firstfruits of your soil to the house of the Lord your God." 
                <br><strong>- Deuteronomy 26:2</strong>
            </div>
        </div>
        
        <!-- Pool Information -->
        <div class="glass-card p-6 mb-8">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                <div>
                    <div class="text-3xl mb-2">🎁</div>
                    <h3 class="text-lg font-semibold text-cyber-purple">Firstfruits Pool</h3>
                    <p class="text-2xl font-bold">{{ "%.2f"|format(pool_balance) }}</p>
                    <p class="text-sm text-gray-400">ONX Contributed</p>
                </div>
                
                <div>
                    <div class="text-3xl mb-2">✨</div>
                    <h3 class="text-lg font-semibold text-cyber-cyan">Etzem Reward</h3>
                    <p class="text-2xl font-bold">{{ etzem_reward }}</p>
                    <p class="text-sm text-gray-400">Tokens per Offering</p>
                </div>
                
                <div>
                    <div class="text-3xl mb-2">🙏</div>
                    <h3 class="text-lg font-semibold text-yellow-400">Your Offerings</h3>
                    <p class="text-2xl font-bold">{{ previous_offerings|length }}</p>
                    <p class="text-sm text-gray-400">Total Made</p>
                </div>
            </div>
        </div>
        
        <!-- Offering Form -->
        <div class="offering-form glass-card p-8 mb-8">
            <h2 class="text-2xl font-semibold mb-6 text-center">Make a Firstfruits Offering</h2>
            
            <form id="offeringForm" action="/api/tokenomics/firstfruits" method="POST">
                <!-- Token Selection -->
                <div class="mb-6">
                    <label class="block text-sm font-medium mb-4">Select Token to Offer</label>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {% for token in token_balances %}
                        <div class="token-selector glass-card p-4" data-token-id="{{ token.token_id }}" data-balance="{{ token.balance }}">
                            <div class="flex justify-between items-center">
                                <div>
                                    <h4 class="font-semibold">{{ token.symbol }}</h4>
                                    <p class="text-sm text-gray-400">{{ token.name }}</p>
                                </div>
                                <div class="text-right">
                                    <p class="font-bold">{{ "%.2f"|format(token.balance) }}</p>
                                    <p class="text-xs text-gray-400">Available</p>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    <input type="hidden" id="selectedToken" name="token_id" required>
                </div>
                
                <!-- Amount Input -->
                <div class="mb-6">
                    <label for="amount" class="block text-sm font-medium mb-2">Offering Amount</label>
                    <div class="relative">
                        <input type="number" 
                               id="amount" 
                               name="amount" 
                               step="0.01" 
                               min="0.01" 
                               class="w-full px-4 py-3 bg-onyx-gray border border-gray-600 rounded-lg focus:border-cyber-cyan focus:ring-1 focus:ring-cyber-cyan text-white"
                               placeholder="Enter amount to offer"
                               required>
                        <div class="absolute right-3 top-3 text-gray-400">
                            <span id="selectedTokenSymbol">Select token first</span>
                        </div>
                    </div>
                    <div class="mt-2 flex justify-between text-sm">
                        <span class="text-gray-400">Available: <span id="availableBalance">0</span></span>
                        <button type="button" id="maxButton" class="text-cyber-cyan hover:text-cyber-cyan/80">Use Max</button>
                    </div>
                </div>
                
                <!-- Offering Preview -->
                <div id="offeringPreview" class="offering-preview p-6 mb-6" style="display: none;">
                    <h3 class="text-lg font-semibold mb-4 text-center">Offering Preview</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="text-center">
                            <p class="text-sm text-gray-400">You Will Offer</p>
                            <p class="text-xl font-bold text-cyber-purple">
                                <span id="previewAmount">0</span> <span id="previewToken">TOKEN</span>
                            </p>
                        </div>
                        <div class="etzem-reward text-center p-4 rounded-lg">
                            <p class="text-sm text-gray-400">You Will Receive</p>
                            <p class="text-xl font-bold text-yellow-400">
                                {{ etzem_reward }} ETZEM
                            </p>
                        </div>
                    </div>
                </div>
                
                <!-- Submit Button -->
                <button type="submit" 
                        id="submitButton"
                        class="w-full py-4 bg-gradient-to-r from-cyber-purple to-cyber-cyan text-white font-semibold rounded-lg hover:from-cyber-purple/90 hover:to-cyber-cyan/90 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                        disabled>
                    <span class="mr-2">🎁</span>Make Firstfruits Offering
                </button>
            </form>
        </div>
        
        <!-- Previous Offerings -->
        {% if previous_offerings %}
        <div class="glass-card p-6">
            <h3 class="text-xl font-semibold mb-6">Your Previous Offerings</h3>
            
            <div class="space-y-4">
                {% for offering in previous_offerings %}
                <div class="glass-card p-4 flex justify-between items-center">
                    <div>
                        <p class="font-semibold">{{ "%.2f"|format(offering.deed_value) }} Deed Value</p>
                        <p class="text-sm text-gray-400">{{ offering.description or 'Firstfruits offering' }}</p>
                        <p class="text-xs text-gray-500">
                            {{ offering.timestamp|timestamp_to_date if offering.timestamp else 'Unknown date' }}
                        </p>
                    </div>
                    <div class="text-right">
                        <span class="px-3 py-1 bg-cyber-purple/20 text-cyber-purple rounded text-sm">
                            Offering
                        </span>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
        
        <!-- Educational Information -->
        <div class="glass-card p-6 mt-8">
            <h3 class="text-xl font-semibold mb-4">About Firstfruits Offerings</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h4 class="font-semibold text-cyber-cyan mb-2">Biblical Foundation</h4>
                    <p class="text-sm text-gray-300 mb-4">
                        Firstfruits offerings represent giving God the first and best of what we receive. 
                        This practice acknowledges God's provision and demonstrates trust in His continued blessing.
                    </p>
                    
                    <h4 class="font-semibold text-cyber-purple mb-2">Community Impact</h4>
                    <p class="text-sm text-gray-300">
                        Your offerings contribute to the firstfruits pool, which supports community initiatives 
                        and provides resources for those in need through the gleaning system.
                    </p>
                </div>
                
                <div>
                    <h4 class="font-semibold text-cyber-blue mb-2">Etzem Tokens</h4>
                    <p class="text-sm text-gray-300 mb-4">
                        Etzem tokens represent the essence of your spiritual contribution. They serve as 
                        recognition of your faithfulness and can be used for special community functions.
                    </p>
                    
                    <h4 class="font-semibold text-yellow-400 mb-2">Spiritual Rewards</h4>
                    <p class="text-sm text-gray-300">
                        Beyond material tokens, firstfruits offerings increase your deed score and 
                        demonstrate your commitment to biblical economic principles.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const tokenSelectors = document.querySelectorAll('.token-selector');
    const selectedTokenInput = document.getElementById('selectedToken');
    const selectedTokenSymbol = document.getElementById('selectedTokenSymbol');
    const availableBalance = document.getElementById('availableBalance');
    const amountInput = document.getElementById('amount');
    const maxButton = document.getElementById('maxButton');
    const submitButton = document.getElementById('submitButton');
    const offeringPreview = document.getElementById('offeringPreview');
    const previewAmount = document.getElementById('previewAmount');
    const previewToken = document.getElementById('previewToken');
    
    let selectedToken = null;
    let selectedBalance = 0;
    
    // Token selection
    tokenSelectors.forEach(selector => {
        selector.addEventListener('click', function() {
            // Remove previous selection
            tokenSelectors.forEach(s => s.classList.remove('selected'));
            
            // Select current token
            this.classList.add('selected');
            selectedToken = this.dataset.tokenId;
            selectedBalance = parseFloat(this.dataset.balance);
            
            // Update UI
            selectedTokenInput.value = selectedToken;
            selectedTokenSymbol.textContent = this.querySelector('h4').textContent;
            availableBalance.textContent = selectedBalance.toFixed(2);
            amountInput.max = selectedBalance;
            
            updatePreview();
            validateForm();
        });
    });
    
    // Max button
    maxButton.addEventListener('click', function() {
        if (selectedToken) {
            amountInput.value = selectedBalance.toFixed(2);
            updatePreview();
            validateForm();
        }
    });
    
    // Amount input
    amountInput.addEventListener('input', function() {
        updatePreview();
        validateForm();
    });
    
    function updatePreview() {
        const amount = parseFloat(amountInput.value) || 0;
        
        if (selectedToken && amount > 0) {
            previewAmount.textContent = amount.toFixed(2);
            previewToken.textContent = selectedTokenSymbol.textContent;
            offeringPreview.style.display = 'block';
        } else {
            offeringPreview.style.display = 'none';
        }
    }
    
    function validateForm() {
        const amount = parseFloat(amountInput.value) || 0;
        const isValid = selectedToken && amount > 0 && amount <= selectedBalance;
        
        submitButton.disabled = !isValid;
    }
    
    // Form submission
    document.getElementById('offeringForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const data = {
            token_id: formData.get('token_id'),
            amount: parseFloat(formData.get('amount'))
        };
        
        try {
            submitButton.disabled = true;
            submitButton.innerHTML = '<span class="mr-2">⏳</span>Processing Offering...';
            
            const response = await fetch('/api/tokenomics/firstfruits', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });
            
            const result = await response.json();
            
            if (response.ok) {
                // Success
                alert(`Firstfruits offering successful! You received ${result.etzem_reward} ETZEM tokens.`);
                window.location.reload();
            } else {
                // Error
                alert(`Error: ${result.error || 'Failed to process offering'}`);
            }
        } catch (error) {
            alert(`Error: ${error.message}`);
        } finally {
            submitButton.disabled = false;
            submitButton.innerHTML = '<span class="mr-2">🎁</span>Make Firstfruits Offering';
        }
    });
});
</script>
{% endblock %}
