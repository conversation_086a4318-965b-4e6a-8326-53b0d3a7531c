"""
Biblical Tokenomics Routes

Web interface for biblical tokenomics features including:
- Dashboard with gleaning pool, loans, and deed scores
- Interactive forms for firstfruits offerings and loans
- Sabbath status and mining reward calculator
- Token classification and concentration monitoring
"""

import os
import sys
import json
import time
import uuid
import logging
from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for, flash

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from shared.models.tokenomics import biblical_tokenomics
from shared.config.chain_parameters import chain_parameters
from shared.db.db import db

logger = logging.getLogger("onnyx.web.tokenomics")

tokenomics_bp = Blueprint('tokenomics', __name__)

def require_auth(f):
    """Decorator to require authentication."""
    from functools import wraps

    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'identity_id' not in session:
            flash('Please log in to access biblical tokenomics features.', 'warning')
            return redirect(url_for('auth.login'))
        return f(*args, **kwargs)
    return decorated_function

@tokenomics_bp.route('/')
def overview():
    """Biblical tokenomics overview page (public)."""
    try:
        # Get public statistics
        stats = {
            'is_sabbath': biblical_tokenomics.is_sabbath_period(),
            'gleaning_pool_balance': 0,
            'active_loans': 0,
            'total_deeds': 0,
            'sabbath_observers': 0
        }
        
        # Get gleaning pool balance
        pool_result = db.query_one("""
            SELECT total_amount FROM jubilee_pools 
            WHERE pool_id = 'GLEANS_POOL' AND pool_type = 'GLEANING'
        """)
        if pool_result:
            stats['gleaning_pool_balance'] = pool_result['total_amount']
        
        # Get active loans count
        loans_result = db.query_one("SELECT COUNT(*) as count FROM loans WHERE status = 'ACTIVE'")
        if loans_result:
            stats['active_loans'] = loans_result['count']
        
        # Get total deeds count
        deeds_result = db.query_one("SELECT COUNT(*) as count FROM deeds_ledger")
        if deeds_result:
            stats['total_deeds'] = deeds_result['count']
        
        # Get sabbath observers count
        observers_result = db.query_one("SELECT COUNT(*) as count FROM identities WHERE sabbath_observer = 1")
        if observers_result:
            stats['sabbath_observers'] = observers_result['count']
        
        return render_template('tokenomics/overview.html', stats=stats)
        
    except Exception as e:
        logger.error(f"Error loading tokenomics overview: {e}")
        flash('Error loading biblical tokenomics overview.', 'error')
        return redirect(url_for('index'))

@tokenomics_bp.route('/dashboard')
@require_auth
def dashboard():
    """Personal biblical tokenomics dashboard."""
    try:
        identity_id = session['identity_id']
        
        # Get user's deed score and statistics
        user_stats = {
            'deed_score': biblical_tokenomics._get_deed_score(identity_id),
            'is_sabbath_observer': False,
            'concentration_status': 'normal',
            'total_balance': 0,
            'recent_deeds': [],
            'active_loans_as_borrower': [],
            'active_loans_as_lender': [],
            'token_classifications': []
        }
        
        # Check if user is sabbath observer
        observer_result = db.query_one("""
            SELECT sabbath_observer FROM identities WHERE identity_id = ?
        """, (identity_id,))
        if observer_result:
            user_stats['is_sabbath_observer'] = bool(observer_result['sabbath_observer'])
        
        # Get user's total balance for concentration check
        user_stats['total_balance'] = biblical_tokenomics._get_total_balance(identity_id)
        concentration_threshold = chain_parameters.get("concentration_threshold", 1000000)
        if user_stats['total_balance'] > concentration_threshold:
            user_stats['concentration_status'] = 'warning'
        
        # Get recent deeds
        user_stats['recent_deeds'] = db.query("""
            SELECT * FROM deeds_ledger 
            WHERE identity_id = ? 
            ORDER BY timestamp DESC 
            LIMIT 10
        """, (identity_id,))
        
        # Get active loans as borrower
        user_stats['active_loans_as_borrower'] = db.query("""
            SELECT * FROM loans 
            WHERE borrower_id = ? AND status = 'ACTIVE'
            ORDER BY created_at DESC
        """, (identity_id,))
        
        # Get active loans as lender
        user_stats['active_loans_as_lender'] = db.query("""
            SELECT * FROM loans 
            WHERE lender_id = ? AND status = 'ACTIVE'
            ORDER BY created_at DESC
        """, (identity_id,))
        
        # Get token classifications for user's tokens
        user_stats['token_classifications'] = db.query("""
            SELECT DISTINCT tc.token_id, tc.class_type, tc.class_metadata, t.name, t.symbol
            FROM token_classes tc
            JOIN tokens t ON tc.token_id = t.token_id
            JOIN token_balances tb ON t.token_id = tb.token_id
            WHERE tb.identity_id = ? AND tb.balance > 0
        """, (identity_id,))
        
        # Get system-wide statistics
        system_stats = {
            'is_sabbath': biblical_tokenomics.is_sabbath_period(),
            'gleaning_pool_balance': 0,
            'total_active_loans': 0,
            'min_reward': chain_parameters.get("min_block_reward", 2),
            'max_reward': chain_parameters.get("max_block_reward", 200),
            'concentration_threshold': concentration_threshold
        }
        
        # Get gleaning pool balance
        pool_result = db.query_one("""
            SELECT total_amount FROM jubilee_pools 
            WHERE pool_id = 'GLEANS_POOL' AND pool_type = 'GLEANING'
        """)
        if pool_result:
            system_stats['gleaning_pool_balance'] = pool_result['total_amount']
        
        # Get total active loans
        loans_result = db.query_one("SELECT COUNT(*) as count FROM loans WHERE status = 'ACTIVE'")
        if loans_result:
            system_stats['total_active_loans'] = loans_result['count']
        
        return render_template('tokenomics/dashboard.html', 
                             user_stats=user_stats, 
                             system_stats=system_stats)
        
    except Exception as e:
        logger.error(f"Error loading tokenomics dashboard: {e}")
        flash('Error loading biblical tokenomics dashboard.', 'error')
        return redirect(url_for('dashboard.overview'))

@tokenomics_bp.route('/firstfruits')
@require_auth
def firstfruits():
    """Firstfruits offering page."""
    try:
        identity_id = session['identity_id']
        
        # Get user's token balances
        token_balances = db.query("""
            SELECT tb.*, t.name, t.symbol, t.category
            FROM token_balances tb
            JOIN tokens t ON tb.token_id = t.token_id
            WHERE tb.identity_id = ? AND tb.balance > 0
            ORDER BY tb.balance DESC
        """, (identity_id,))
        
        # Get firstfruits pool information
        pool_result = db.query_one("""
            SELECT total_amount FROM jubilee_pools 
            WHERE pool_id = 'FIRSTFRUITS_POOL' AND pool_type = 'FIRSTFRUITS'
        """)
        pool_balance = pool_result['total_amount'] if pool_result else 0
        
        # Get user's previous offerings
        previous_offerings = db.query("""
            SELECT * FROM deeds_ledger 
            WHERE identity_id = ? AND deed_type = 'FIRSTFRUITS'
            ORDER BY timestamp DESC 
            LIMIT 5
        """, (identity_id,))
        
        return render_template('tokenomics/firstfruits.html',
                             token_balances=token_balances,
                             pool_balance=pool_balance,
                             previous_offerings=previous_offerings,
                             etzem_reward=chain_parameters.get("firstfruits_etzem_reward", 2))
        
    except Exception as e:
        logger.error(f"Error loading firstfruits page: {e}")
        flash('Error loading firstfruits offering page.', 'error')
        return redirect(url_for('tokenomics.dashboard'))

@tokenomics_bp.route('/loans')
@require_auth
def loans():
    """Interest-free loans page."""
    try:
        identity_id = session['identity_id']
        
        # Get user's loans as borrower
        loans_as_borrower = db.query("""
            SELECT l.*, i.name as lender_name
            FROM loans l
            JOIN identities i ON l.lender_id = i.identity_id
            WHERE l.borrower_id = ?
            ORDER BY l.created_at DESC
        """, (identity_id,))
        
        # Get user's loans as lender
        loans_as_lender = db.query("""
            SELECT l.*, i.name as borrower_name
            FROM loans l
            JOIN identities i ON l.borrower_id = i.identity_id
            WHERE l.lender_id = ?
            ORDER BY l.created_at DESC
        """, (identity_id,))
        
        # Get user's token balances for lending
        token_balances = db.query("""
            SELECT tb.*, t.name, t.symbol
            FROM token_balances tb
            JOIN tokens t ON tb.token_id = t.token_id
            WHERE tb.identity_id = ? AND tb.balance > 0
            ORDER BY tb.balance DESC
        """, (identity_id,))
        
        # Get available identities for lending (excluding self)
        available_borrowers = db.query("""
            SELECT identity_id, name FROM identities 
            WHERE identity_id != ?
            ORDER BY name
        """, (identity_id,))
        
        return render_template('tokenomics/loans.html',
                             loans_as_borrower=loans_as_borrower,
                             loans_as_lender=loans_as_lender,
                             token_balances=token_balances,
                             available_borrowers=available_borrowers,
                             grace_blocks=chain_parameters.get("loan_grace_blocks", 14400),
                             forgiveness_threshold=chain_parameters.get("loan_forgiveness_threshold", 0.8))
        
    except Exception as e:
        logger.error(f"Error loading loans page: {e}")
        flash('Error loading loans page.', 'error')
        return redirect(url_for('tokenomics.dashboard'))

@tokenomics_bp.route('/gleaning')
@require_auth
def gleaning():
    """Gleaning pool page."""
    try:
        identity_id = session['identity_id']
        
        # Get gleaning pool information
        pool_result = db.query_one("""
            SELECT * FROM jubilee_pools 
            WHERE pool_id = 'GLEANS_POOL' AND pool_type = 'GLEANING'
        """)
        
        pool_info = {
            'balance': pool_result['total_amount'] if pool_result else 0,
            'last_distribution': pool_result['last_distribution'] if pool_result else 0,
            'created_at': pool_result['created_at'] if pool_result else 0
        }
        
        # Get recent gleaning claims
        recent_claims = db.query("""
            SELECT * FROM deeds_ledger 
            WHERE deed_type = 'GLEANING_CLAIM'
            ORDER BY timestamp DESC 
            LIMIT 10
        """)
        
        # Get user's gleaning history
        user_claims = db.query("""
            SELECT * FROM deeds_ledger 
            WHERE identity_id = ? AND deed_type = 'GLEANING_CLAIM'
            ORDER BY timestamp DESC 
            LIMIT 5
        """, (identity_id,))
        
        # Check if user is eligible for gleaning
        user_balance = biblical_tokenomics._get_total_balance(identity_id)
        gleaning_threshold = chain_parameters.get("gleaning_eligibility_threshold", 100)
        is_eligible = user_balance < gleaning_threshold
        
        return render_template('tokenomics/gleaning.html',
                             pool_info=pool_info,
                             recent_claims=recent_claims,
                             user_claims=user_claims,
                             is_eligible=is_eligible,
                             gleaning_threshold=gleaning_threshold,
                             max_claim_amount=chain_parameters.get("max_gleaning_claim", 50))
        
    except Exception as e:
        logger.error(f"Error loading gleaning page: {e}")
        flash('Error loading gleaning pool page.', 'error')
        return redirect(url_for('tokenomics.dashboard'))

@tokenomics_bp.route('/calculator')
def calculator():
    """Mining reward calculator (public)."""
    try:
        # Get current system parameters
        params = {
            'min_reward': chain_parameters.get("min_block_reward", 2),
            'max_reward': chain_parameters.get("max_block_reward", 200),
            'deed_multiplier': chain_parameters.get("deed_score_multiplier", 0.1),
            'gleaning_percentage': chain_parameters.get("gleaning_pool_percentage", 0.02),
            'concentration_threshold': chain_parameters.get("concentration_threshold", 1000000),
            'concentration_penalty': chain_parameters.get("concentration_penalty_rate", 0.1),
            'is_sabbath': biblical_tokenomics.is_sabbath_period()
        }
        
        return render_template('tokenomics/calculator.html', params=params)
        
    except Exception as e:
        logger.error(f"Error loading calculator: {e}")
        flash('Error loading mining reward calculator.', 'error')
        return redirect(url_for('tokenomics.overview'))

@tokenomics_bp.route('/education')
def education():
    """Biblical principles education page (public)."""
    try:
        # Get current system statistics for examples
        stats = {
            'gleaning_pool_balance': 0,
            'active_loans': 0,
            'sabbath_observers': 0,
            'total_deeds': 0,
            'is_sabbath': biblical_tokenomics.is_sabbath_period()
        }
        
        # Get basic statistics
        pool_result = db.query_one("""
            SELECT total_amount FROM jubilee_pools 
            WHERE pool_id = 'GLEANS_POOL' AND pool_type = 'GLEANING'
        """)
        if pool_result:
            stats['gleaning_pool_balance'] = pool_result['total_amount']
        
        loans_result = db.query_one("SELECT COUNT(*) as count FROM loans WHERE status = 'ACTIVE'")
        if loans_result:
            stats['active_loans'] = loans_result['count']
        
        observers_result = db.query_one("SELECT COUNT(*) as count FROM identities WHERE sabbath_observer = 1")
        if observers_result:
            stats['sabbath_observers'] = observers_result['count']
        
        deeds_result = db.query_one("SELECT COUNT(*) as count FROM deeds_ledger")
        if deeds_result:
            stats['total_deeds'] = deeds_result['count']
        
        return render_template('tokenomics/education.html', stats=stats)
        
    except Exception as e:
        logger.error(f"Error loading education page: {e}")
        flash('Error loading biblical principles education.', 'error')
        return redirect(url_for('tokenomics.overview'))
