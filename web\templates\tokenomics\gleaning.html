{% extends "base.html" %}

{% block title %}Gleaning Pool - Biblical Tokenomics{% endblock %}

{% block head %}
<style>
    .gleaning-form {
        background: linear-gradient(135deg, rgba(0, 255, 247, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
        border: 1px solid rgba(0, 255, 247, 0.3);
    }
    
    .eligibility-card {
        transition: all 0.3s ease;
    }
    
    .eligible {
        background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(0, 255, 247, 0.1) 100%);
        border: 1px solid rgba(16, 185, 129, 0.3);
    }
    
    .not-eligible {
        background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(245, 101, 101, 0.1) 100%);
        border: 1px solid rgba(239, 68, 68, 0.3);
    }
    
    .claim-card {
        border-left: 4px solid;
        transition: all 0.3s ease;
    }
    
    .claim-card:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 15px rgba(0, 255, 247, 0.15);
    }
    
    .claim-approved { border-left-color: #10b981; }
    .claim-pending { border-left-color: #f59e0b; }
    .claim-rejected { border-left-color: #ef4444; }
    
    .pool-stats {
        background: linear-gradient(135deg, rgba(0, 255, 247, 0.1) 0%, rgba(154, 0, 255, 0.1) 100%);
        border: 1px solid rgba(0, 255, 247, 0.3);
    }
    
    .verse-highlight {
        background: linear-gradient(90deg, rgba(0, 255, 247, 0.1) 0%, transparent 100%);
        border-left: 3px solid #00fff7;
        padding: 1rem;
        font-style: italic;
    }
    
    .amount-slider {
        width: 100%;
        height: 8px;
        border-radius: 4px;
        background: linear-gradient(90deg, #2a2a2a 0%, #00fff7 100%);
        outline: none;
        -webkit-appearance: none;
    }
    
    .amount-slider::-webkit-slider-thumb {
        -webkit-appearance: none;
        appearance: none;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #00fff7;
        cursor: pointer;
        box-shadow: 0 0 10px rgba(0, 255, 247, 0.5);
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-onyx-black">
    <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-orbitron font-bold mb-4">
                <span class="bg-gradient-to-r from-cyber-cyan to-green-400 bg-clip-text text-transparent">
                    Gleaning Pool
                </span>
            </h1>
            <p class="text-xl text-gray-300 mb-6">
                Community support system based on biblical gleaning laws
            </p>
            
            <!-- Biblical Verse -->
            <div class="verse-highlight max-w-3xl mx-auto mb-8">
                "When you reap the harvest of your land, do not reap to the very edges of your field or gather the gleanings of your harvest. Leave them for the poor and for the foreigner residing among you." 
                <br><strong>- Leviticus 19:9-10</strong>
            </div>
        </div>
        
        <!-- Pool Statistics -->
        <div class="pool-stats glass-card p-6 mb-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 text-center">
                <div>
                    <div class="text-3xl mb-2">🌾</div>
                    <h3 class="text-lg font-semibold text-cyber-cyan">Pool Balance</h3>
                    <p class="text-2xl font-bold">{{ "%.2f"|format(pool_info.balance) }}</p>
                    <p class="text-sm text-gray-400">ONX Available</p>
                </div>
                
                <div>
                    <div class="text-3xl mb-2">📅</div>
                    <h3 class="text-lg font-semibold text-green-400">Last Distribution</h3>
                    <p class="text-lg font-bold">
                        {% if pool_info.last_distribution %}
                        {{ pool_info.last_distribution|timestamp_to_date }}
                        {% else %}
                        Never
                        {% endif %}
                    </p>
                    <p class="text-sm text-gray-400">Most Recent</p>
                </div>
                
                <div>
                    <div class="text-3xl mb-2">🎯</div>
                    <h3 class="text-lg font-semibold text-yellow-400">Eligibility Threshold</h3>
                    <p class="text-2xl font-bold">{{ gleaning_threshold }}</p>
                    <p class="text-sm text-gray-400">ONX Maximum Balance</p>
                </div>
                
                <div>
                    <div class="text-3xl mb-2">💰</div>
                    <h3 class="text-lg font-semibold text-cyber-purple">Max Claim</h3>
                    <p class="text-2xl font-bold">{{ max_claim_amount }}</p>
                    <p class="text-sm text-gray-400">ONX Per Request</p>
                </div>
            </div>
        </div>
        
        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Eligibility & Claim Form -->
            <div class="space-y-6">
                <!-- Eligibility Status -->
                <div class="eligibility-card glass-card p-6 {% if is_eligible %}eligible{% else %}not-eligible{% endif %}">
                    <div class="flex items-center space-x-4 mb-4">
                        <div class="text-4xl">
                            {% if is_eligible %}✅{% else %}❌{% endif %}
                        </div>
                        <div>
                            <h3 class="text-xl font-semibold">
                                {% if is_eligible %}Eligible for Gleaning{% else %}Not Currently Eligible{% endif %}
                            </h3>
                            <p class="text-sm text-gray-400">
                                {% if is_eligible %}
                                You can claim from the gleaning pool
                                {% else %}
                                Your balance exceeds the threshold
                                {% endif %}
                            </p>
                        </div>
                    </div>
                    
                    <div class="text-sm">
                        <p class="mb-2">
                            <strong>Your Balance:</strong> 
                            <span class="{% if is_eligible %}text-green-400{% else %}text-red-400{% endif %}">
                                {{ "%.2f"|format(user_balance if user_balance is defined else 0) }} ONX
                            </span>
                        </p>
                        <p>
                            <strong>Threshold:</strong> {{ gleaning_threshold }} ONX
                        </p>
                    </div>
                </div>
                
                <!-- Claim Form -->
                {% if is_eligible %}
                <div class="gleaning-form glass-card p-6">
                    <h3 class="text-xl font-semibold mb-6">Request Gleaning Support</h3>
                    
                    <form id="gleaningForm" action="/api/tokenomics/gleaning-pool/claim" method="POST">
                        <!-- Amount Selection -->
                        <div class="mb-6">
                            <label for="claimAmount" class="block text-sm font-medium mb-2">
                                Claim Amount (ONX)
                            </label>
                            <div class="space-y-3">
                                <input type="range" 
                                       id="amountSlider" 
                                       class="amount-slider" 
                                       min="1" 
                                       max="{{ max_claim_amount }}" 
                                       value="{{ (max_claim_amount / 2)|round }}" 
                                       step="1">
                                <div class="flex justify-between text-sm text-gray-400">
                                    <span>1 ONX</span>
                                    <span id="selectedAmount" class="text-cyber-cyan font-bold">{{ (max_claim_amount / 2)|round }} ONX</span>
                                    <span>{{ max_claim_amount }} ONX</span>
                                </div>
                                <input type="hidden" id="claimAmount" name="amount" value="{{ (max_claim_amount / 2)|round }}">
                            </div>
                        </div>
                        
                        <!-- Justification -->
                        <div class="mb-6">
                            <label for="justification" class="block text-sm font-medium mb-2">
                                Justification <span class="text-red-400">*</span>
                            </label>
                            <textarea id="justification" 
                                      name="justification" 
                                      rows="4" 
                                      class="w-full px-4 py-3 bg-onyx-gray border border-gray-600 rounded-lg focus:border-cyber-cyan focus:ring-1 focus:ring-cyber-cyan text-white"
                                      placeholder="Please explain your need for gleaning support..."
                                      required></textarea>
                            <p class="text-xs text-gray-500 mt-1">
                                Describe your situation and how this support will help you.
                            </p>
                        </div>
                        
                        <!-- Submit Button -->
                        <button type="submit" 
                                id="submitClaim"
                                class="w-full py-4 bg-gradient-to-r from-cyber-cyan to-green-400 text-white font-semibold rounded-lg hover:from-cyber-cyan/90 hover:to-green-400/90 transition-all duration-300 transform hover:scale-105">
                            <span class="mr-2">🌾</span>Request Gleaning Support
                        </button>
                    </form>
                </div>
                {% else %}
                <div class="glass-card p-6 text-center">
                    <div class="text-4xl mb-4">💰</div>
                    <h3 class="text-lg font-semibold mb-2">Balance Too High</h3>
                    <p class="text-gray-400 mb-4">
                        Your current balance exceeds the gleaning threshold. The gleaning pool is reserved 
                        for those with limited resources.
                    </p>
                    <p class="text-sm text-gray-500">
                        Consider making a firstfruits offering to support others in need.
                    </p>
                    <a href="{{ url_for('tokenomics.firstfruits') }}" 
                       class="inline-block mt-4 px-4 py-2 bg-cyber-purple text-white rounded hover:bg-cyber-purple/90">
                        Make an Offering
                    </a>
                </div>
                {% endif %}
            </div>
            
            <!-- Recent Activity -->
            <div class="space-y-6">
                <!-- Your Claims History -->
                {% if user_claims %}
                <div class="glass-card p-6">
                    <h3 class="text-xl font-semibold mb-4">Your Gleaning History</h3>
                    
                    <div class="space-y-3">
                        {% for claim in user_claims %}
                        <div class="claim-card claim-approved glass-card p-4">
                            <div class="flex justify-between items-start">
                                <div>
                                    <p class="font-semibold">{{ "%.2f"|format(claim.deed_value) }} ONX</p>
                                    <p class="text-sm text-gray-400">{{ claim.description or 'Gleaning claim' }}</p>
                                    <p class="text-xs text-gray-500">
                                        {{ claim.timestamp|timestamp_to_date if claim.timestamp else 'Unknown date' }}
                                    </p>
                                </div>
                                <span class="px-2 py-1 bg-green-500/20 text-green-400 rounded text-xs">
                                    Approved
                                </span>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
                
                <!-- Recent Community Claims -->
                {% if recent_claims %}
                <div class="glass-card p-6">
                    <h3 class="text-xl font-semibold mb-4">Recent Community Claims</h3>
                    
                    <div class="space-y-3">
                        {% for claim in recent_claims %}
                        <div class="claim-card claim-approved glass-card p-4">
                            <div class="flex justify-between items-start">
                                <div>
                                    <p class="font-semibold">{{ "%.2f"|format(claim.deed_value) }} ONX</p>
                                    <p class="text-sm text-gray-400">Community member</p>
                                    <p class="text-xs text-gray-500">
                                        {{ claim.timestamp|timestamp_to_date if claim.timestamp else 'Unknown date' }}
                                    </p>
                                </div>
                                <span class="px-2 py-1 bg-green-500/20 text-green-400 rounded text-xs">
                                    Approved
                                </span>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
                
                <!-- No Activity Message -->
                {% if not user_claims and not recent_claims %}
                <div class="glass-card p-8 text-center">
                    <div class="text-4xl mb-4">🌾</div>
                    <h3 class="text-xl font-semibold mb-2">No Recent Activity</h3>
                    <p class="text-gray-400 mb-4">
                        The gleaning pool is ready to support community members in need.
                    </p>
                    <p class="text-sm text-gray-500">
                        Claims are reviewed to ensure fair distribution of resources.
                    </p>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Educational Information -->
        <div class="glass-card p-6 mt-8">
            <h3 class="text-xl font-semibold mb-4">About the Gleaning Pool</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h4 class="font-semibold text-cyber-cyan mb-2">Biblical Foundation</h4>
                    <p class="text-sm text-gray-300 mb-4">
                        The gleaning laws in Leviticus required landowners to leave portions of their harvest 
                        for the poor and foreigners. This ensured no one went without basic needs and created 
                        a sustainable support system.
                    </p>
                    
                    <h4 class="font-semibold text-green-400 mb-2">Automatic Funding</h4>
                    <p class="text-sm text-gray-300">
                        The gleaning pool is automatically funded by 2% of all mining rewards, ensuring 
                        consistent resources for community support without requiring manual donations.
                    </p>
                </div>
                
                <div>
                    <h4 class="font-semibold text-yellow-400 mb-2">Fair Distribution</h4>
                    <p class="text-sm text-gray-300 mb-4">
                        Eligibility is based on current token balance, ensuring support goes to those who 
                        need it most. Claims are limited to prevent abuse while providing meaningful assistance.
                    </p>
                    
                    <h4 class="font-semibold text-cyber-purple mb-2">Community Impact</h4>
                    <p class="text-sm text-gray-300">
                        The gleaning pool creates economic security, reduces inequality, and demonstrates 
                        the practical application of biblical principles in modern blockchain economics.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const amountSlider = document.getElementById('amountSlider');
    const selectedAmount = document.getElementById('selectedAmount');
    const claimAmountInput = document.getElementById('claimAmount');
    
    // Update amount display
    if (amountSlider) {
        amountSlider.addEventListener('input', function() {
            const amount = parseInt(this.value);
            selectedAmount.textContent = amount + ' ONX';
            claimAmountInput.value = amount;
        });
    }
    
    // Form submission
    const gleaningForm = document.getElementById('gleaningForm');
    if (gleaningForm) {
        gleaningForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = {
                amount: parseInt(formData.get('amount')),
                justification: formData.get('justification')
            };
            
            const submitButton = document.getElementById('submitClaim');
            
            try {
                submitButton.disabled = true;
                submitButton.innerHTML = '<span class="mr-2">⏳</span>Processing Claim...';
                
                const response = await fetch('/api/tokenomics/gleaning-pool/claim', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    alert(`Gleaning claim successful! You received ${result.amount} ONX.`);
                    window.location.reload();
                } else {
                    alert(`Error: ${result.error || 'Failed to process claim'}`);
                }
            } catch (error) {
                alert(`Error: ${error.message}`);
            } finally {
                submitButton.disabled = false;
                submitButton.innerHTML = '<span class="mr-2">🌾</span>Request Gleaning Support';
            }
        });
    }
});
</script>
{% endblock %}
