/**
 * ONNYX Platform Performance Optimizer
 * Comprehensive performance enhancements for production readiness
 */

class PerformanceOptimizer {
    constructor() {
        this.lazyLoadObserver = null;
        this.intersectionObserver = null;
        this.performanceMetrics = {};
        
        this.init();
    }

    init() {
        console.log('🚀 ONNYX Performance Optimizer initializing...');
        
        // Core Web Vitals monitoring
        this.initCoreWebVitals();
        
        // Lazy loading for images and components
        this.initLazyLoading();
        
        // Intersection observer for animations
        this.initIntersectionObserver();
        
        // Resource optimization
        this.optimizeResources();
        
        // Preload critical resources
        this.preloadCriticalResources();
        
        // Service worker registration
        this.registerServiceWorker();
        
        console.log('✅ Performance Optimizer ready');
    }

    /**
     * Initialize Core Web Vitals monitoring
     */
    initCoreWebVitals() {
        // Largest Contentful Paint (LCP)
        new PerformanceObserver((entryList) => {
            const entries = entryList.getEntries();
            const lastEntry = entries[entries.length - 1];
            this.performanceMetrics.lcp = lastEntry.startTime;
            
            if (lastEntry.startTime > 2500) {
                console.warn('⚠️ LCP is slow:', lastEntry.startTime + 'ms');
            }
        }).observe({ entryTypes: ['largest-contentful-paint'] });

        // First Input Delay (FID)
        new PerformanceObserver((entryList) => {
            const entries = entryList.getEntries();
            entries.forEach(entry => {
                this.performanceMetrics.fid = entry.processingStart - entry.startTime;
                
                if (entry.processingStart - entry.startTime > 100) {
                    console.warn('⚠️ FID is slow:', entry.processingStart - entry.startTime + 'ms');
                }
            });
        }).observe({ entryTypes: ['first-input'] });

        // Cumulative Layout Shift (CLS)
        let clsValue = 0;
        new PerformanceObserver((entryList) => {
            const entries = entryList.getEntries();
            entries.forEach(entry => {
                if (!entry.hadRecentInput) {
                    clsValue += entry.value;
                }
            });
            
            this.performanceMetrics.cls = clsValue;
            
            if (clsValue > 0.1) {
                console.warn('⚠️ CLS is high:', clsValue);
            }
        }).observe({ entryTypes: ['layout-shift'] });
    }

    /**
     * Initialize lazy loading for images and components
     */
    initLazyLoading() {
        // Lazy load images
        const images = document.querySelectorAll('img[data-src]');
        
        if ('IntersectionObserver' in window) {
            this.lazyLoadObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        img.classList.add('loaded');
                        this.lazyLoadObserver.unobserve(img);
                    }
                });
            }, {
                rootMargin: '50px 0px',
                threshold: 0.01
            });

            images.forEach(img => {
                this.lazyLoadObserver.observe(img);
            });
        } else {
            // Fallback for older browsers
            images.forEach(img => {
                img.src = img.dataset.src;
            });
        }

        // Lazy load components
        const lazyComponents = document.querySelectorAll('[data-lazy-component]');
        
        if (lazyComponents.length > 0) {
            const componentObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const component = entry.target;
                        const componentName = component.dataset.lazyComponent;
                        this.loadComponent(componentName, component);
                        componentObserver.unobserve(component);
                    }
                });
            }, {
                rootMargin: '100px 0px'
            });

            lazyComponents.forEach(component => {
                componentObserver.observe(component);
            });
        }
    }

    /**
     * Initialize intersection observer for animations
     */
    initIntersectionObserver() {
        const animatedElements = document.querySelectorAll('[data-animate]');
        
        if (animatedElements.length > 0) {
            this.intersectionObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const element = entry.target;
                        const animation = element.dataset.animate;
                        
                        // Respect reduced motion preference
                        if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
                            element.style.opacity = '1';
                            element.style.transform = 'none';
                        } else {
                            element.classList.add(`animate-${animation}`);
                        }
                        
                        this.intersectionObserver.unobserve(element);
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            });

            animatedElements.forEach(element => {
                this.intersectionObserver.observe(element);
            });
        }
    }

    /**
     * Optimize resources
     */
    optimizeResources() {
        // Optimize images
        const images = document.querySelectorAll('img');
        images.forEach(img => {
            // Add loading="lazy" if not already present
            if (!img.hasAttribute('loading')) {
                img.loading = 'lazy';
            }
            
            // Add decoding="async" for better performance
            if (!img.hasAttribute('decoding')) {
                img.decoding = 'async';
            }
        });

        // Optimize fonts
        const fontLinks = document.querySelectorAll('link[href*="fonts"]');
        fontLinks.forEach(link => {
            if (!link.hasAttribute('rel') || link.rel !== 'preload') {
                link.rel = 'preload';
                link.as = 'font';
                link.crossOrigin = 'anonymous';
            }
        });

        // Optimize CSS delivery
        this.optimizeCSSDelivery();
    }

    /**
     * Optimize CSS delivery
     */
    optimizeCSSDelivery() {
        // Load non-critical CSS asynchronously
        const nonCriticalCSS = document.querySelectorAll('link[data-critical="false"]');
        
        nonCriticalCSS.forEach(link => {
            const newLink = document.createElement('link');
            newLink.rel = 'stylesheet';
            newLink.href = link.href;
            newLink.media = 'print';
            newLink.onload = function() {
                this.media = 'all';
            };
            
            document.head.appendChild(newLink);
            link.remove();
        });
    }

    /**
     * Preload critical resources
     */
    preloadCriticalResources() {
        const criticalResources = [
            { href: '/static/css/main.css', as: 'style' },
            { href: '/static/js/main.js', as: 'script' },
            { href: '/static/images/onnyx_logo.png', as: 'image' }
        ];

        criticalResources.forEach(resource => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.href = resource.href;
            link.as = resource.as;
            
            if (resource.as === 'font') {
                link.crossOrigin = 'anonymous';
            }
            
            document.head.appendChild(link);
        });
    }

    /**
     * Register service worker for caching
     */
    async registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            try {
                const registration = await navigator.serviceWorker.register('/static/js/sw.js');
                console.log('✅ Service Worker registered:', registration);
            } catch (error) {
                console.log('❌ Service Worker registration failed:', error);
            }
        }
    }

    /**
     * Load component dynamically
     */
    async loadComponent(componentName, container) {
        try {
            const module = await import(`/static/js/components/${componentName}.js`);
            const Component = module.default;
            new Component(container);
        } catch (error) {
            console.warn(`Failed to load component: ${componentName}`, error);
        }
    }

    /**
     * Get performance metrics
     */
    getPerformanceMetrics() {
        return {
            ...this.performanceMetrics,
            navigation: performance.getEntriesByType('navigation')[0],
            resources: performance.getEntriesByType('resource'),
            memory: performance.memory ? {
                usedJSHeapSize: performance.memory.usedJSHeapSize,
                totalJSHeapSize: performance.memory.totalJSHeapSize,
                jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
            } : null
        };
    }

    /**
     * Report performance metrics
     */
    reportPerformanceMetrics() {
        const metrics = this.getPerformanceMetrics();
        
        console.log('📊 Performance Metrics:', {
            LCP: metrics.lcp ? `${metrics.lcp.toFixed(2)}ms` : 'Not measured',
            FID: metrics.fid ? `${metrics.fid.toFixed(2)}ms` : 'Not measured',
            CLS: metrics.cls ? metrics.cls.toFixed(4) : 'Not measured',
            LoadTime: metrics.navigation ? `${metrics.navigation.loadEventEnd.toFixed(2)}ms` : 'Not measured'
        });

        // Send metrics to analytics (if configured)
        if (window.gtag) {
            window.gtag('event', 'performance_metrics', {
                lcp: metrics.lcp,
                fid: metrics.fid,
                cls: metrics.cls,
                load_time: metrics.navigation?.loadEventEnd
            });
        }
    }

    /**
     * Cleanup observers
     */
    destroy() {
        if (this.lazyLoadObserver) {
            this.lazyLoadObserver.disconnect();
        }
        
        if (this.intersectionObserver) {
            this.intersectionObserver.disconnect();
        }
    }
}

// Initialize performance optimizer when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.PerformanceOptimizer = new PerformanceOptimizer();
    
    // Report metrics after page load
    window.addEventListener('load', () => {
        setTimeout(() => {
            window.PerformanceOptimizer.reportPerformanceMetrics();
        }, 1000);
    });
});

// Export for manual access
window.PerformanceOptimizer = PerformanceOptimizer;
