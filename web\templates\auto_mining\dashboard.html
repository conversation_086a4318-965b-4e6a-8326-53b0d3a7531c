{% extends "base.html" %}

{% block title %}Auto-Mining Dashboard - Onnyx Platform{% endblock %}

{% block head %}
<style>
    .mining-status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 8px;
    }

    .mining-active {
        background: #22c55e;
        box-shadow: 0 0 10px rgba(34, 197, 94, 0.5);
        animation: pulse 2s infinite;
    }

    .mining-inactive {
        background: #6b7280;
    }

    .mining-scheduled {
        background: #f59e0b;
        box-shadow: 0 0 10px rgba(245, 158, 11, 0.5);
    }

    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
    }

    .stats-card {
        background: var(--glass-bg);
        backdrop-filter: blur(12px);
        -webkit-backdrop-filter: blur(12px);
        border: 1px solid var(--glass-border);
        border-radius: 16px;
        padding: 2rem;
        transition: all 0.3s ease;
    }

    .stats-card:hover {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(0, 212, 255, 0.3);
        box-shadow: 0 0 20px rgba(0, 212, 255, 0.2);
    }

    .validator-card {
        background: var(--glass-bg);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid var(--glass-border);
        border-radius: 12px;
        padding: 1.5rem;
        transition: all 0.3s ease;
    }

    .validator-card:hover {
        background: rgba(255, 255, 255, 0.1);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    }

    .toggle-switch {
        position: relative;
        display: inline-block;
        width: 60px;
        height: 34px;
    }

    .toggle-switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #6b7280;
        transition: 0.4s;
        border-radius: 34px;
    }

    .slider:before {
        position: absolute;
        content: "";
        height: 26px;
        width: 26px;
        left: 4px;
        bottom: 4px;
        background-color: white;
        transition: 0.4s;
        border-radius: 50%;
    }

    input:checked + .slider {
        background-color: var(--cyber-cyan);
        box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
    }

    input:checked + .slider:before {
        transform: translateX(26px);
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-onyx-black via-onyx-dark to-onyx-black">
    <!-- Enhanced Header Section -->
    <section class="pt-24 pb-16">
        <div class="container-xl mx-auto px-6">
            <div class="text-center mb-16">
                <div class="flex justify-center mb-8">
                    <div class="glass-card-premium w-24 h-24 rounded-3xl flex items-center justify-center">
                        <svg class="w-12 h-12 text-cyber-cyan" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                </div>
                <h1 class="text-5xl md:text-6xl font-orbitron font-bold text-cyber-cyan mb-6">
                    Auto-Mining Control
                </h1>
                <p class="text-xl text-text-secondary max-w-3xl mx-auto leading-relaxed">
                    Advanced automated mining with biblical compliance and intelligent scheduling
                </p>
            </div>
        </div>
    </section>

    <!-- Enhanced Biblical Compliance Status -->
    <section class="pb-16">
        <div class="container-xl mx-auto px-6">
            <div class="glass-card-premium p-8 mb-16">
                <div class="flex items-center justify-between mb-8">
                    <div>
                        <h2 class="text-3xl font-orbitron font-bold text-cyber-purple mb-2">📜 24/6 Mining Covenant Status</h2>
                        <p class="text-text-secondary">Enhanced biblical compliance with annual emission caps</p>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 rounded-full {{ 'bg-cyber-green animate-pulse' if mining_allowed else 'bg-cyber-red animate-pulse' }}"></div>
                        <span class="text-sm font-orbitron {{ 'text-cyber-green' if mining_allowed else 'text-cyber-red' }}">
                            {{ 'MINING ALLOWED' if not biblical_status.is_sabbath_period else 'SABBATH RESTRICTION' }}
                        </span>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <!-- Sabbath Status -->
                    <div class="glass-card-enhanced p-6 text-center">
                        <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-cyber-purple to-cyber-blue rounded-2xl flex items-center justify-center">
                            {% if biblical_status.is_sabbath_period %}
                            <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                            </svg>
                            {% else %}
                            <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                            </svg>
                            {% endif %}
                        </div>
                        <div class="text-lg font-orbitron font-bold mb-2 {{ 'text-cyber-purple' if biblical_status.is_sabbath_period else 'text-cyber-green' }}">
                            {{ 'Sabbath Active' if biblical_status.is_sabbath_period else 'Work Period' }}
                        </div>
                        <div class="text-sm text-text-secondary">
                            {{ 'Mining Restricted' if biblical_status.is_sabbath_period else 'Next: ' + biblical_status.next_sabbath }}
                        </div>
                    </div>

                    <!-- Yovel Cycle -->
                    <div class="glass-card-enhanced p-6 text-center">
                        <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-cyber-cyan to-cyber-blue rounded-2xl flex items-center justify-center">
                            <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="text-2xl font-orbitron font-bold text-cyber-cyan mb-2">Cycle {{ biblical_status.current_yovel_cycle }}</div>
                        <div class="text-sm text-text-secondary">Yovel ({{ biblical_status.yovel_cycle_years }} Year)</div>
                        <div class="text-xs text-text-tertiary mt-2">{{ biblical_status.years_until_reset }} years until reset</div>
                    </div>

                    <!-- Compliance Score -->
                    <div class="glass-card-enhanced p-6 text-center">
                        <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-cyber-green to-green-400 rounded-2xl flex items-center justify-center">
                            <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="text-2xl font-orbitron font-bold text-cyber-green mb-2">{{ biblical_status.compliance_score }}%</div>
                        <div class="text-sm text-text-secondary">Compliance Score</div>
                        <div class="text-xs {{ 'text-cyber-green' if biblical_status.compliance_score >= 80 else 'text-cyber-yellow' if biblical_status.compliance_score >= 60 else 'text-cyber-red' }}">
                            {{ 'Excellent' if biblical_status.compliance_score >= 80 else 'Good' if biblical_status.compliance_score >= 60 else 'Needs Improvement' }}
                        </div>
                    </div>

                    <!-- Auto-Compliance -->
                    <div class="glass-card-enhanced p-6 text-center">
                        <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-cyber-yellow to-cyber-orange rounded-2xl flex items-center justify-center">
                            <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                            </svg>
                        </div>
                        <div class="text-lg font-orbitron font-bold text-cyber-yellow mb-2">Auto-Compliance</div>
                        <div class="text-sm text-text-secondary">{{ 'Enabled' if biblical_status.auto_compliance else 'Disabled' }}</div>
                        <div class="text-xs text-text-tertiary mt-2">{{ biblical_status.compliant_validators }} validators compliant</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Dashboard Content -->
    <section class="pb-32">
        <div class="container-xl mx-auto px-6">
            <div class="max-w-7xl mx-auto space-y-16">
                <!-- Enhanced System Controls -->
                <div class="glass-card-premium p-8">
                    <div class="flex items-center justify-between mb-8">
                        <div>
                            <h2 class="text-3xl font-orbitron font-bold text-cyber-cyan mb-2">🤖 Mining Control Center</h2>
                            <p class="text-text-secondary">Intelligent automated mining management</p>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 rounded-full {{ 'bg-cyber-green animate-pulse' if summary_stats.system_running else 'bg-cyber-red' }}"></div>
                            <span class="text-sm font-orbitron {{ 'text-cyber-green' if summary_stats.system_running else 'text-cyber-red' }}">
                                {{ 'SYSTEM ACTIVE' if summary_stats.system_running else 'SYSTEM INACTIVE' }}
                            </span>
                        </div>
                    </div>

                    <!-- System Status Grid -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <div class="glass-card-enhanced p-6 text-center">
                            <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-cyber-cyan to-cyber-blue rounded-2xl flex items-center justify-center">
                                <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                            </div>
                            <div class="text-3xl font-orbitron font-bold text-cyber-cyan mb-2">{{ summary_stats.total_validators }}</div>
                            <div class="text-sm text-text-secondary">Total Validators</div>
                        </div>

                        <div class="glass-card-enhanced p-6 text-center">
                            <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-cyber-green to-green-400 rounded-2xl flex items-center justify-center">
                                <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                            </div>
                            <div class="text-3xl font-orbitron font-bold text-cyber-green mb-2">{{ summary_stats.active_miners }}</div>
                            <div class="text-sm text-text-secondary">Active Miners</div>
                        </div>

                        <div class="glass-card-enhanced p-6 text-center">
                            <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-cyber-purple to-cyber-blue rounded-2xl flex items-center justify-center">
                                <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                </svg>
                            </div>
                            <div class="text-3xl font-orbitron font-bold text-cyber-purple mb-2">{{ "%.2f"|format(summary_stats.total_earnings) }}</div>
                            <div class="text-sm text-text-secondary">Total Earnings (ONX)</div>
                        </div>

                        <div class="glass-card-enhanced p-6 text-center">
                            <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-cyber-yellow to-cyber-orange rounded-2xl flex items-center justify-center">
                                <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                </svg>
                            </div>
                            <div class="text-3xl font-orbitron font-bold text-cyber-yellow mb-2">{{ summary_stats.total_blocks }}</div>
                            <div class="text-sm text-text-secondary">Blocks Mined</div>
                        </div>
                    </div>

                    <!-- Control Buttons -->
                    <div class="flex flex-wrap gap-4 justify-center">
                        {% if summary_stats.system_running %}
                        <button onclick="stopAutoMining()"
                                class="glass-button px-8 py-4 rounded-xl font-orbitron font-bold transition-all duration-300 hover:scale-105"
                                style="background: rgba(239, 68, 68, 0.2); border-color: rgba(239, 68, 68, 0.3); color: #ef4444;">
                            ⏹️ Stop System
                        </button>
                        {% else %}
                        <button onclick="startAutoMining()"
                                class="glass-button-primary px-8 py-4 rounded-xl font-orbitron font-bold transition-all duration-300 hover:scale-105">
                            🚀 Start System
                        </button>
                        {% endif %}

                        <a href="{{ url_for('auto_mining.performance') }}"
                           class="glass-button-enhanced px-8 py-4 rounded-xl font-orbitron font-bold transition-all duration-300 hover:scale-105">
                            📊 Performance Analytics
                        </a>

                        <a href="{{ url_for('auto_mining.settings') }}"
                           class="glass-button px-8 py-4 rounded-xl font-orbitron font-bold transition-all duration-300 hover:scale-105">
                            ⚙️ Advanced Settings
                        </a>

                        <button onclick="emergencyStop()"
                                class="glass-button px-8 py-4 rounded-xl font-orbitron font-bold transition-all duration-300 hover:scale-105"
                                style="background: rgba(239, 68, 68, 0.1); border-color: rgba(239, 68, 68, 0.2); color: #ef4444;">
                            🚨 Emergency Stop
                        </button>
                    </div>

                    <!-- Real-time Status -->
                    <div class="mt-8 p-6 glass-card rounded-xl">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <div class="mining-status-indicator {% if summary_stats.system_running %}mining-active{% else %}mining-inactive{% endif %}"></div>
                                <div>
                                    <h4 class="text-lg font-orbitron font-bold text-white">System Status</h4>
                                    <p class="text-text-tertiary">
                                        {% if summary_stats.system_running %}
                                        Monitoring {{ summary_stats.total_validators }} validators • {{ summary_stats.active_miners }} actively mining
                                        {% else %}
                                        System stopped • All mining operations halted
                                        {% endif %}
                                    </p>
                                </div>
                            </div>

                            <div class="text-right">
                                <div class="text-sm text-text-tertiary">Last Updated</div>
                                <div class="text-cyber-cyan font-mono" id="last-updated">
                                    {{ moment().format('HH:mm:ss') }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

    <!-- Summary Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="stats-card text-center">
            <div class="text-3xl font-orbitron font-bold text-cyber-cyan mb-2">
                {{ summary_stats.total_validators }}
            </div>
            <div class="text-sm text-text-tertiary uppercase tracking-wider">Total Validators</div>
        </div>

        <div class="stats-card text-center">
            <div class="text-3xl font-orbitron font-bold text-green-400 mb-2">
                {{ summary_stats.active_miners }}
            </div>
            <div class="text-sm text-text-tertiary uppercase tracking-wider">Active Miners</div>
        </div>

        <div class="stats-card text-center">
            <div class="text-3xl font-orbitron font-bold text-cyber-purple mb-2">
                {{ "%.2f"|format(summary_stats.total_earnings) }}
            </div>
            <div class="text-sm text-text-tertiary uppercase tracking-wider">Total Earnings</div>
        </div>

        <div class="stats-card text-center">
            <div class="text-3xl font-orbitron font-bold text-cyber-blue mb-2">
                {{ summary_stats.total_blocks }}
            </div>
            <div class="text-sm text-text-tertiary uppercase tracking-wider">Blocks Mined</div>
        </div>
    </div>

    <!-- System Status -->
    <div class="glass-card p-6 mb-8">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="mining-status-indicator {% if summary_stats.system_running %}mining-active{% else %}mining-inactive{% endif %}"></div>
                <div>
                    <h3 class="text-xl font-orbitron font-bold text-white">
                        Auto-Mining System
                    </h3>
                    <p class="text-text-tertiary">
                        {% if summary_stats.system_running %}
                        System is running and monitoring validators
                        {% else %}
                        System is stopped
                        {% endif %}
                    </p>
                </div>
            </div>

            <div class="text-right">
                <div class="text-sm text-text-tertiary">Last Updated</div>
                <div class="text-cyber-cyan font-mono" id="last-updated">
                    {{ moment().format('HH:mm:ss') }}
                </div>
            </div>
        </div>
    </div>

    <!-- Validators List -->
    <div class="glass-card p-6">
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-2xl font-orbitron font-bold text-cyber-cyan">
                Validator Auto-Mining Status
            </h2>
            <button onclick="refreshStatus()"
                    class="glass-button px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300">
                🔄 Refresh
            </button>
        </div>

        {% if validators %}
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {% for validator in validators %}
            <div class="validator-card">
                <div class="flex items-start justify-between mb-4">
                    <div class="flex-1">
                        <div class="flex items-center space-x-3 mb-2">
                            <div class="mining-status-indicator
                                {% if validator.currently_mining %}mining-active
                                {% elif validator.auto_mining_enabled and not validator.in_mining_hours %}mining-scheduled
                                {% else %}mining-inactive{% endif %}">
                            </div>
                            <h3 class="text-lg font-orbitron font-bold text-white">
                                {{ validator.name }}
                            </h3>
                        </div>
                        <p class="text-sm text-text-tertiary uppercase tracking-wider">
                            {{ validator.category }} • {{ validator.mining_tier|title }} Tier
                        </p>
                    </div>

                    <!-- Auto-Mining Toggle -->
                    <label class="toggle-switch">
                        <input type="checkbox"
                               {% if validator.auto_mining_enabled %}checked{% endif %}
                               onchange="toggleValidator('{{ validator.sela_id }}')">
                        <span class="slider"></span>
                    </label>
                </div>

                <!-- Validator Stats -->
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <div class="text-sm text-text-tertiary">Mining Power</div>
                        <div class="text-cyber-cyan font-mono">{{ validator.mining_power }}x</div>
                    </div>
                    <div>
                        <div class="text-sm text-text-tertiary">Blocks Mined</div>
                        <div class="text-green-400 font-mono">{{ validator.blocks_mined or 0 }}</div>
                    </div>
                    <div>
                        <div class="text-sm text-text-tertiary">Earnings</div>
                        <div class="text-cyber-purple font-mono">{{ "%.2f"|format(validator.mining_rewards_earned or 0) }}</div>
                    </div>
                    <div>
                        <div class="text-sm text-text-tertiary">Uptime</div>
                        <div class="text-cyber-blue font-mono">{{ validator.uptime }}</div>
                    </div>
                </div>

                <!-- Status Information -->
                <div class="space-y-2 mb-4">
                    {% if validator.currently_mining %}
                    <div class="flex items-center space-x-2">
                        <span class="text-green-400">●</span>
                        <span class="text-sm text-green-400">Currently Mining</span>
                        {% if validator.process_id %}
                        <span class="text-xs text-text-tertiary">(PID: {{ validator.process_id }})</span>
                        {% endif %}
                    </div>
                    {% elif validator.auto_mining_enabled %}
                        {% if validator.in_mining_hours %}
                        <div class="flex items-center space-x-2">
                            <span class="text-yellow-400">●</span>
                            <span class="text-sm text-yellow-400">Starting Soon</span>
                        </div>
                        {% else %}
                        <div class="flex items-center space-x-2">
                            <span class="text-blue-400">●</span>
                            <span class="text-sm text-blue-400">Scheduled (Outside Hours)</span>
                        </div>
                        {% endif %}
                    {% else %}
                    <div class="flex items-center space-x-2">
                        <span class="text-gray-400">●</span>
                        <span class="text-sm text-gray-400">Auto-Mining Disabled</span>
                    </div>
                    {% endif %}

                    {% if validator.restart_attempts > 0 %}
                    <div class="flex items-center space-x-2">
                        <span class="text-orange-400">⚠️</span>
                        <span class="text-sm text-orange-400">{{ validator.restart_attempts }} restart(s)</span>
                    </div>
                    {% endif %}

                    {% if validator.schedule_enabled %}
                    <div class="flex items-center space-x-2">
                        <span class="text-purple-400">⏰</span>
                        <span class="text-sm text-purple-400">Scheduled Mining</span>
                    </div>
                    {% endif %}
                </div>

                <!-- Action Buttons -->
                <div class="flex space-x-2">
                    <a href="{{ url_for('auto_mining.configure_validator', sela_id=validator.sela_id) }}"
                       class="flex-1 glass-button px-3 py-2 rounded-lg text-sm font-medium text-center transition-all duration-300">
                        ⚙️ Configure
                    </a>
                    <a href="{{ url_for('sela.profile', sela_id=validator.sela_id) }}"
                       class="flex-1 glass-button px-3 py-2 rounded-lg text-sm font-medium text-center transition-all duration-300">
                        📊 Details
                    </a>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="text-center py-12">
            <div class="text-6xl mb-4">🤖</div>
            <h3 class="text-xl font-orbitron font-bold text-text-secondary mb-2">
                No Validators Found
            </h3>
            <p class="text-text-tertiary mb-6">
                Register a validator to start auto-mining
            </p>
            <a href="{{ url_for('auth.register_sela') }}"
               class="glass-button-primary px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300">
                🏢 Register Validator
            </a>
        </div>
        {% endif %}
    </div>
</div>

<script>
// Auto-refresh status every 30 seconds
setInterval(refreshStatus, 30000);

function refreshStatus() {
    fetch('/auto-mining/api/status')
        .then(response => response.json())
        .then(data => {
            // Update last updated time
            document.getElementById('last-updated').textContent = new Date().toLocaleTimeString();

            // Optionally reload the page for full update
            // location.reload();
        })
        .catch(error => {
            console.error('Error refreshing status:', error);
        });
}

function startAutoMining() {
    if (confirm('Start the auto-mining system?')) {
        fetch('/auto-mining/api/start', { method: 'POST' })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Failed to start auto-mining: ' + data.error);
                }
            })
            .catch(error => {
                alert('Error starting auto-mining: ' + error);
            });
    }
}

function stopAutoMining() {
    if (confirm('Stop the auto-mining system? This will stop all active miners.')) {
        fetch('/auto-mining/api/stop', { method: 'POST' })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Failed to stop auto-mining: ' + data.error);
                }
            })
            .catch(error => {
                alert('Error stopping auto-mining: ' + error);
            });
    }
}

function toggleValidator(selaId) {
    fetch(`/auto-mining/api/toggle/${selaId}`, { method: 'POST' })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Optionally show a success message
                console.log('Validator toggled:', data.message);
                // Refresh after a short delay to see changes
                setTimeout(() => location.reload(), 1000);
            } else {
                alert('Failed to toggle validator: ' + data.error);
                // Revert the toggle
                location.reload();
            }
        })
        .catch(error => {
            alert('Error toggling validator: ' + error);
            location.reload();
        });
}
</script>
{% endblock %}
