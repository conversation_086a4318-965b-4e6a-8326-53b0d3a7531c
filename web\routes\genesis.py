"""
ONNYX Genesis Management Routes
Handles genesis block verification, tribal elder onboarding, and voice scroll deployment
"""

from flask import Blueprint, render_template, request, jsonify, session
import json
import time
import uuid
import hashlib
from datetime import datetime

from shared.db.db import db

genesis_bp = Blueprint('genesis', __name__, url_prefix='/genesis')

@genesis_bp.route('/')
def genesis_dashboard():
    """Genesis management dashboard."""
    return render_template('genesis/dashboard.html')

@genesis_bp.route('/api/status')
def genesis_status():
    """Get current genesis and council status."""
    try:
        # Check genesis block
        genesis_block = db.query_one("SELECT * FROM blocks WHERE block_height = 0")
        genesis_exists = genesis_block is not None
        
        # Check tribal elders
        elders = db.query("SELECT COUNT(*) as count FROM identities WHERE role_class = 'Tribal_Elder'")
        elder_count = elders[0]['count'] if elders else 0
        
        # Check voice scrolls
        try:
            scrolls = db.query("SELECT COUNT(*) as count FROM voice_scrolls WHERE status = 'proposed'")
            scroll_count = scrolls[0]['count'] if scrolls else 0
        except:
            scroll_count = 0
        
        # Check nations
        try:
            nations = db.query("SELECT COUNT(*) as count FROM biblical_nations")
            nation_count = nations[0]['count'] if nations else 0
        except:
            nation_count = 0
        
        status = {
            "genesis_block": {
                "exists": genesis_exists,
                "hash": genesis_block['block_hash'][:16] + "..." if genesis_block else None,
                "timestamp": genesis_block['timestamp'] if genesis_block else None,
                "miner": genesis_block['miner'] if genesis_block else None
            },
            "tribal_council": {
                "elders_registered": elder_count,
                "target_elders": 12,
                "formation_complete": elder_count >= 12
            },
            "voice_scrolls": {
                "deployed": scroll_count,
                "target_scrolls": 3
            },
            "nations": {
                "registered": nation_count,
                "target_nations": 47
            },
            "next_step": get_next_step(genesis_exists, elder_count, scroll_count, nation_count)
        }
        
        return jsonify(status)
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

def get_next_step(genesis_exists, elder_count, scroll_count, nation_count):
    """Determine the next step in the genesis process."""
    if not genesis_exists:
        return "Create Genesis Block"
    elif nation_count < 47:
        return "Register 47 Nations"
    elif elder_count < 12:
        return "Onboard Tribal Elders"
    elif scroll_count < 3:
        return "Deploy Genesis Voice Scrolls"
    else:
        return "Begin Governance Voting"

@genesis_bp.route('/api/create-elder', methods=['POST'])
def create_tribal_elder():
    """Create a new tribal elder identity."""
    try:
        data = request.get_json()
        tribe_code = data.get('tribe_code')
        elder_name = data.get('elder_name')
        
        if not tribe_code or not elder_name:
            return jsonify({"error": "Missing tribe_code or elder_name"}), 400
        
        # Tribe information
        tribes = {
            "JU": {"name": "Judah", "voting_weight": 2, "role": "Royal"},
            "LE": {"name": "Levi", "voting_weight": 2, "role": "Priestly"},
            "EP": {"name": "Ephraim", "voting_weight": 2, "role": "Fruitful"},
            "BE": {"name": "Benjamin", "voting_weight": 1, "role": "Standard"},
            "SI": {"name": "Simeon", "voting_weight": 1, "role": "Standard"},
            "MA": {"name": "Manasseh", "voting_weight": 1, "role": "Standard"},
            "IS": {"name": "Issachar", "voting_weight": 1, "role": "Standard"},
            "ZE": {"name": "Zebulun", "voting_weight": 1, "role": "Standard"},
            "NA": {"name": "Naphtali", "voting_weight": 1, "role": "Standard"},
            "GA": {"name": "Gad", "voting_weight": 1, "role": "Standard"},
            "AS": {"name": "Asher", "voting_weight": 1, "role": "Standard"},
            "RE": {"name": "Reuben", "voting_weight": 1, "role": "Standard"}
        }
        
        if tribe_code not in tribes:
            return jsonify({"error": "Invalid tribe code"}), 400
        
        tribe_info = tribes[tribe_code]
        
        # Generate identity
        identity_id = str(uuid.uuid4())
        public_key = hashlib.sha256(f"{identity_id}:{tribe_code}".encode()).hexdigest()
        
        # Create metadata
        metadata = {
            "tribe_code": tribe_code,
            "tribe_name": tribe_info["name"],
            "tribal_role": "Elder",
            "council_member": True,
            "voting_weight": tribe_info["voting_weight"],
            "tribal_authority": tribe_info["role"],
            "badges": ["Tribal_Elder", "Council_Member", f"{tribe_info['name']}_Elder"],
            "reputation": 1000,
            "covenant_comprehension_score": 5,
            "onboarding_completed": True
        }
        
        current_time = int(time.time())
        
        # Insert into identities table
        db.execute("""
            INSERT INTO identities (
                identity_id, name, email, public_key, nation_id, metadata,
                status, created_at, updated_at, nation_of_origin, role_class,
                etzem_score, verification_level, covenant_accepted, vault_status,
                nation_code, nation_name
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            identity_id,
            elder_name,
            f"{tribe_code.lower()}.<EMAIL>",
            public_key,
            tribe_code,
            json.dumps(metadata),
            "active",
            current_time,
            current_time,
            tribe_code,
            "Tribal_Elder",
            1000,
            1,
            True,
            "Active",
            tribe_code,
            tribe_info["name"]
        ))
        
        return jsonify({
            "success": True,
            "identity_id": identity_id,
            "tribe": tribe_info["name"],
            "voting_weight": tribe_info["voting_weight"],
            "role": tribe_info["role"]
        })
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@genesis_bp.route('/api/deploy-scrolls', methods=['POST'])
def deploy_genesis_scrolls():
    """Deploy the three Genesis Voice Scrolls."""
    try:
        # Load genesis scrolls configuration
        with open('data/genesis_voice_scrolls.json', 'r', encoding='utf-8') as f:
            scrolls_config = json.load(f)
        
        deployed_count = 0
        
        for scroll in scrolls_config['scrolls']:
            try:
                # Check if scroll already exists
                existing = db.query_one("SELECT scroll_id FROM voice_scrolls WHERE scroll_id = ?", (scroll['scroll_id'],))
                
                if not existing:
                    db.execute("""
                        INSERT INTO voice_scrolls (
                            scroll_id, title, type, proposed_by, content,
                            voting_requirements, status, created_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        scroll['scroll_id'],
                        scroll['title'],
                        scroll['type'],
                        scroll['proposed_by'],
                        json.dumps(scroll['content']),
                        json.dumps(scroll['voting_requirements']),
                        scroll['status'],
                        scroll['proposed_at']
                    ))
                    deployed_count += 1
                    
            except Exception as e:
                print(f"Error deploying scroll {scroll['scroll_id']}: {e}")
        
        return jsonify({
            "success": True,
            "deployed_count": deployed_count,
            "total_scrolls": len(scrolls_config['scrolls'])
        })
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@genesis_bp.route('/api/elders')
def list_tribal_elders():
    """List all registered tribal elders."""
    try:
        elders = db.query("""
            SELECT identity_id, name, nation_code, nation_name, 
                   verification_level, covenant_accepted, created_at
            FROM identities 
            WHERE role_class = 'Tribal_Elder'
            ORDER BY nation_code
        """)
        
        elder_list = []
        for elder in elders:
            elder_list.append({
                "identity_id": elder['identity_id'],
                "name": elder['name'],
                "tribe_code": elder['nation_code'],
                "tribe_name": elder['nation_name'],
                "verification_level": elder['verification_level'],
                "covenant_accepted": elder['covenant_accepted'],
                "created_at": elder['created_at']
            })
        
        return jsonify({
            "elders": elder_list,
            "total_count": len(elder_list)
        })
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@genesis_bp.route('/api/scrolls')
def list_voice_scrolls():
    """List all deployed voice scrolls."""
    try:
        scrolls = db.query("""
            SELECT scroll_id, title, type, proposed_by, status, created_at
            FROM voice_scrolls
            ORDER BY created_at DESC
        """)
        
        scroll_list = []
        for scroll in scrolls:
            scroll_list.append({
                "scroll_id": scroll['scroll_id'],
                "title": scroll['title'],
                "type": scroll['type'],
                "proposed_by": scroll['proposed_by'],
                "status": scroll['status'],
                "created_at": scroll['created_at']
            })
        
        return jsonify({
            "scrolls": scroll_list,
            "total_count": len(scroll_list)
        })
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500
