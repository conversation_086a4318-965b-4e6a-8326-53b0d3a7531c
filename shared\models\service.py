"""
Onnyx Service Model

This module defines the Service model for the Onnyx blockchain.
"""

import json
import time
import logging
import sqlite3
from typing import Dict, Any, List, Optional, Union

from shared.config.config import onnyx_config
from shared.models.base_model import BaseModel

# Set up logging
logger = logging.getLogger("onnyx.models.service")

class Service(BaseModel):
    """
    Service model for the Onnyx blockchain.
    
    A Service represents a service provided by a Sela in the Onnyx ecosystem.
    """
    
    TABLE_NAME = "services"
    PRIMARY_KEY = "service_id"
    
    def __init__(
        self,
        service_id: str,
        sela_id: str,
        service_type: str,
        description: str,
        timestamp: int,
        recipient_id: Optional[str] = None,
        duration: Optional[int] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize a Service.
        
        Args:
            service_id: The service ID
            sela_id: The Sela ID
            service_type: The type of service
            description: A description of the service
            timestamp: The timestamp when the service was provided
            recipient_id: The recipient's identity ID (optional)
            duration: The duration of the service in minutes (optional)
            metadata: Additional metadata for the service (optional)
        """
        self.service_id = service_id
        self.sela_id = sela_id
        self.service_type = service_type
        self.description = description
        self.timestamp = timestamp
        self.recipient_id = recipient_id
        self.duration = duration
        self.metadata = metadata or {}
    
    @classmethod
    def create_table(cls) -> None:
        """Create the Service table if it doesn't exist."""
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()
        
        cursor.execute(f"""
        CREATE TABLE IF NOT EXISTS {cls.TABLE_NAME} (
            {cls.PRIMARY_KEY} TEXT PRIMARY KEY,
            sela_id TEXT NOT NULL,
            service_type TEXT NOT NULL,
            description TEXT NOT NULL,
            timestamp INTEGER NOT NULL,
            recipient_id TEXT,
            duration INTEGER,
            metadata TEXT
        )
        """)
        
        conn.commit()
        conn.close()
    
    @classmethod
    def create(
        cls,
        service_id: str,
        sela_id: str,
        service_type: str,
        description: str,
        timestamp: Optional[int] = None,
        recipient_id: Optional[str] = None,
        duration: Optional[int] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> "Service":
        """
        Create a new Service.
        
        Args:
            service_id: The service ID
            sela_id: The Sela ID
            service_type: The type of service
            description: A description of the service
            timestamp: The timestamp when the service was provided (optional)
            recipient_id: The recipient's identity ID (optional)
            duration: The duration of the service in minutes (optional)
            metadata: Additional metadata for the service (optional)
        
        Returns:
            The created Service
        """
        # Create the table if it doesn't exist
        cls.create_table()
        
        # Create the Service
        service = cls(
            service_id=service_id,
            sela_id=sela_id,
            service_type=service_type,
            description=description,
            timestamp=timestamp or int(time.time()),
            recipient_id=recipient_id,
            duration=duration,
            metadata=metadata
        )
        
        # Save the Service to the database
        service.save()
        
        return service
    
    def save(self) -> None:
        """Save the Service to the database."""
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()
        
        cursor.execute(f"""
        INSERT OR REPLACE INTO {self.TABLE_NAME} (
            {self.PRIMARY_KEY},
            sela_id,
            service_type,
            description,
            timestamp,
            recipient_id,
            duration,
            metadata
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            self.service_id,
            self.sela_id,
            self.service_type,
            self.description,
            self.timestamp,
            self.recipient_id,
            self.duration,
            json.dumps(self.metadata)
        ))
        
        conn.commit()
        conn.close()
    
    @classmethod
    def get_by_id(cls, service_id: str) -> Optional["Service"]:
        """
        Get a Service by ID.
        
        Args:
            service_id: The service ID
        
        Returns:
            The Service, or None if not found
        """
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()
        
        cursor.execute(f"""
        SELECT
            {cls.PRIMARY_KEY},
            sela_id,
            service_type,
            description,
            timestamp,
            recipient_id,
            duration,
            metadata
        FROM {cls.TABLE_NAME}
        WHERE {cls.PRIMARY_KEY} = ?
        """, (service_id,))
        
        row = cursor.fetchone()
        conn.close()
        
        if row:
            return cls(
                service_id=row[0],
                sela_id=row[1],
                service_type=row[2],
                description=row[3],
                timestamp=row[4],
                recipient_id=row[5],
                duration=row[6],
                metadata=json.loads(row[7]) if row[7] else {}
            )
        
        return None
    
    @classmethod
    def get_all(cls, limit: int = 100, offset: int = 0) -> List["Service"]:
        """
        Get all Services.
        
        Args:
            limit: The maximum number of Services to return
            offset: The offset for pagination
        
        Returns:
            A list of all Services
        """
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()
        
        cursor.execute(f"""
        SELECT
            {cls.PRIMARY_KEY},
            sela_id,
            service_type,
            description,
            timestamp,
            recipient_id,
            duration,
            metadata
        FROM {cls.TABLE_NAME}
        ORDER BY timestamp DESC
        LIMIT ? OFFSET ?
        """, (limit, offset))
        
        rows = cursor.fetchall()
        conn.close()
        
        return [
            cls(
                service_id=row[0],
                sela_id=row[1],
                service_type=row[2],
                description=row[3],
                timestamp=row[4],
                recipient_id=row[5],
                duration=row[6],
                metadata=json.loads(row[7]) if row[7] else {}
            )
            for row in rows
        ]
    
    @classmethod
    def find_by_sela(cls, sela_id: str, limit: int = 100) -> List["Service"]:
        """
        Find Services by Sela ID.
        
        Args:
            sela_id: The Sela ID
            limit: The maximum number of Services to return
        
        Returns:
            A list of Services for the Sela
        """
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()
        
        cursor.execute(f"""
        SELECT
            {cls.PRIMARY_KEY},
            sela_id,
            service_type,
            description,
            timestamp,
            recipient_id,
            duration,
            metadata
        FROM {cls.TABLE_NAME}
        WHERE sela_id = ?
        ORDER BY timestamp DESC
        LIMIT ?
        """, (sela_id, limit))
        
        rows = cursor.fetchall()
        conn.close()
        
        return [
            cls(
                service_id=row[0],
                sela_id=row[1],
                service_type=row[2],
                description=row[3],
                timestamp=row[4],
                recipient_id=row[5],
                duration=row[6],
                metadata=json.loads(row[7]) if row[7] else {}
            )
            for row in rows
        ]
    
    @classmethod
    def find_by_type(cls, service_type: str, limit: int = 100) -> List["Service"]:
        """
        Find Services by type.
        
        Args:
            service_type: The service type
            limit: The maximum number of Services to return
        
        Returns:
            A list of Services of the specified type
        """
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()
        
        cursor.execute(f"""
        SELECT
            {cls.PRIMARY_KEY},
            sela_id,
            service_type,
            description,
            timestamp,
            recipient_id,
            duration,
            metadata
        FROM {cls.TABLE_NAME}
        WHERE service_type = ?
        ORDER BY timestamp DESC
        LIMIT ?
        """, (service_type, limit))
        
        rows = cursor.fetchall()
        conn.close()
        
        return [
            cls(
                service_id=row[0],
                sela_id=row[1],
                service_type=row[2],
                description=row[3],
                timestamp=row[4],
                recipient_id=row[5],
                duration=row[6],
                metadata=json.loads(row[7]) if row[7] else {}
            )
            for row in rows
        ]
    
    @classmethod
    def find_by_recipient(cls, recipient_id: str, limit: int = 100) -> List["Service"]:
        """
        Find Services by recipient ID.
        
        Args:
            recipient_id: The recipient's identity ID
            limit: The maximum number of Services to return
        
        Returns:
            A list of Services for the recipient
        """
        conn = sqlite3.connect(onnyx_config.db_path)
        cursor = conn.cursor()
        
        cursor.execute(f"""
        SELECT
            {cls.PRIMARY_KEY},
            sela_id,
            service_type,
            description,
            timestamp,
            recipient_id,
            duration,
            metadata
        FROM {cls.TABLE_NAME}
        WHERE recipient_id = ?
        ORDER BY timestamp DESC
        LIMIT ?
        """, (recipient_id, limit))
        
        rows = cursor.fetchall()
        conn.close()
        
        return [
            cls(
                service_id=row[0],
                sela_id=row[1],
                service_type=row[2],
                description=row[3],
                timestamp=row[4],
                recipient_id=row[5],
                duration=row[6],
                metadata=json.loads(row[7]) if row[7] else {}
            )
            for row in rows
        ]
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the Service to a dictionary.
        
        Returns:
            The Service as a dictionary
        """
        return {
            "service_id": self.service_id,
            "sela_id": self.sela_id,
            "service_type": self.service_type,
            "description": self.description,
            "timestamp": self.timestamp,
            "recipient_id": self.recipient_id,
            "duration": self.duration,
            "metadata": self.metadata
        }
