"""
Labor API Routes
Handles labor logging, verification, and biblical tokenomics integration
"""

import json
import logging
import time
import uuid
from flask import Blueprint, request, jsonify, session, render_template, redirect, url_for
from typing import Dict, Any

# Import our tokenomics system
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from shared.db.db import db
from blockchain.tokenomics.biblical_tokenomics import BiblicalTokenomics
from blockchain.tokenomics.labor_rewards import LaborRewardCalculator
from blockchain.tokenomics.mikvah_tokens import MikvahTokenManager

# Set up logging
logger = logging.getLogger("onnyx.web.labor")

# Create blueprint
labor_bp = Blueprint('labor', __name__, url_prefix='/api/labor')

# Initialize tokenomics systems
biblical_tokenomics = BiblicalTokenomics(db)
labor_calculator = LaborRewardCalculator(db)
mikvah_manager = MikvahTokenManager(db)

@labor_bp.route('/dashboard')
def labor_dashboard():
    """Labor dashboard page."""
    # Check authentication
    if 'identity_id' not in session:
        return redirect(url_for('auth.login'))

    return render_template('labor/dashboard.html')

@labor_bp.route('/log', methods=['POST'])
def log_labor():
    """Log new labor activity with validation."""
    try:
        # Check authentication
        if 'identity_id' not in session:
            return jsonify({'error': 'Authentication required'}), 401

        identity_id = session['identity_id']

        # Check CIPP verification level (Tier 1+ required)
        identity = db.query_one("""
            SELECT verification_level FROM identities WHERE identity_id = ?
        """, (identity_id,))

        if not identity or identity['verification_level'] < 1:
            return jsonify({'error': 'Minimum Tier 1 verification required for labor logging'}), 403

        # Get request data
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        # Add identity_id to data
        data['identity_id'] = identity_id

        # Validate labor submission
        is_valid, error_message = labor_calculator.validate_labor_submission(data)
        if not is_valid:
            return jsonify({'error': error_message}), 400

        # Check sabbath period
        if biblical_tokenomics.is_sabbath_period():
            return jsonify({'error': 'Labor logging not permitted during Sabbath period'}), 403

        # Create labor record
        labor_id = str(uuid.uuid4())
        current_time = int(time.time())
        current_season = biblical_tokenomics.current_season

        # Prepare metadata
        metadata = data.get('metadata', {})
        if isinstance(metadata, str):
            try:
                metadata = json.loads(metadata)
            except:
                metadata = {}

        # Insert labor record
        db.execute("""
            INSERT INTO labor_records
            (labor_id, identity_id, sela_id, labor_type, description, value_estimate,
             timestamp, verification_status, mikvah_eligible, season_period,
             metadata, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (labor_id, identity_id, data.get('sela_id'), data['labor_type'],
              data['description'], float(data['value_estimate']), current_time,
              'pending', True, current_season, json.dumps(metadata),
              current_time, current_time))

        # Calculate potential rewards (for display)
        potential_rewards = labor_calculator.calculate_base_reward(data)

        logger.info(f"Labor logged: {labor_id} by {identity_id}")

        return jsonify({
            'success': True,
            'labor_id': labor_id,
            'status': 'pending_verification',
            'potential_mikvah_tokens': potential_rewards[0],
            'potential_etzem_points': potential_rewards[1],
            'message': 'Labor logged successfully. Awaiting community verification.'
        })

    except Exception as e:
        logger.error(f"Error logging labor: {e}")
        return jsonify({'error': 'Failed to log labor'}), 500

@labor_bp.route('/history/<identity_id>')
def get_labor_history(identity_id):
    """Retrieve labor history for an identity."""
    try:
        # Check if requesting own history or has permission
        if 'identity_id' not in session:
            return jsonify({'error': 'Authentication required'}), 401

        requesting_identity = session['identity_id']

        # Allow viewing own history or if requester is verified
        if requesting_identity != identity_id:
            requester = db.query_one("""
                SELECT verification_level FROM identities WHERE identity_id = ?
            """, (requesting_identity,))

            if not requester or requester['verification_level'] < 2:
                return jsonify({'error': 'Insufficient permissions'}), 403

        # Get labor history
        labor_records = db.query("""
            SELECT lr.*, i.name as verifier_name
            FROM labor_records lr
            LEFT JOIN identities i ON lr.verified_by = i.identity_id
            WHERE lr.identity_id = ?
            ORDER BY lr.timestamp DESC
            LIMIT 50
        """, (identity_id,))

        # Get labor statistics
        stats = biblical_tokenomics.get_labor_statistics(identity_id)

        # Get token balance
        token_balance = mikvah_manager.get_token_balance(identity_id)

        return jsonify({
            'labor_records': labor_records,
            'statistics': stats,
            'token_balance': token_balance
        })

    except Exception as e:
        logger.error(f"Error getting labor history: {e}")
        return jsonify({'error': 'Failed to retrieve labor history'}), 500

@labor_bp.route('/verify/<labor_id>', methods=['POST'])
def verify_labor(labor_id):
    """Verify labor by community member or validator."""
    try:
        # Check authentication
        if 'identity_id' not in session:
            return jsonify({'error': 'Authentication required'}), 401

        verifier_id = session['identity_id']

        # Check verifier permissions (Tier 1+ required)
        verifier = db.query_one("""
            SELECT verification_level, etzem_score FROM identities WHERE identity_id = ?
        """, (verifier_id,))

        if not verifier or verifier['verification_level'] < 1:
            return jsonify({'error': 'Minimum Tier 1 verification required for labor verification'}), 403

        # Get verification data
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No verification data provided'}), 400

        verification_type = data.get('verification_type', 'approve')  # 'approve', 'dispute', 'reject'
        verification_notes = data.get('notes', '')

        if verification_type not in ['approve', 'dispute', 'reject']:
            return jsonify({'error': 'Invalid verification type'}), 400

        # Get labor record
        labor = db.query_one("""
            SELECT * FROM labor_records WHERE labor_id = ?
        """, (labor_id,))

        if not labor:
            return jsonify({'error': 'Labor record not found'}), 404

        # Can't verify own labor
        if labor['identity_id'] == verifier_id:
            return jsonify({'error': 'Cannot verify your own labor'}), 403

        # Check if already verified by this person
        existing_verification = db.query_one("""
            SELECT verification_id FROM labor_verifications
            WHERE labor_id = ? AND verifier_id = ?
        """, (labor_id, verifier_id))

        if existing_verification:
            return jsonify({'error': 'You have already verified this labor'}), 400

        # Create verification record
        verification_id = str(uuid.uuid4())

        db.execute("""
            INSERT INTO labor_verifications
            (verification_id, labor_id, verifier_id, verification_type,
             verification_notes, timestamp)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (verification_id, labor_id, verifier_id, verification_type,
              verification_notes, int(time.time())))

        # Check if labor should be marked as verified
        verification_result = _check_labor_verification_status(labor_id)

        if verification_result['status_changed']:
            # Update labor record
            db.execute("""
                UPDATE labor_records SET
                    verification_status = ?,
                    verified_by = ?,
                    updated_at = ?
                WHERE labor_id = ?
            """, (verification_result['new_status'], verifier_id, int(time.time()), labor_id))

            # If verified, process token minting
            if verification_result['new_status'] == 'verified':
                mint_result = biblical_tokenomics.process_labor_mint(labor_id)

                return jsonify({
                    'success': True,
                    'verification_id': verification_id,
                    'labor_status': verification_result['new_status'],
                    'tokens_minted': mint_result.get('mikvah_tokens', 0),
                    'etzem_awarded': mint_result.get('etzem_points', 0),
                    'message': 'Labor verified and tokens minted successfully'
                })

        return jsonify({
            'success': True,
            'verification_id': verification_id,
            'labor_status': labor['verification_status'],
            'message': 'Verification recorded successfully'
        })

    except Exception as e:
        logger.error(f"Error verifying labor: {e}")
        return jsonify({'error': 'Failed to verify labor'}), 500

@labor_bp.route('/pending-verification')
def get_pending_verification():
    """List labor awaiting verification."""
    try:
        # Check authentication
        if 'identity_id' not in session:
            return jsonify({'error': 'Authentication required'}), 401

        identity_id = session['identity_id']

        # Check permissions (Tier 1+ required)
        identity = db.query_one("""
            SELECT verification_level FROM identities WHERE identity_id = ?
        """, (identity_id,))

        if not identity or identity['verification_level'] < 1:
            return jsonify({'error': 'Minimum Tier 1 verification required'}), 403

        # Get pending labor records (excluding own)
        pending_labor = db.query("""
            SELECT lr.*, i.name as submitter_name, i.nation_name,
                   COUNT(lv.verification_id) as verification_count
            FROM labor_records lr
            JOIN identities i ON lr.identity_id = i.identity_id
            LEFT JOIN labor_verifications lv ON lr.labor_id = lv.labor_id
            WHERE lr.verification_status = 'pending'
            AND lr.identity_id != ?
            GROUP BY lr.labor_id, lr.identity_id, lr.labor_type, lr.description,
                     lr.value_estimate, lr.timestamp, i.name, i.nation_name
            ORDER BY lr.timestamp DESC
            LIMIT 20
        """, (identity_id,))

        # Check which ones this user has already verified
        for labor in pending_labor:
            existing_verification = db.query_one("""
                SELECT verification_type FROM labor_verifications
                WHERE labor_id = ? AND verifier_id = ?
            """, (labor['labor_id'], identity_id))

            labor['user_verification'] = existing_verification['verification_type'] if existing_verification else None

        return jsonify({
            'pending_labor': pending_labor,
            'total_count': len(pending_labor)
        })

    except Exception as e:
        logger.error(f"Error getting pending verification: {e}")
        return jsonify({'error': 'Failed to retrieve pending labor'}), 500

@labor_bp.route('/dispute/<labor_id>', methods=['PUT'])
def dispute_labor(labor_id):
    """Dispute labor record validity."""
    try:
        # Check authentication
        if 'identity_id' not in session:
            return jsonify({'error': 'Authentication required'}), 401

        disputer_id = session['identity_id']

        # Check permissions (Tier 2+ required for disputes)
        disputer = db.query_one("""
            SELECT verification_level, etzem_score FROM identities WHERE identity_id = ?
        """, (disputer_id,))

        if not disputer or disputer['verification_level'] < 2:
            return jsonify({'error': 'Minimum Tier 2 verification required for labor disputes'}), 403

        # Get dispute data
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No dispute data provided'}), 400

        dispute_reason = data.get('reason', '')
        if len(dispute_reason) < 10:
            return jsonify({'error': 'Dispute reason must be at least 10 characters'}), 400

        # Get labor record
        labor = db.query_one("""
            SELECT * FROM labor_records WHERE labor_id = ?
        """, (labor_id,))

        if not labor:
            return jsonify({'error': 'Labor record not found'}), 404

        # Can't dispute own labor
        if labor['identity_id'] == disputer_id:
            return jsonify({'error': 'Cannot dispute your own labor'}), 403

        # Create dispute verification
        verification_id = str(uuid.uuid4())

        db.execute("""
            INSERT INTO labor_verifications
            (verification_id, labor_id, verifier_id, verification_type,
             verification_notes, timestamp)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (verification_id, labor_id, disputer_id, 'dispute',
              dispute_reason, int(time.time())))

        # Update labor status to disputed
        db.execute("""
            UPDATE labor_records SET
                verification_status = 'disputed',
                updated_at = ?
            WHERE labor_id = ?
        """, (int(time.time()), labor_id))

        logger.info(f"Labor disputed: {labor_id} by {disputer_id}")

        return jsonify({
            'success': True,
            'verification_id': verification_id,
            'labor_status': 'disputed',
            'message': 'Labor dispute recorded successfully'
        })

    except Exception as e:
        logger.error(f"Error disputing labor: {e}")
        return jsonify({'error': 'Failed to dispute labor'}), 500

@labor_bp.route('/types')
def get_labor_types():
    """Get available labor types and their information."""
    try:
        labor_type_info = labor_calculator.get_labor_type_info()

        return jsonify({
            'labor_types': labor_type_info['labor_types'],
            'quality_levels': labor_type_info['quality_levels'],
            'experience_levels': labor_type_info['experience_levels'],
            'submission_limits': {
                'daily_limit': 10,
                'min_description_length': 10,
                'max_description_length': 1000
            }
        })

    except Exception as e:
        logger.error(f"Error getting labor types: {e}")
        return jsonify({'error': 'Failed to retrieve labor types'}), 500

@labor_bp.route('/statistics/<identity_id>')
def get_labor_statistics(identity_id):
    """Get comprehensive labor statistics for an identity."""
    try:
        # Check authentication
        if 'identity_id' not in session:
            return jsonify({'error': 'Authentication required'}), 401

        requesting_identity = session['identity_id']

        # Allow viewing own stats or if requester is verified
        if requesting_identity != identity_id:
            requester = db.query_one("""
                SELECT verification_level FROM identities WHERE identity_id = ?
            """, (requesting_identity,))

            if not requester or requester['verification_level'] < 2:
                return jsonify({'error': 'Insufficient permissions'}), 403

        # Get comprehensive statistics
        stats = biblical_tokenomics.get_labor_statistics(identity_id)
        token_balance = mikvah_manager.get_token_balance(identity_id)

        # Get labor type breakdown
        labor_breakdown = db.query("""
            SELECT labor_type, COUNT(*) as count,
                   COALESCE(SUM(value_estimate), 0) as total_value,
                   COALESCE(SUM(etzem_points), 0) as total_etzem
            FROM labor_records
            WHERE identity_id = ? AND verification_status = 'verified'
            GROUP BY labor_type
            ORDER BY count DESC
        """, (identity_id,))

        return jsonify({
            'statistics': stats,
            'token_balance': token_balance,
            'labor_breakdown': labor_breakdown
        })

    except Exception as e:
        logger.error(f"Error getting labor statistics: {e}")
        return jsonify({'error': 'Failed to retrieve labor statistics'}), 500

def _check_labor_verification_status(labor_id: str) -> Dict[str, Any]:
    """Check if labor verification status should be updated."""
    try:
        # Get all verifications for this labor
        verifications = db.query("""
            SELECT verification_type FROM labor_verifications
            WHERE labor_id = ?
        """, (labor_id,))

        if not verifications:
            return {'status_changed': False, 'new_status': 'pending'}

        # Count verification types
        approvals = sum(1 for v in verifications if v['verification_type'] == 'approve')
        disputes = sum(1 for v in verifications if v['verification_type'] == 'dispute')
        rejections = sum(1 for v in verifications if v['verification_type'] == 'reject')

        total_verifications = len(verifications)

        # Determine new status
        if disputes > 0:
            new_status = 'disputed'
        elif rejections >= 2:  # 2+ rejections = rejected
            new_status = 'rejected'
        elif approvals >= 3:  # 3+ approvals = verified
            new_status = 'verified'
        elif total_verifications >= 5:  # 5+ verifications without consensus = disputed
            new_status = 'disputed'
        else:
            new_status = 'pending'

        # Get current status
        current_labor = db.query_one("""
            SELECT verification_status FROM labor_records WHERE labor_id = ?
        """, (labor_id,))

        current_status = current_labor['verification_status'] if current_labor else 'pending'

        return {
            'status_changed': new_status != current_status,
            'new_status': new_status,
            'verification_counts': {
                'approvals': approvals,
                'disputes': disputes,
                'rejections': rejections,
                'total': total_verifications
            }
        }

    except Exception as e:
        logger.error(f"Error checking verification status: {e}")
        return {'status_changed': False, 'new_status': 'pending'}
