/**
 * ONNYX Navigation Verification Script
 * Real-time verification of navigation functionality and responsiveness
 */

class NavigationVerifier {
    constructor() {
        this.isInitialized = false;
        this.currentBreakpoint = null;
        this.verificationResults = {};
        
        this.init();
    }

    init() {
        if (this.isInitialized) return;
        
        console.log('🔍 ONNYX Navigation Verifier Initialized');
        
        // Monitor viewport changes
        window.addEventListener('resize', () => this.handleViewportChange());
        
        // Initial verification
        this.handleViewportChange();
        
        // Test mobile menu functionality
        this.testMobileMenuFunctionality();
        
        // Test user dropdown functionality
        this.testUserDropdownFunctionality();
        
        this.isInitialized = true;
    }

    handleViewportChange() {
        const width = window.innerWidth;
        const newBreakpoint = this.getBreakpointName(width);
        
        if (newBreakpoint !== this.currentBreakpoint) {
            this.currentBreakpoint = newBreakpoint;
            console.log(`📱 Breakpoint changed to: ${newBreakpoint} (${width}px)`);
            this.verifyCurrentBreakpoint();
        }
    }

    getBreakpointName(width) {
        if (width <= 320) return 'Extra Small Mobile';
        if (width <= 480) return 'Small Mobile';
        if (width <= 768) return 'Mobile';
        if (width <= 992) return 'Tablet';
        if (width <= 1200) return 'Desktop';
        return 'Large Desktop';
    }

    verifyCurrentBreakpoint() {
        const width = window.innerWidth;
        const results = {
            breakpoint: this.currentBreakpoint,
            width: width,
            timestamp: new Date().toISOString(),
            checks: {}
        };

        // Check navigation visibility
        results.checks.navigationVisibility = this.checkNavigationVisibility(width);
        
        // Check touch targets
        results.checks.touchTargets = this.checkTouchTargets();
        
        // Check Biblical Tokenomics prominence
        results.checks.biblicalTokenomics = this.checkBiblicalTokenomicsProminence();
        
        // Check authentication buttons
        results.checks.authButtons = this.checkAuthenticationButtons(width);
        
        // Check mobile menu functionality
        if (width <= 768) {
            results.checks.mobileMenu = this.checkMobileMenuFunctionality();
        }
        
        // Check cyberpunk theme consistency
        results.checks.cyberpunkTheme = this.checkCyberpunkTheme();

        this.verificationResults[this.currentBreakpoint] = results;
        this.logVerificationResults(results);
    }

    checkNavigationVisibility(width) {
        const navbar = document.querySelector('.navbar-nav');
        const mobileToggle = document.querySelector('.mobile-menu-toggle');
        
        if (width <= 768) {
            // Mobile: navbar should be hidden, mobile toggle visible
            const navbarHidden = !navbar || window.getComputedStyle(navbar).display === 'none';
            const mobileVisible = mobileToggle && window.getComputedStyle(mobileToggle).display !== 'none';
            
            return {
                passed: navbarHidden && mobileVisible,
                details: {
                    navbarHidden,
                    mobileToggleVisible: mobileVisible
                }
            };
        } else {
            // Desktop: navbar visible, mobile toggle hidden
            const navbarVisible = navbar && window.getComputedStyle(navbar).display !== 'none';
            const mobileHidden = !mobileToggle || window.getComputedStyle(mobileToggle).display === 'none';
            
            return {
                passed: navbarVisible && mobileHidden,
                details: {
                    navbarVisible,
                    mobileToggleHidden: mobileHidden
                }
            };
        }
    }

    checkTouchTargets() {
        const targets = document.querySelectorAll('.nav-item, .auth-btn, .user-button, .mobile-menu-btn, .mobile-nav-item');
        let validTargets = 0;
        let totalTargets = 0;
        const invalidTargets = [];

        targets.forEach(target => {
            if (window.getComputedStyle(target).display !== 'none') {
                totalTargets++;
                const rect = target.getBoundingClientRect();
                
                if (rect.height >= 44 && rect.width >= 44) {
                    validTargets++;
                } else {
                    invalidTargets.push({
                        element: target.className,
                        size: `${Math.round(rect.width)}x${Math.round(rect.height)}`
                    });
                }
            }
        });

        return {
            passed: validTargets === totalTargets,
            details: {
                validTargets,
                totalTargets,
                invalidTargets
            }
        };
    }

    checkBiblicalTokenomicsProminence() {
        const featuredDesktop = document.querySelector('.nav-item-featured');
        const featuredMobile = document.querySelector('.mobile-nav-item.featured');
        const featured = featuredDesktop || featuredMobile;
        
        if (!featured) {
            return {
                passed: false,
                details: { error: 'Biblical Tokenomics element not found' }
            };
        }

        const styles = window.getComputedStyle(featured);
        const hasGradient = styles.background.includes('gradient');
        const hasGlow = styles.boxShadow !== 'none';
        const hasSpecialBorder = styles.borderColor.includes('255') || styles.borderWidth !== '1px';
        
        return {
            passed: hasGradient && hasGlow,
            details: {
                hasGradient,
                hasGlow,
                hasSpecialBorder,
                background: styles.background,
                boxShadow: styles.boxShadow
            }
        };
    }

    checkAuthenticationButtons(width) {
        const authBtns = document.querySelectorAll('.auth-btn');
        if (authBtns.length === 0) {
            return { passed: true, details: { note: 'No auth buttons present (user logged in)' } };
        }

        let properSized = 0;
        const expectedMaxWidth = width <= 320 ? 100 : width <= 480 ? 120 : width <= 768 ? 140 : 160;
        
        authBtns.forEach(btn => {
            const rect = btn.getBoundingClientRect();
            if (rect.width <= expectedMaxWidth + 10) { // 10px tolerance
                properSized++;
            }
        });

        return {
            passed: properSized === authBtns.length,
            details: {
                properSized,
                totalButtons: authBtns.length,
                expectedMaxWidth
            }
        };
    }

    checkMobileMenuFunctionality() {
        const mobileToggle = document.querySelector('.mobile-menu-btn');
        const mobileOverlay = document.querySelector('.mobile-menu-overlay');
        
        if (!mobileToggle || !mobileOverlay) {
            return {
                passed: false,
                details: { error: 'Mobile menu elements not found' }
            };
        }

        // Check if Alpine.js is working
        const hasAlpineData = mobileToggle.hasAttribute('x-data') || mobileToggle.closest('[x-data]');
        
        return {
            passed: hasAlpineData,
            details: {
                toggleExists: !!mobileToggle,
                overlayExists: !!mobileOverlay,
                hasAlpineData
            }
        };
    }

    checkCyberpunkTheme() {
        const navbar = document.querySelector('.onnyx-navbar');
        if (!navbar) return { passed: false, details: { error: 'Navbar not found' } };

        const styles = window.getComputedStyle(navbar);
        const hasBackdropFilter = styles.backdropFilter.includes('blur') || styles.webkitBackdropFilter.includes('blur');
        const hasGradientBg = styles.background.includes('gradient');
        const hasCyberColors = styles.borderColor.includes('255') || document.querySelector('[style*="--cyber-cyan"]');

        return {
            passed: hasBackdropFilter,
            details: {
                hasBackdropFilter,
                hasGradientBg,
                hasCyberColors,
                backdropFilter: styles.backdropFilter
            }
        };
    }

    testMobileMenuFunctionality() {
        const mobileBtn = document.querySelector('.mobile-menu-btn');
        if (!mobileBtn) return;

        // Test click functionality
        console.log('🧪 Testing mobile menu click functionality...');
        
        // Simulate click event
        const clickEvent = new Event('click', { bubbles: true });
        mobileBtn.dispatchEvent(clickEvent);
        
        setTimeout(() => {
            const overlay = document.querySelector('.mobile-menu-overlay');
            const isVisible = overlay && window.getComputedStyle(overlay).display !== 'none';
            console.log(`📱 Mobile menu test: ${isVisible ? '✅ Working' : '❌ Not working'}`);
        }, 100);
    }

    testUserDropdownFunctionality() {
        const userButton = document.querySelector('.user-button');
        if (!userButton) return;

        console.log('🧪 Testing user dropdown functionality...');
        
        // Simulate click event
        const clickEvent = new Event('click', { bubbles: true });
        userButton.dispatchEvent(clickEvent);
        
        setTimeout(() => {
            const dropdown = document.querySelector('.user-menu');
            const isVisible = dropdown && window.getComputedStyle(dropdown).display !== 'none';
            console.log(`👤 User dropdown test: ${isVisible ? '✅ Working' : '❌ Not working'}`);
        }, 100);
    }

    logVerificationResults(results) {
        const passedChecks = Object.values(results.checks).filter(check => check.passed).length;
        const totalChecks = Object.keys(results.checks).length;
        const passRate = ((passedChecks / totalChecks) * 100).toFixed(1);
        
        console.log(`📊 ${results.breakpoint} Verification:`, {
            passRate: `${passRate}%`,
            passed: `${passedChecks}/${totalChecks}`,
            details: results.checks
        });

        // Log any failures
        Object.entries(results.checks).forEach(([checkName, result]) => {
            if (!result.passed) {
                console.warn(`⚠️ ${checkName} failed:`, result.details);
            }
        });
    }

    generateReport() {
        console.log('📋 Navigation Verification Report:', this.verificationResults);
        return this.verificationResults;
    }
}

// Initialize verification when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.navigationVerifier = new NavigationVerifier();
    });
} else {
    window.navigationVerifier = new NavigationVerifier();
}

// Export for manual testing
window.NavigationVerifier = NavigationVerifier;
