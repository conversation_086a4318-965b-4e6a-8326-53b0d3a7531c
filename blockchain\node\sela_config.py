"""
Onnyx Sela Miner Configuration Module

This module provides the SelaConfig class for managing Sela miner configurations.
"""

import os
import json
import logging
import uuid
from typing import Dict, Any, Optional, List

# Set up logging
logger = logging.getLogger("onnyx.node.sela_config")

class SelaConfig:
    """
    SelaConfig manages the configuration for a Sela miner node.
    """

    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the Sela miner configuration.

        Args:
            config_path: Path to the configuration file. If None, a default path will be used.
        """
        # Set up configuration path
        if config_path is None:
            # Use a default path in the data directory
            base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            data_dir = os.path.join(base_dir, "data")
            if not os.path.exists(data_dir):
                os.makedirs(data_dir)
            self.config_path = os.path.join(data_dir, "sela_config.json")
        else:
            self.config_path = config_path

        # Default configuration
        self.config = {
            "sela_id": "",
            "identity_id": "",
            "private_key_path": "",
            "api_port": 8888,
            "role": "validator",
            "auto_mine": False,
            "mine_interval": 60,  # seconds
            "activity_log_path": "data/activity_log.json",
            "validator_rotation": {
                "enabled": True,
                "min_etzem_score": 30,
                "required_badges": ["SELA_FOUNDER", "VALIDATOR_ELIGIBLE_BADGE"]
            }
        }

        # Load configuration
        self._load_config()

    def _load_config(self):
        """Load the configuration from the file."""
        try:
            if os.path.exists(self.config_path) and os.path.getsize(self.config_path) > 0:
                with open(self.config_path, "r") as f:
                    loaded_config = json.load(f)
                    # Update the default config with the loaded values
                    self.config.update(loaded_config)
                    logger.info(f"Loaded Sela miner configuration from {self.config_path}")
            else:
                # Save the default configuration
                self._save_config()
                logger.info(f"Created default Sela miner configuration at {self.config_path}")
        except Exception as e:
            logger.error(f"Error loading Sela miner configuration: {str(e)}")

    def _save_config(self):
        """Save the configuration to the file."""
        try:
            with open(self.config_path, "w") as f:
                json.dump(self.config, f, indent=2)
                logger.info(f"Saved Sela miner configuration to {self.config_path}")
        except Exception as e:
            logger.error(f"Error saving Sela miner configuration: {str(e)}")

    def get(self, key: str, default: Any = None) -> Any:
        """
        Get a configuration value.

        Args:
            key: The configuration key.
            default: The default value to return if the key is not found.

        Returns:
            The configuration value.
        """
        return self.config.get(key, default)

    def set(self, key: str, value: Any):
        """
        Set a configuration value.

        Args:
            key: The configuration key.
            value: The configuration value.
        """
        self.config[key] = value
        self._save_config()

    def validate(self) -> bool:
        """
        Validate the configuration.

        Returns:
            True if the configuration is valid, False otherwise.
        """
        # Check required fields
        required_fields = ["sela_id", "identity_id", "private_key_path"]
        for field in required_fields:
            if not self.config.get(field):
                logger.error(f"Missing required configuration field: {field}")
                return False

        # Check if private key file exists
        if not os.path.exists(self.config["private_key_path"]):
            logger.error(f"Private key file not found: {self.config['private_key_path']}")
            return False

        return True

    def setup_new_sela(self, sela_id: str, identity_id: str, private_key_path: str, api_port: int = 8888):
        """
        Set up a new Sela miner configuration.

        Args:
            sela_id: The Sela ID.
            identity_id: The identity ID.
            private_key_path: Path to the private key file.
            api_port: The API port to use.
        """
        self.config["sela_id"] = sela_id
        self.config["identity_id"] = identity_id
        self.config["private_key_path"] = private_key_path
        self.config["api_port"] = api_port
        self._save_config()
        logger.info(f"Set up new Sela miner configuration for Sela {sela_id}")

    def get_all(self) -> Dict[str, Any]:
        """
        Get all configuration values.

        Returns:
            All configuration values.
        """
        return self.config.copy()


# Create a global instance of the Sela configuration
sela_config = SelaConfig()

# Export configuration values for easy access
SELA_ID = sela_config.get("sela_id")
IDENTITY_ID = sela_config.get("identity_id")
PRIVATE_KEY_PATH = sela_config.get("private_key_path")
API_PORT = sela_config.get("api_port")
ROLE = sela_config.get("role")
AUTO_MINE = sela_config.get("auto_mine")
MINE_INTERVAL = sela_config.get("mine_interval")
ACTIVITY_LOG_PATH = sela_config.get("activity_log_path")
VALIDATOR_ROTATION = sela_config.get("validator_rotation")
