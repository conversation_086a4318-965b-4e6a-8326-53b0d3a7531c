"""
ONNYX P2P Miner
Distributed mining system for the covenant blockchain with biblical compliance
"""

import asyncio
import json
import time
import hashlib
import logging
from typing import Dict, List, Optional, Set
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class MiningStatus(Enum):
    IDLE = "idle"
    MINING = "mining"
    PROPOSING = "proposing"
    VALIDATING = "validating"
    SABBATH_REST = "sabbath_rest"

class ProposalStatus(Enum):
    PENDING = "pending"
    VALIDATING = "validating"
    APPROVED = "approved"
    REJECTED = "rejected"
    COMMITTED = "committed"

@dataclass
class MiningProposal:
    proposal_id: str
    miner_id: str
    tribal_code: str
    block_height: int
    block_data: dict
    biblical_compliance_score: float
    timestamp: int
    status: ProposalStatus = ProposalStatus.PENDING
    validator_votes: Dict[str, bool] = None
    
    def __post_init__(self):
        if self.validator_votes is None:
            self.validator_votes = {}

@dataclass
class MiningReward:
    miner_id: str
    base_reward: float
    deed_bonus: float
    gleaning_allocation: float
    total_reward: float
    token_id: str
    biblical_compliance: bool

class P2PMiner:
    """
    Distributed P2P miner that coordinates with tribal elders for covenant-compliant mining
    """
    
    def __init__(self, peer_manager, consensus_engine, miner_identity: str, tribal_code: str = None):
        self.peer_manager = peer_manager
        self.consensus = consensus_engine
        self.miner_identity = miner_identity
        self.tribal_code = tribal_code
        
        # Mining state
        self.mining_status = MiningStatus.IDLE
        self.current_proposal: Optional[MiningProposal] = None
        self.active_proposals: Dict[str, MiningProposal] = {}
        self.mining_history: List[dict] = []
        
        # Biblical compliance
        self.sabbath_enforcement = True
        self.anti_usury_compliance = True
        self.gleaning_pool_participation = True
        
        # Mining parameters
        self.mining_difficulty = 1
        self.block_time_target = 600  # 10 minutes (biblical tithe of hour)
        self.max_transactions_per_block = 144  # 12 tribes * 12
        
        # Performance tracking
        self.blocks_mined = 0
        self.proposals_submitted = 0
        self.proposals_approved = 0
        self.total_rewards_earned = 0.0
        self.biblical_compliance_score = 1.0
        
        # Register mining message handlers
        self._register_mining_handlers()
        
        # Start background mining tasks
        asyncio.create_task(self._mining_coordination_loop())
        asyncio.create_task(self._biblical_compliance_monitor())
        
        logger.info(f"P2P Miner initialized for {miner_identity} (Tribal: {tribal_code})")
    
    def _register_mining_handlers(self):
        """Register P2P message handlers for mining coordination"""
        handlers = {
            'mining_proposal': self._handle_mining_proposal,
            'proposal_validation': self._handle_proposal_validation,
            'mining_approval': self._handle_mining_approval,
            'mining_rejection': self._handle_mining_rejection,
            'block_commitment': self._handle_block_commitment,
            'mining_stats_request': self._handle_mining_stats_request,
            'sabbath_announcement': self._handle_sabbath_announcement
        }
        
        for msg_type, handler in handlers.items():
            self.peer_manager.register_message_handler(msg_type, handler)
    
    async def start_mining(self) -> bool:
        """Start the P2P mining process"""
        try:
            # Check if mining is allowed (Sabbath compliance)
            if not await self._can_mine():
                logger.info("Mining not allowed - Sabbath period or other restrictions")
                self.mining_status = MiningStatus.SABBATH_REST
                return False
            
            # Check network readiness
            tribal_elders = self.peer_manager.get_tribal_elders()
            if len(tribal_elders) < 8:  # Need 2/3 of 12 elders
                logger.warning("Insufficient tribal elders for mining validation")
                return False
            
            self.mining_status = MiningStatus.MINING
            logger.info(f"P2P mining started for {self.miner_identity}")
            
            # Start mining coordination
            await self._coordinate_mining()
            
            return True
            
        except Exception as e:
            logger.error(f"Error starting P2P mining: {e}")
            self.mining_status = MiningStatus.IDLE
            return False
    
    async def stop_mining(self):
        """Stop the P2P mining process"""
        self.mining_status = MiningStatus.IDLE
        self.current_proposal = None
        logger.info(f"P2P mining stopped for {self.miner_identity}")
    
    async def _coordinate_mining(self):
        """Coordinate mining with the P2P network"""
        try:
            # Get current blockchain state
            current_height = await self._get_current_block_height()
            
            # Check if we're eligible to mine this block
            if not await self._is_eligible_to_mine(current_height + 1):
                logger.debug("Not eligible to mine current block")
                return
            
            # Create mining proposal
            proposal = await self._create_mining_proposal(current_height + 1)
            
            if proposal:
                # Submit proposal to tribal elders for validation
                await self._submit_proposal_for_validation(proposal)
                
                # Wait for validation results
                await self._wait_for_validation_results(proposal)
        
        except Exception as e:
            logger.error(f"Mining coordination error: {e}")
    
    async def _create_mining_proposal(self, block_height: int) -> Optional[MiningProposal]:
        """Create a mining proposal for validation"""
        try:
            # Import the existing miner for block creation
            from blockchain.consensus.miner import BlockMiner
            
            # Create block using existing miner
            block_miner = BlockMiner()
            
            # Get pending transactions
            pending_txs = await self._get_pending_transactions()
            
            # Filter transactions for biblical compliance
            compliant_txs = await self._filter_compliant_transactions(pending_txs)
            
            # Create block data
            block_data = {
                "block_height": block_height,
                "miner_id": self.miner_identity,
                "tribal_code": self.tribal_code,
                "transactions": compliant_txs[:self.max_transactions_per_block],
                "timestamp": int(time.time()),
                "previous_hash": await self._get_previous_block_hash(),
                "difficulty": self.mining_difficulty
            }
            
            # Calculate biblical compliance score
            compliance_score = await self._calculate_biblical_compliance(block_data)
            
            # Create proposal
            proposal_id = hashlib.sha256(
                f"{self.miner_identity}{block_height}{int(time.time())}".encode()
            ).hexdigest()[:16]
            
            proposal = MiningProposal(
                proposal_id=proposal_id,
                miner_id=self.miner_identity,
                tribal_code=self.tribal_code or "COMMUNITY",
                block_height=block_height,
                block_data=block_data,
                biblical_compliance_score=compliance_score,
                timestamp=int(time.time())
            )
            
            self.current_proposal = proposal
            self.active_proposals[proposal_id] = proposal
            self.proposals_submitted += 1
            
            logger.info(f"Created mining proposal {proposal_id} for block {block_height}")
            return proposal
            
        except Exception as e:
            logger.error(f"Error creating mining proposal: {e}")
            return None
    
    async def _submit_proposal_for_validation(self, proposal: MiningProposal):
        """Submit mining proposal to tribal elders for validation"""
        try:
            self.mining_status = MiningStatus.PROPOSING
            
            # Create proposal message
            message = self.peer_manager.NetworkMessage(
                message_type="mining_proposal",
                sender_id=self.peer_manager.node_id,
                recipient_id=None,  # Broadcast to all
                data={
                    "proposal_id": proposal.proposal_id,
                    "miner_id": proposal.miner_id,
                    "tribal_code": proposal.tribal_code,
                    "block_height": proposal.block_height,
                    "block_data": proposal.block_data,
                    "biblical_compliance_score": proposal.biblical_compliance_score,
                    "timestamp": proposal.timestamp
                },
                timestamp=int(time.time())
            )
            
            # Broadcast to tribal elders
            await self.peer_manager.broadcast_message(message)
            
            proposal.status = ProposalStatus.VALIDATING
            logger.info(f"Submitted mining proposal {proposal.proposal_id} for validation")
            
        except Exception as e:
            logger.error(f"Error submitting proposal for validation: {e}")
    
    async def _wait_for_validation_results(self, proposal: MiningProposal, timeout: int = 60):
        """Wait for validation results from tribal elders"""
        try:
            start_time = time.time()
            
            while time.time() - start_time < timeout:
                if proposal.status in [ProposalStatus.APPROVED, ProposalStatus.REJECTED]:
                    break
                
                await asyncio.sleep(1)
            
            if proposal.status == ProposalStatus.APPROVED:
                await self._commit_approved_block(proposal)
            elif proposal.status == ProposalStatus.REJECTED:
                logger.info(f"Mining proposal {proposal.proposal_id} was rejected")
            else:
                logger.warning(f"Mining proposal {proposal.proposal_id} validation timeout")
                proposal.status = ProposalStatus.REJECTED
            
        except Exception as e:
            logger.error(f"Error waiting for validation results: {e}")
    
    async def _commit_approved_block(self, proposal: MiningProposal):
        """Commit an approved mining proposal as a new block"""
        try:
            # Import the existing miner for block creation
            from blockchain.consensus.miner import BlockMiner
            
            block_miner = BlockMiner()
            
            # Create the actual block
            block = block_miner.create_block(
                proposer_id=proposal.miner_id,
                sela_id=proposal.tribal_code
            )
            
            # Update block with proposal data
            block.update({
                "proposal_id": proposal.proposal_id,
                "biblical_compliance_score": proposal.biblical_compliance_score,
                "validator_approvals": len([v for v in proposal.validator_votes.values() if v])
            })
            
            # Broadcast block commitment
            message = self.peer_manager.NetworkMessage(
                message_type="block_commitment",
                sender_id=self.peer_manager.node_id,
                recipient_id=None,
                data={
                    "proposal_id": proposal.proposal_id,
                    "block": block,
                    "miner_id": proposal.miner_id
                },
                timestamp=int(time.time())
            )
            
            await self.peer_manager.broadcast_message(message)
            
            # Update mining statistics
            self.blocks_mined += 1
            self.proposals_approved += 1
            
            # Calculate and record rewards
            reward = await self._calculate_mining_reward(proposal, block)
            self.total_rewards_earned += reward.total_reward
            
            # Record mining activity
            self.mining_history.append({
                "proposal_id": proposal.proposal_id,
                "block_height": proposal.block_height,
                "block_hash": block["hash"],
                "reward": reward.total_reward,
                "compliance_score": proposal.biblical_compliance_score,
                "timestamp": int(time.time())
            })
            
            proposal.status = ProposalStatus.COMMITTED
            self.mining_status = MiningStatus.IDLE
            
            logger.info(f"Successfully committed block {proposal.block_height} from proposal {proposal.proposal_id}")
            
        except Exception as e:
            logger.error(f"Error committing approved block: {e}")
    
    # Message Handlers
    async def _handle_mining_proposal(self, message):
        """Handle incoming mining proposals (for tribal elders)"""
        try:
            # Only tribal elders should validate proposals
            if self.peer_manager.node_type.value != "tribal_elder":
                return
            
            data = message.data
            proposal_id = data["proposal_id"]
            
            # Create proposal object
            proposal = MiningProposal(
                proposal_id=proposal_id,
                miner_id=data["miner_id"],
                tribal_code=data["tribal_code"],
                block_height=data["block_height"],
                block_data=data["block_data"],
                biblical_compliance_score=data["biblical_compliance_score"],
                timestamp=data["timestamp"]
            )
            
            # Validate the proposal
            is_valid = await self._validate_mining_proposal(proposal)
            
            # Send validation response
            response = self.peer_manager.NetworkMessage(
                message_type="proposal_validation",
                sender_id=self.peer_manager.node_id,
                recipient_id=message.sender_id,
                data={
                    "proposal_id": proposal_id,
                    "validator_id": self.peer_manager.node_id,
                    "tribal_code": self.tribal_code,
                    "is_valid": is_valid,
                    "compliance_score": proposal.biblical_compliance_score,
                    "validation_timestamp": int(time.time())
                },
                timestamp=int(time.time())
            )
            
            await self.peer_manager.send_to_peer(message.sender_id, response)
            
            logger.info(f"Validated mining proposal {proposal_id}: {'APPROVED' if is_valid else 'REJECTED'}")
            
        except Exception as e:
            logger.error(f"Error handling mining proposal: {e}")
    
    async def _handle_proposal_validation(self, message):
        """Handle validation responses from tribal elders"""
        try:
            data = message.data
            proposal_id = data["proposal_id"]
            is_valid = data["is_valid"]
            validator_id = data["validator_id"]
            
            if proposal_id in self.active_proposals:
                proposal = self.active_proposals[proposal_id]
                proposal.validator_votes[validator_id] = is_valid
                
                # Check if we have enough votes to make a decision
                total_votes = len(proposal.validator_votes)
                approval_votes = sum(proposal.validator_votes.values())
                
                # Need 2/3 majority from tribal elders
                tribal_elders = self.peer_manager.get_tribal_elders()
                required_votes = max(8, len(tribal_elders) * 2 // 3)
                
                if total_votes >= required_votes:
                    approval_ratio = approval_votes / total_votes
                    
                    if approval_ratio >= 0.67:  # 2/3 majority
                        proposal.status = ProposalStatus.APPROVED
                        logger.info(f"Mining proposal {proposal_id} APPROVED ({approval_votes}/{total_votes})")
                    else:
                        proposal.status = ProposalStatus.REJECTED
                        logger.info(f"Mining proposal {proposal_id} REJECTED ({approval_votes}/{total_votes})")
            
        except Exception as e:
            logger.error(f"Error handling proposal validation: {e}")
    
    async def _handle_mining_approval(self, message):
        """Handle mining approval messages"""
        logger.info(f"Received mining approval from {message.sender_id}")
    
    async def _handle_mining_rejection(self, message):
        """Handle mining rejection messages"""
        logger.info(f"Received mining rejection from {message.sender_id}")
    
    async def _handle_block_commitment(self, message):
        """Handle block commitment messages"""
        try:
            data = message.data
            proposal_id = data["proposal_id"]
            block = data["block"]
            miner_id = data["miner_id"]
            
            logger.info(f"Block committed by {miner_id} for proposal {proposal_id}")
            
            # Update our blockchain state
            await self._update_blockchain_state(block)
            
        except Exception as e:
            logger.error(f"Error handling block commitment: {e}")
    
    async def _handle_mining_stats_request(self, message):
        """Handle mining statistics requests"""
        try:
            stats = self.get_mining_statistics()
            
            response = self.peer_manager.NetworkMessage(
                message_type="mining_stats_response",
                sender_id=self.peer_manager.node_id,
                recipient_id=message.sender_id,
                data=stats,
                timestamp=int(time.time())
            )
            
            await self.peer_manager.send_to_peer(message.sender_id, response)
            
        except Exception as e:
            logger.error(f"Error handling mining stats request: {e}")
    
    async def _handle_sabbath_announcement(self, message):
        """Handle Sabbath period announcements"""
        try:
            data = message.data
            is_sabbath = data.get("is_sabbath", False)
            
            if is_sabbath and self.sabbath_enforcement:
                logger.info("Sabbath period announced - stopping mining activities")
                self.mining_status = MiningStatus.SABBATH_REST
                await self.stop_mining()
            elif not is_sabbath and self.mining_status == MiningStatus.SABBATH_REST:
                logger.info("Sabbath period ended - resuming mining activities")
                await self.start_mining()
            
        except Exception as e:
            logger.error(f"Error handling Sabbath announcement: {e}")
    
    # Background Tasks
    async def _mining_coordination_loop(self):
        """Main mining coordination loop"""
        while True:
            try:
                if self.mining_status == MiningStatus.MINING:
                    await self._coordinate_mining()
                
                # Wait before next mining attempt
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                logger.error(f"Mining coordination loop error: {e}")
                await asyncio.sleep(30)
    
    async def _biblical_compliance_monitor(self):
        """Monitor biblical compliance requirements"""
        while True:
            try:
                # Check if it's Sabbath period
                if await self._is_sabbath_period():
                    if self.mining_status != MiningStatus.SABBATH_REST and self.sabbath_enforcement:
                        logger.info("Sabbath period detected - entering rest mode")
                        self.mining_status = MiningStatus.SABBATH_REST
                        await self.stop_mining()
                elif self.mining_status == MiningStatus.SABBATH_REST:
                    logger.info("Sabbath period ended - resuming mining")
                    await self.start_mining()
                
                # Update biblical compliance score
                await self._update_biblical_compliance_score()
                
                await asyncio.sleep(300)  # Check every 5 minutes
                
            except Exception as e:
                logger.error(f"Biblical compliance monitor error: {e}")
                await asyncio.sleep(300)
    
    # Utility Methods
    async def _can_mine(self) -> bool:
        """Check if mining is currently allowed"""
        # Check Sabbath compliance
        if self.sabbath_enforcement and await self._is_sabbath_period():
            return False
        
        # Check network connectivity
        tribal_elders = self.peer_manager.get_tribal_elders()
        if len(tribal_elders) < 8:
            return False
        
        return True
    
    async def _is_eligible_to_mine(self, block_height: int) -> bool:
        """Check if this miner is eligible to mine the given block"""
        # Simple round-robin or random selection
        # In production, this could be more sophisticated
        return True
    
    async def _get_current_block_height(self) -> int:
        """Get the current blockchain height"""
        try:
            from shared.db.db import db
            result = db.query_one("SELECT MAX(block_number) as height FROM blocks")
            return result['height'] if result['height'] is not None else 0
        except Exception as e:
            logger.error(f"Error getting current block height: {e}")
            return 0
    
    async def _get_previous_block_hash(self) -> str:
        """Get the hash of the previous block"""
        try:
            from shared.db.db import db
            result = db.query_one("SELECT block_hash FROM blocks ORDER BY block_number DESC LIMIT 1")
            return result['block_hash'] if result else "0" * 64
        except Exception as e:
            logger.error(f"Error getting previous block hash: {e}")
            return "0" * 64
    
    async def _get_pending_transactions(self) -> List[dict]:
        """Get pending transactions from mempool"""
        try:
            from shared.db.db import db
            results = db.query("SELECT * FROM mempool WHERE status = 'pending' ORDER BY created_at ASC")
            return [dict(row) for row in results] if results else []
        except Exception as e:
            logger.error(f"Error getting pending transactions: {e}")
            return []
    
    async def _filter_compliant_transactions(self, transactions: List[dict]) -> List[dict]:
        """Filter transactions for biblical compliance"""
        compliant_txs = []
        
        for tx in transactions:
            if await self._is_transaction_compliant(tx):
                compliant_txs.append(tx)
        
        return compliant_txs
    
    async def _is_transaction_compliant(self, transaction: dict) -> bool:
        """Check if a transaction is biblically compliant"""
        # Check for usury violations
        if transaction.get('op') == 'OP_LEND':
            interest_rate = transaction.get('data', {}).get('interest_rate', 0)
            if interest_rate > 0 and self.anti_usury_compliance:
                return False
        
        # Check for Sabbath violations
        if await self._is_sabbath_period():
            forbidden_ops = ['OP_TRADE', 'OP_BUSINESS', 'OP_WORK']
            if transaction.get('op') in forbidden_ops:
                return False
        
        return True
    
    async def _calculate_biblical_compliance(self, block_data: dict) -> float:
        """Calculate biblical compliance score for a block"""
        score = 1.0
        transactions = block_data.get('transactions', [])
        
        for tx in transactions:
            if not await self._is_transaction_compliant(tx):
                score -= 0.1
        
        # Bonus for gleaning pool transactions
        gleaning_txs = [tx for tx in transactions if tx.get('op') == 'OP_GLEANING_DISTRIBUTE']
        score += len(gleaning_txs) * 0.05
        
        return max(0.0, min(1.0, score))
    
    async def _validate_mining_proposal(self, proposal: MiningProposal) -> bool:
        """Validate a mining proposal (for tribal elders)"""
        # Check biblical compliance score
        if proposal.biblical_compliance_score < 0.8:
            return False
        
        # Validate block structure
        if not proposal.block_data.get('transactions'):
            return False
        
        # Check for proper gleaning pool allocation
        has_gleaning = any(tx.get('op') == 'OP_GLEANING_DISTRIBUTE' 
                          for tx in proposal.block_data.get('transactions', []))
        
        return True
    
    async def _calculate_mining_reward(self, proposal: MiningProposal, block: dict) -> MiningReward:
        """Calculate mining reward with biblical tokenomics"""
        try:
            from shared.models.tokenomics import biblical_tokenomics
            from shared.config.chain_parameters import chain_parameters
            
            base_reward = chain_parameters.get("block_reward", 50.0)
            reward_token = chain_parameters.get("reward_token", "ONX")
            
            # Calculate deed bonus
            deed_bonus = 0.0
            if biblical_tokenomics:
                deed_bonus = biblical_tokenomics.calculate_deed_bonus(proposal.miner_id)
            
            # Calculate gleaning allocation (2% of total reward)
            total_reward_before_gleaning = base_reward + deed_bonus
            gleaning_allocation = total_reward_before_gleaning * 0.02
            
            # Final reward after gleaning allocation
            final_reward = total_reward_before_gleaning - gleaning_allocation
            
            return MiningReward(
                miner_id=proposal.miner_id,
                base_reward=base_reward,
                deed_bonus=deed_bonus,
                gleaning_allocation=gleaning_allocation,
                total_reward=final_reward,
                token_id=reward_token,
                biblical_compliance=proposal.biblical_compliance_score >= 0.8
            )
            
        except Exception as e:
            logger.error(f"Error calculating mining reward: {e}")
            return MiningReward(
                miner_id=proposal.miner_id,
                base_reward=50.0,
                deed_bonus=0.0,
                gleaning_allocation=1.0,
                total_reward=49.0,
                token_id="ONX",
                biblical_compliance=True
            )
    
    async def _is_sabbath_period(self) -> bool:
        """Check if it's currently Sabbath period"""
        # Simple Sabbath check - Saturday
        import time
        day_of_week = time.gmtime().tm_wday
        return day_of_week == 5  # Saturday (0=Monday)
    
    async def _update_biblical_compliance_score(self):
        """Update the miner's biblical compliance score"""
        # Calculate based on recent mining history
        if len(self.mining_history) > 0:
            recent_scores = [h['compliance_score'] for h in self.mining_history[-10:]]
            self.biblical_compliance_score = sum(recent_scores) / len(recent_scores)
    
    async def _update_blockchain_state(self, block: dict):
        """Update local blockchain state with new block"""
        # This would integrate with the blockchain sync system
        pass
    
    # Public API Methods
    def get_mining_statistics(self) -> dict:
        """Get comprehensive mining statistics"""
        return {
            "miner_id": self.miner_identity,
            "tribal_code": self.tribal_code,
            "mining_status": self.mining_status.value,
            "blocks_mined": self.blocks_mined,
            "proposals_submitted": self.proposals_submitted,
            "proposals_approved": self.proposals_approved,
            "approval_rate": self.proposals_approved / max(self.proposals_submitted, 1),
            "total_rewards_earned": self.total_rewards_earned,
            "biblical_compliance_score": self.biblical_compliance_score,
            "active_proposals": len(self.active_proposals),
            "mining_history_count": len(self.mining_history),
            "sabbath_enforcement": self.sabbath_enforcement,
            "anti_usury_compliance": self.anti_usury_compliance,
            "gleaning_pool_participation": self.gleaning_pool_participation
        }
    
    def get_mining_history(self) -> List[dict]:
        """Get mining history"""
        return self.mining_history.copy()
    
    def get_current_proposal(self) -> Optional[dict]:
        """Get current mining proposal"""
        if self.current_proposal:
            return {
                "proposal_id": self.current_proposal.proposal_id,
                "block_height": self.current_proposal.block_height,
                "status": self.current_proposal.status.value,
                "biblical_compliance_score": self.current_proposal.biblical_compliance_score,
                "validator_votes": len(self.current_proposal.validator_votes),
                "timestamp": self.current_proposal.timestamp
            }
        return None
