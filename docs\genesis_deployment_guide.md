# 🌟 ONNYX Genesis Deployment Guide

## Executive Summary

This document provides comprehensive instructions for deploying the ONNYX Genesis Block, establishing the immutable covenant foundation, and onboarding the first twelve tribal elders to begin covenant governance.

---

## 📋 Pre-Deployment Checklist

### ✅ Technical Requirements
- [ ] Database schema initialized with all required tables
- [ ] Biblical tokenomics system operational
- [ ] CIPP identity protection system active
- [ ] All 16 opcodes implemented and tested (including OP_COVENANT_FOUNDING)
- [ ] Genesis nations configuration file validated (47 nations)
- [ ] Genesis Voice Scrolls prepared for initial governance
- [ ] Covenant comprehension test system ready

### ✅ Spiritual Requirements
- [ ] Genesis Covenant document finalized and reviewed
- [ ] All 47 nations properly categorized (12 covenant + 35 witness)
- [ ] Biblical references verified for all nations and principles
- [ ] Psalm 83 enemy nations correctly identified as witness nations
- [ ] Covenant text spiritually aligned with biblical principles

### ✅ Governance Requirements
- [ ] Initial Council of Twelve Tribes structure defined
- [ ] Voting weight system established (Judah, Levi, Ephraim: 2 votes each)
- [ ] Quorum rules and majority requirements set
- [ ] Voice Scroll proposal system operational
- [ ] Tribal representative election process defined

---

## 🚀 Genesis Deployment Sequence

### **Phase 1: Genesis Block Creation**

#### Step 1: Calculate Covenant Hash
```bash
python scripts/generate_genesis_block.py
```

**Expected Output:**
```
📜 Calculating Genesis Covenant hash...
✅ Covenant hash calculated: [64-character SHA3-256 hash]
```

#### Step 2: Validate Nations Configuration
```bash
python -c "
import json
with open('data/genesis_nations.json', 'r') as f:
    config = json.load(f)
print(f'Total nations: {config[\"total_nations\"]}')
print(f'Covenant nations: {config[\"covenant_nations\"]}')
print(f'Witness nations: {config[\"witness_nations\"]}')
"
```

**Expected Output:**
```
Total nations: 47
Covenant nations: 12
Witness nations: 35
```

#### Step 3: Deploy Genesis Block
```bash
python scripts/generate_genesis_block.py
```

**Expected Output:**
```
🎉 GENESIS BLOCK GENERATION COMPLETE!
📦 Block Hash: [genesis_block_hash]
📜 Covenant Hash: [covenant_document_hash]
🌍 Nations Registered: 47
⏰ Timestamp: [ISO_timestamp]
🔒 Immutable: TRUE
🔐 Sealed: TRUE
```

### **Phase 2: Verification and Validation**

#### Step 1: Verify Genesis Block Immutability
```bash
python -c "
from shared.db.db import db
genesis = db.query_one('SELECT * FROM blocks WHERE block_height = 0')
print(f'Genesis Block Hash: {genesis[\"block_hash\"]}')
print(f'Previous Hash: {genesis[\"previous_hash\"]}')
print(f'Miner: {genesis[\"miner\"]}')
"
```

#### Step 2: Verify Covenant Transaction
```bash
python -c "
from shared.db.db import db
import json
tx = db.query_one('SELECT * FROM transactions WHERE block_height = 0')
data = json.loads(tx[\"data\"])
print(f'Operation: {tx[\"op\"]}')
print(f'Nations Count: {len(data[\"founding_nations\"])}')
print(f'Covenant Hash: {data[\"covenant_text_hash\"]}')
print(f'Immutable: {data[\"immutable\"]}')
print(f'Sealed: {data[\"sealed\"]}')
"
```

#### Step 3: Verify All Nations Registered
```bash
python -c "
from shared.db.db import db
nations = db.query('SELECT type, COUNT(*) as count FROM biblical_nations GROUP BY type')
for nation in nations:
    print(f'{nation[\"type\"]} nations: {nation[\"count\"]}')
total = db.query_one('SELECT COUNT(*) as count FROM biblical_nations')[\"count\"]
print(f'Total nations: {total}')
"
```

### **Phase 3: Initialize Governance System**

#### Step 1: Deploy Genesis Voice Scrolls
```bash
python -c "
import json
from shared.db.db import db

# Load genesis scrolls
with open('data/genesis_voice_scrolls.json', 'r') as f:
    scrolls_config = json.load(f)

for scroll in scrolls_config['scrolls']:
    db.execute('''
        INSERT INTO voice_scrolls (
            scroll_id, title, type, proposed_by, content, 
            voting_requirements, status, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    ''', (
        scroll['scroll_id'],
        scroll['title'], 
        scroll['type'],
        scroll['proposed_by'],
        json.dumps(scroll['content']),
        json.dumps(scroll['voting_requirements']),
        scroll['status'],
        scroll['proposed_at']
    ))

print('✅ Genesis Voice Scrolls deployed')
"
```

#### Step 2: Initialize Biblical Tokenomics
```bash
python -c "
from blockchain.tokenomics.biblical_tokenomics import BiblicalTokenomics
from shared.db.db import db

bt = BiblicalTokenomics(db)
print(f'✅ Biblical Tokenomics initialized')
print(f'   - Yovel Cycle: {bt.current_yovel_cycle}')
print(f'   - Season: {bt.current_season}')
print(f'   - Sabbath Active: {bt.is_sabbath_period()}')
"
```

---

## 👥 Tribal Elder Onboarding Process

### **Phase 1: Identity Registration**

Each of the 12 tribal elders must complete the following process:

#### Step 1: Create Soulbound Identity
```bash
# Example for Judah tribal elder
curl -X POST http://localhost:5000/auth/api/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Elder of Judah",
    "email": "<EMAIL>",
    "nation_code": "JU",
    "role_class": "Tribal_Elder"
  }'
```

#### Step 2: Complete Covenant Comprehension Test
- Navigate to `/auth/onboarding/covenant-test`
- Complete all 5 questions with minimum score of 4/5
- Accept covenant scroll digitally
- Receive community verification from Genesis Council

#### Step 3: Advance to Tier 1 CIPP Status
```bash
# Verify advancement
curl -X GET http://localhost:5000/auth/api/identity/{identity_id}/protection-status
```

**Expected Response:**
```json
{
  "verification_level": 1,
  "protection_tier": "Community",
  "covenant_accepted": true,
  "governance_eligible": true
}
```

### **Phase 2: Tribal Council Formation**

#### Step 1: Elect Tribal Representatives
Each tribe must elect their representative to the Council of Twelve Tribes:

```bash
# Example tribal election process
python -c "
from shared.db.db import db

# Register tribal representative
db.execute('''
    INSERT INTO tribal_representatives (
        tribe_code, identity_id, elected_at, term_expires, voting_weight
    ) VALUES (?, ?, ?, ?, ?)
''', ('JU', 'judah_elder_id', int(time.time()), int(time.time()) + 220752000, 2))

print('✅ Tribal representative registered')
"
```

#### Step 2: Activate Council Voting
Once all 12 representatives are elected:

```bash
# Activate the Council of Twelve Tribes
python -c "
from shared.db.db import db

# Check all tribes have representatives
reps = db.query('SELECT COUNT(*) as count FROM tribal_representatives')
if reps[0]['count'] == 12:
    print('✅ All 12 tribal representatives elected')
    print('✅ Council of Twelve Tribes is operational')
    print('✅ Genesis Voice Scrolls ready for voting')
else:
    print(f'⚠️ Only {reps[0][\"count\"]}/12 representatives elected')
"
```

### **Phase 3: Initial Governance Actions**

#### Step 1: Vote on Genesis Voice Scrolls
The first actions of the Council should be to vote on the three Genesis Voice Scrolls:

1. **SCROLL_001_COUNCIL_RATIFICATION** - Ratify Council of Twelve Tribes
2. **SCROLL_002_GOVERNANCE_QUORUM** - Establish Governance Quorum Rules  
3. **SCROLL_003_BIBLICAL_TOKENOMICS** - Initialize Biblical Tokenomics Parameters

#### Step 2: Establish Ongoing Governance
- Set regular council meeting schedule
- Define proposal submission process
- Establish inter-tribal communication protocols
- Begin community onboarding for general population

---

## 🔐 Security and Immutability Verification

### **Genesis Block Immutability Checks**

#### Verify Covenant Hash Integrity
```bash
python -c "
import hashlib
with open('docs/Genesis Covenant of Onnyx.md', 'r', encoding='utf-8') as f:
    covenant_text = f.read()
calculated_hash = hashlib.sha3_256(covenant_text.encode('utf-8')).hexdigest()

from shared.db.db import db
import json
tx = db.query_one('SELECT * FROM transactions WHERE block_height = 0')
stored_hash = json.loads(tx['data'])['covenant_text_hash']

if calculated_hash == stored_hash:
    print('✅ Covenant hash integrity verified')
    print(f'   Hash: {calculated_hash}')
else:
    print('❌ Covenant hash mismatch!')
    print(f'   Calculated: {calculated_hash}')
    print(f'   Stored: {stored_hash}')
"
```

#### Verify Block Chain Integrity
```bash
python -c "
from shared.db.db import db

blocks = db.query('SELECT * FROM blocks ORDER BY block_height ASC LIMIT 10')
for i, block in enumerate(blocks):
    if i == 0:
        # Genesis block
        if block['previous_hash'] == '0x0000000000000000000000000000000000000000000000000000000000000000':
            print(f'✅ Genesis block (height {block[\"block_height\"]}) properly initialized')
        else:
            print(f'❌ Genesis block has invalid previous hash')
    else:
        # Subsequent blocks
        prev_block = blocks[i-1]
        if block['previous_hash'] == prev_block['block_hash']:
            print(f'✅ Block {block[\"block_height\"]} properly linked')
        else:
            print(f'❌ Block {block[\"block_height\"]} chain integrity broken')
"
```

---

## 📊 Success Metrics and Monitoring

### **Deployment Success Criteria**

- [ ] Genesis block created with height 0
- [ ] Covenant hash permanently sealed and immutable
- [ ] All 47 nations registered in database
- [ ] OP_COVENANT_FOUNDING transaction validated
- [ ] Biblical tokenomics system operational
- [ ] All 12 tribal representatives elected
- [ ] Genesis Voice Scrolls deployed and ready for voting
- [ ] Covenant comprehension test system functional

### **Ongoing Monitoring**

#### Daily Checks
```bash
# Check blockchain integrity
python scripts/blockchain_verification.py

# Check governance activity
python -c "
from shared.db.db import db
scrolls = db.query('SELECT COUNT(*) as count FROM voice_scrolls WHERE status = \"active\"')
print(f'Active Voice Scrolls: {scrolls[0][\"count\"]}')
"

# Check community growth
python -c "
from shared.db.db import db
identities = db.query('SELECT verification_level, COUNT(*) as count FROM identities GROUP BY verification_level')
for level in identities:
    print(f'Tier {level[\"verification_level\"]} identities: {level[\"count\"]}')
"
```

#### Weekly Reports
- Tribal council meeting minutes
- Voice Scroll voting results
- Community growth metrics
- Biblical tokenomics compliance
- Security audit results

---

## 🎉 Genesis Deployment Complete

Upon successful completion of all phases:

1. **Genesis Block**: Immutably sealed with covenant hash
2. **47 Nations**: All biblical nations registered and categorized
3. **Tribal Council**: 12 representatives elected and operational
4. **Governance**: Voice Scroll system active with initial proposals
5. **Community**: Onboarding system ready for general population
6. **Economics**: Biblical tokenomics enforcing covenant principles

**The ONNYX blockchain is now operational and ready to serve as the foundation for a biblical covenant community.**

---

## 📞 Support and Troubleshooting

### Common Issues
- **Database connection errors**: Verify SQLite database exists and is accessible
- **Nation registration failures**: Check biblical_nations table schema
- **Covenant hash mismatches**: Ensure Genesis Covenant document hasn't been modified
- **Tribal election issues**: Verify identity verification levels and CIPP status

### Emergency Procedures
- **Genesis block corruption**: Restore from backup and re-verify covenant hash
- **Governance system failure**: Activate emergency council procedures
- **Security breaches**: Implement vault freeze protocols via CIPP system

**For technical support, contact the Genesis Council or refer to the ONNYX technical documentation.**
