{% extends "base.html" %}

{% block title %}Genesis Identity Created - ONNYX Platform Founder{% endblock %}

{% block content %}
<div class="min-h-screen hero-gradient cyber-grid relative py-20">
    <!-- Enhanced floating particles -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute top-1/4 left-1/4 w-4 h-4 bg-cyber-cyan rounded-full animate-ping opacity-70"></div>
        <div class="absolute top-1/3 right-1/3 w-3 h-3 bg-cyber-purple rounded-full animate-pulse opacity-60"></div>
        <div class="absolute bottom-1/4 left-1/3 w-3.5 h-3.5 bg-cyber-blue rounded-full animate-bounce opacity-50"></div>
        <div class="absolute top-1/2 right-1/4 w-2.5 h-2.5 bg-green-400 rounded-full animate-pulse opacity-40"></div>
        <div class="absolute bottom-1/3 right-1/2 w-2 h-2 bg-yellow-400 rounded-full animate-ping opacity-30"></div>
    </div>

    <div class="container-lg relative z-10">
        <!-- Success Header -->
        <div class="text-center mb-12">
            <!-- ONNYX Logo -->
            <div class="mb-8 flex justify-center">
                <div class="w-24 h-24 md:w-28 md:h-28 rounded-2xl flex items-center justify-center shadow-2xl shadow-cyber-cyan/50 bg-white/5 backdrop-blur-sm border border-white/20 hover:shadow-cyber-cyan/70 transition-all duration-500 group">
                    <img src="{{ url_for('static', filename='images/onnyx_logo.png') }}"
                         alt="ONNYX Logo"
                         class="w-16 h-16 md:w-20 md:h-20 object-contain group-hover:scale-110 transition-all duration-500"
                         style="filter: brightness(0) saturate(100%) invert(64%) sepia(100%) saturate(2000%) hue-rotate(180deg) brightness(100%) contrast(100%);">
                </div>
            </div>

            <!-- Success Icon -->
            <div class="w-32 h-32 bg-gradient-to-br from-cyber-cyan via-cyber-purple to-cyber-blue rounded-xl flex items-center justify-center mx-auto mb-8 glow-effect shadow-cyber animate-pulse">
                <svg class="w-16 h-16 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                </svg>
            </div>

            <h1 class="text-4xl md:text-6xl font-orbitron font-bold mb-6">
                <span class="hologram-text">🌟 Genesis Identity Created!</span>
            </h1>
            <p class="text-xl md:text-2xl text-text-secondary max-w-4xl mx-auto leading-relaxed mb-8">
                Welcome, Platform Founder <strong class="text-cyber-cyan">{{ identity.name }}</strong>! 
                You have successfully established the foundational identity for the ONNYX blockchain network.
            </p>
        </div>

        <!-- Genesis Block Information -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
            <!-- Identity Information -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title text-cyber-cyan">🔐 Genesis Identity Details</h3>
                    <p class="card-subtitle">Your Platform Founder credentials</p>
                </div>
                <div class="card-body">
                    <div class="space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="text-text-muted">Identity ID:</span>
                            <span class="font-mono text-cyber-cyan text-sm">{{ identity.identity_id[:16] }}...</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-text-muted">Name:</span>
                            <span class="text-text-primary">{{ identity.name }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-text-muted">Email:</span>
                            <span class="text-text-primary">{{ identity.email }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-text-muted">Role:</span>
                            <span class="text-cyber-purple font-semibold">Platform Founder</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-text-muted">Status:</span>
                            <span class="text-green-400 font-semibold">Genesis Identity ✓</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Genesis Block Information -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title text-cyber-purple">⛓️ Genesis Block #0</h3>
                    <p class="card-subtitle">Blockchain foundation established</p>
                </div>
                <div class="card-body">
                    <div class="space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="text-text-muted">Block Number:</span>
                            <span class="font-mono text-cyber-purple">#0 (Genesis)</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-text-muted">Timestamp:</span>
                            <span class="text-text-primary">{{ genesis_block_data.created_at | format_timestamp }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-text-muted">Previous Hash:</span>
                            <span class="font-mono text-text-tertiary text-sm">0000...0000</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-text-muted">Founder:</span>
                            <span class="text-text-primary">{{ genesis_block_data.founder_name }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-text-muted">Network Status:</span>
                            <span class="text-green-400 font-semibold">Initialized ✓</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Critical Security Section -->
        <div class="card border border-yellow-500/40 bg-yellow-500/10 mb-8">
            <div class="card-header">
                <div class="flex items-center gap-4">
                    <div class="w-12 h-12 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-lg flex items-center justify-center">
                        <svg class="w-7 h-7 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="card-title text-yellow-400">🔑 CRITICAL: Download Your Private Key</h3>
                        <p class="card-subtitle text-yellow-200">This is your only chance to download your Genesis Identity private key</p>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="space-y-6">
                    <p class="text-yellow-200 leading-relaxed">
                        <strong>Your Genesis Identity private key is displayed below.</strong> This key controls the entire ONNYX platform and cannot be recovered if lost. 
                        You must download and store it securely in multiple locations immediately.
                    </p>
                    
                    <!-- Key Download Section -->
                    <div class="bg-onyx-black/50 p-6 rounded-lg border border-yellow-500/30">
                        <div class="flex justify-between items-center mb-4">
                            <h4 class="font-orbitron font-bold text-yellow-400">Genesis Identity Key File</h4>
                            <button onclick="downloadKey()" class="btn btn-primary btn-sm">
                                <span>📥</span>
                                <span>Download Key</span>
                            </button>
                        </div>
                        <pre class="text-xs font-mono text-gray-300 bg-onyx-gray/50 p-4 rounded border overflow-x-auto">{{ key_data }}</pre>
                    </div>

                    <!-- Security Checklist -->
                    <div class="space-y-3">
                        <h4 class="font-orbitron font-bold text-yellow-400 mb-3">Security Checklist:</h4>
                        <label class="form-check">
                            <input type="checkbox" class="form-check-input" id="downloaded">
                            <span class="form-check-label text-yellow-200">I have downloaded the key file to a secure location</span>
                        </label>
                        <label class="form-check">
                            <input type="checkbox" class="form-check-input" id="backed-up">
                            <span class="form-check-label text-yellow-200">I have created multiple backup copies in different locations</span>
                        </label>
                        <label class="form-check">
                            <input type="checkbox" class="form-check-input" id="understood">
                            <span class="form-check-label text-yellow-200">I understand this key cannot be recovered if lost</span>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Next Steps -->
        <div class="card mb-8">
            <div class="card-header">
                <h3 class="card-title text-cyber-blue">🚀 Next Steps as Platform Founder</h3>
                <p class="card-subtitle">Your journey to building the ONNYX network begins now</p>
            </div>
            <div class="card-body">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-4">
                        <h4 class="font-orbitron font-bold text-cyber-cyan">Immediate Actions:</h4>
                        <ul class="space-y-2 text-text-secondary">
                            <li class="flex items-center gap-2">
                                <span class="w-2 h-2 bg-cyber-cyan rounded-full"></span>
                                <span>Secure your private key in multiple locations</span>
                            </li>
                            <li class="flex items-center gap-2">
                                <span class="w-2 h-2 bg-cyber-cyan rounded-full"></span>
                                <span>Access your Platform Founder dashboard</span>
                            </li>
                            <li class="flex items-center gap-2">
                                <span class="w-2 h-2 bg-cyber-cyan rounded-full"></span>
                                <span>Register your first business validators</span>
                            </li>
                        </ul>
                    </div>
                    <div class="space-y-4">
                        <h4 class="font-orbitron font-bold text-cyber-purple">Phase 1 Goals:</h4>
                        <ul class="space-y-2 text-text-secondary">
                            <li class="flex items-center gap-2">
                                <span class="w-2 h-2 bg-cyber-purple rounded-full"></span>
                                <span>Onboard GetTwisted Hair Studios</span>
                            </li>
                            <li class="flex items-center gap-2">
                                <span class="w-2 h-2 bg-cyber-purple rounded-full"></span>
                                <span>Register catering business validator</span>
                            </li>
                            <li class="flex items-center gap-2">
                                <span class="w-2 h-2 bg-cyber-purple rounded-full"></span>
                                <span>Prepare for public launch</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="{{ url_for('dashboard.overview') }}" class="btn btn-primary btn-lg">
                <span class="text-xl">📊</span>
                <span>Access Founder Dashboard</span>
            </a>
            <a href="{{ url_for('auth.register_sela') }}" class="btn btn-secondary btn-lg">
                <span class="text-xl">🏢</span>
                <span>Register First Validator</span>
            </a>
            <a href="{{ url_for('sela.directory') }}" class="btn btn-outline btn-lg">
                <span class="text-xl">🌐</span>
                <span>View Network</span>
            </a>
        </div>
    </div>
</div>

<script>
function downloadKey() {
    const keyData = {{ key_data | safe }};
    const blob = new Blob([keyData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `onnyx-genesis-identity-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    // Mark as downloaded
    document.getElementById('downloaded').checked = true;
    
    // Show success message
    if (typeof Onnyx !== 'undefined' && Onnyx.utils && Onnyx.utils.showNotification) {
        Onnyx.utils.showNotification('Genesis Identity key downloaded successfully!', 'success');
    }
}

// Auto-focus on security checklist
document.addEventListener('DOMContentLoaded', function() {
    const firstCheckbox = document.getElementById('downloaded');
    if (firstCheckbox) {
        firstCheckbox.focus();
    }
});
</script>
{% endblock %}
