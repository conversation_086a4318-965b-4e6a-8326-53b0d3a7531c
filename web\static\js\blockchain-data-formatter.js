/**
 * ONNYX Blockchain Data Formatter
 * Consistent formatting and display utilities for blockchain data across the platform
 */

class BlockchainDataFormatter {
    constructor() {
        this.init();
    }

    init() {
        console.log('🔗 ONNYX Blockchain Data Formatter Initialized');
        this.formatAllMetrics();
        this.setupAutoRefresh();
        this.addLoadingStates();
    }

    /**
     * Format numeric values with proper units and decimals
     */
    formatNumber(value, options = {}) {
        const {
            decimals = 2,
            unit = '',
            prefix = '',
            suffix = '',
            compact = false,
            currency = false
        } = options;

        if (value === null || value === undefined || isNaN(value)) {
            return '—';
        }

        let formattedValue = parseFloat(value);

        // Compact notation for large numbers
        if (compact && formattedValue >= 1000) {
            if (formattedValue >= 1000000000) {
                formattedValue = (formattedValue / 1000000000).toFixed(1) + 'B';
            } else if (formattedValue >= 1000000) {
                formattedValue = (formattedValue / 1000000).toFixed(1) + 'M';
            } else if (formattedValue >= 1000) {
                formattedValue = (formattedValue / 1000).toFixed(1) + 'K';
            }
        } else {
            formattedValue = formattedValue.toLocaleString('en-US', {
                minimumFractionDigits: decimals,
                maximumFractionDigits: decimals
            });
        }

        return `${prefix}${formattedValue}${unit}${suffix}`;
    }

    /**
     * Format hash values with truncation
     */
    formatHash(hash, options = {}) {
        const { length = 8, showFull = false } = options;
        
        if (!hash) return '—';
        
        if (showFull || hash.length <= length * 2) {
            return hash;
        }
        
        return `${hash.substring(0, length)}...${hash.substring(hash.length - length)}`;
    }

    /**
     * Format timestamps consistently
     */
    formatTimestamp(timestamp, options = {}) {
        const { relative = true, format = 'short' } = options;
        
        if (!timestamp) return '—';
        
        const date = new Date(timestamp);
        const now = new Date();
        const diff = now - date;
        
        if (relative && diff < 86400000) { // Less than 24 hours
            if (diff < 60000) return 'Just now';
            if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`;
            return `${Math.floor(diff / 3600000)}h ago`;
        }
        
        return date.toLocaleDateString('en-US', {
            year: format === 'long' ? 'numeric' : '2-digit',
            month: format === 'long' ? 'long' : 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    /**
     * Format percentage values
     */
    formatPercentage(value, decimals = 1) {
        if (value === null || value === undefined || isNaN(value)) {
            return '—';
        }
        return `${parseFloat(value).toFixed(decimals)}%`;
    }

    /**
     * Format mining power display
     */
    formatMiningPower(power, tier = 'basic') {
        if (!power) return '1.0x';
        
        const tierMultipliers = {
            'basic': 1,
            'advanced': 2,
            'professional': 5,
            'enterprise': 10
        };
        
        const multiplier = tierMultipliers[tier.toLowerCase()] || 1;
        const totalPower = parseFloat(power) * multiplier;
        
        return `${totalPower.toFixed(1)}x`;
    }

    /**
     * Create status indicator element
     */
    createStatusIndicator(status, text = null) {
        const indicator = document.createElement('span');
        indicator.className = `status-indicator status-${status.toLowerCase()}`;
        
        const statusIcons = {
            'active': '●',
            'pending': '◐',
            'inactive': '○',
            'error': '✕'
        };
        
        const icon = statusIcons[status.toLowerCase()] || '●';
        indicator.innerHTML = `${icon} ${text || status.toUpperCase()}`;
        
        return indicator;
    }

    /**
     * Apply loading state to elements
     */
    addLoadingStates() {
        const metrics = document.querySelectorAll('[data-metric]');
        metrics.forEach(metric => {
            if (!metric.textContent.trim() || metric.textContent === '0') {
                metric.classList.add('metric-loading');
                metric.textContent = '';
            }
        });
    }

    /**
     * Remove loading states
     */
    removeLoadingStates() {
        const loadingElements = document.querySelectorAll('.metric-loading');
        loadingElements.forEach(element => {
            element.classList.remove('metric-loading');
        });
    }

    /**
     * Format all metrics on the page
     */
    formatAllMetrics() {
        // Format currency values
        document.querySelectorAll('[data-format="currency"]').forEach(element => {
            const value = parseFloat(element.dataset.value || element.textContent);
            element.textContent = this.formatNumber(value, { 
                decimals: 2, 
                prefix: '', 
                suffix: ' ONX' 
            });
        });

        // Format percentages
        document.querySelectorAll('[data-format="percentage"]').forEach(element => {
            const value = parseFloat(element.dataset.value || element.textContent);
            element.textContent = this.formatPercentage(value);
        });

        // Format hashes
        document.querySelectorAll('[data-format="hash"]').forEach(element => {
            const hash = element.dataset.value || element.textContent;
            element.textContent = this.formatHash(hash);
            element.title = hash; // Show full hash on hover
        });

        // Format timestamps
        document.querySelectorAll('[data-format="timestamp"]').forEach(element => {
            const timestamp = element.dataset.value || element.textContent;
            element.textContent = this.formatTimestamp(timestamp);
        });

        // Format large numbers
        document.querySelectorAll('[data-format="number"]').forEach(element => {
            const value = parseFloat(element.dataset.value || element.textContent);
            const compact = element.dataset.compact === 'true';
            element.textContent = this.formatNumber(value, { compact });
        });

        // Format mining power
        document.querySelectorAll('[data-format="mining-power"]').forEach(element => {
            const power = element.dataset.value || element.textContent;
            const tier = element.dataset.tier || 'basic';
            element.textContent = this.formatMiningPower(power, tier);
        });
    }

    /**
     * Setup auto-refresh for dynamic data
     */
    setupAutoRefresh() {
        // Refresh timestamps every minute
        setInterval(() => {
            document.querySelectorAll('[data-format="timestamp"]').forEach(element => {
                const timestamp = element.dataset.value;
                if (timestamp) {
                    element.textContent = this.formatTimestamp(timestamp);
                }
            });
        }, 60000);

        console.log('🔄 Auto-refresh enabled for blockchain data');
    }

    /**
     * Update metric value with animation
     */
    updateMetric(element, newValue, options = {}) {
        if (!element) return;

        const oldValue = parseFloat(element.textContent.replace(/[^\d.-]/g, '')) || 0;
        const targetValue = parseFloat(newValue) || 0;

        // Add loading state briefly
        element.classList.add('metric-loading');
        
        setTimeout(() => {
            element.classList.remove('metric-loading');
            
            // Format and update value
            const format = element.dataset.format;
            switch (format) {
                case 'currency':
                    element.textContent = this.formatNumber(targetValue, { 
                        decimals: 2, 
                        suffix: ' ONX' 
                    });
                    break;
                case 'percentage':
                    element.textContent = this.formatPercentage(targetValue);
                    break;
                case 'number':
                    const compact = element.dataset.compact === 'true';
                    element.textContent = this.formatNumber(targetValue, { compact });
                    break;
                default:
                    element.textContent = targetValue;
            }

            // Add update animation
            element.style.transform = 'scale(1.05)';
            element.style.transition = 'transform 0.2s ease';
            
            setTimeout(() => {
                element.style.transform = 'scale(1)';
            }, 200);
            
        }, 300);
    }

    /**
     * Batch update multiple metrics
     */
    updateMetrics(updates) {
        Object.entries(updates).forEach(([selector, value]) => {
            const element = document.querySelector(selector);
            if (element) {
                this.updateMetric(element, value);
            }
        });
    }
}

// Initialize the formatter when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.blockchainFormatter = new BlockchainDataFormatter();
    });
} else {
    window.blockchainFormatter = new BlockchainDataFormatter();
}

// Export for use in other scripts
window.BlockchainDataFormatter = BlockchainDataFormatter;
