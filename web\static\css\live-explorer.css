/**
 * ONNYX Live Explorer Styles
 * Cyberpunk aesthetic with glass morphism effects
 */

/* Live Explorer Layout */
.live-explorer {
    padding: 24px;
    max-width: 1536px;
    margin: 0 auto;
    min-height: 100vh;
}

.explorer-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    margin-bottom: 32px;
}

.explorer-section {
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(16px);
    border: 1px solid rgba(0, 212, 255, 0.2);
    border-radius: 16px;
    padding: 24px;
    position: relative;
    overflow: hidden;
}

.explorer-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, #00d4ff, transparent);
    opacity: 0.6;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid rgba(0, 212, 255, 0.1);
}

.section-title {
    font-family: 'Orbitron', monospace;
    font-size: 1.25rem;
    font-weight: 600;
    color: #00d4ff;
    text-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
}

.section-controls {
    display: flex;
    gap: 8px;
    align-items: center;
}

.refresh-button {
    background: rgba(0, 212, 255, 0.1);
    border: 1px solid rgba(0, 212, 255, 0.3);
    border-radius: 8px;
    padding: 8px 12px;
    color: #00d4ff;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 44px;
    min-width: 44px;
}

.refresh-button:hover {
    background: rgba(0, 212, 255, 0.2);
    border-color: rgba(0, 212, 255, 0.5);
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
}

.auto-refresh-toggle {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.875rem;
    color: #a0a0a0;
}

/* Loading States */
.loading {
    position: relative;
    min-height: 200px;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    margin: -20px 0 0 -20px;
    border: 3px solid rgba(0, 212, 255, 0.1);
    border-top: 3px solid #00d4ff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Block Items */
.block-item {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(0, 212, 255, 0.1);
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 12px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.block-item:hover {
    border-color: rgba(0, 212, 255, 0.4);
    background: rgba(0, 0, 0, 0.5);
    transform: translateY(-2px);
    box-shadow: 0 8px 32px rgba(0, 212, 255, 0.1);
}

.block-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.block-height {
    font-family: 'Orbitron', monospace;
    font-weight: 600;
    color: #00d4ff;
    font-size: 1.1rem;
}

.block-age {
    color: #a0a0a0;
    font-size: 0.875rem;
}

.block-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.block-hash {
    font-family: 'JetBrains Mono', monospace;
    color: #e0e0e0;
    font-size: 0.875rem;
    word-break: break-all;
}

.block-miner {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.miner-label {
    color: #a0a0a0;
    font-size: 0.875rem;
}

.miner-id {
    font-family: 'JetBrains Mono', monospace;
    color: #e0e0e0;
    font-size: 0.875rem;
}

.block-stats {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
}

.block-stats > span {
    color: #a0a0a0;
    font-size: 0.875rem;
}

/* Transaction Items */
.transaction-item {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(154, 0, 255, 0.1);
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 12px;
    transition: all 0.3s ease;
}

.transaction-item:hover {
    border-color: rgba(154, 0, 255, 0.4);
    background: rgba(0, 0, 0, 0.5);
    transform: translateY(-2px);
    box-shadow: 0 8px 32px rgba(154, 0, 255, 0.1);
}

.tx-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.tx-hash {
    font-family: 'JetBrains Mono', monospace;
    color: #9a00ff;
    font-weight: 600;
}

.tx-age {
    color: #a0a0a0;
    font-size: 0.875rem;
}

.tx-flow {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
    flex-wrap: wrap;
}

.tx-sender, .tx-receiver {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    min-width: 120px;
}

.tx-arrow {
    color: #00d4ff;
    font-weight: bold;
    font-size: 1.2rem;
}

.address {
    font-family: 'JetBrains Mono', monospace;
    color: #e0e0e0;
    font-size: 0.875rem;
}

.tx-stats {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
    align-items: center;
}

.tx-amount {
    color: #00d4ff;
    font-weight: 600;
    font-family: 'Orbitron', monospace;
}

.tx-type {
    background: rgba(0, 128, 255, 0.2);
    color: #0080ff;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 500;
}

.tx-confirmations {
    color: #a0a0a0;
    font-size: 0.875rem;
}

.tx-status {
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-confirmed {
    background: rgba(0, 255, 0, 0.2);
    color: #00ff00;
}

.status-pending {
    background: rgba(255, 165, 0, 0.2);
    color: #ffa500;
}

.status-failed {
    background: rgba(255, 0, 0, 0.2);
    color: #ff0000;
}

/* Tribal Badges */
.tribal-badge {
    background: rgba(0, 212, 255, 0.2);
    color: #00d4ff;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    border: 1px solid rgba(0, 212, 255, 0.3);
}

.tribal-judah { background: rgba(255, 215, 0, 0.2); color: #ffd700; border-color: rgba(255, 215, 0, 0.3); }
.tribal-levi { background: rgba(138, 43, 226, 0.2); color: #8a2be2; border-color: rgba(138, 43, 226, 0.3); }
.tribal-ephraim { background: rgba(34, 139, 34, 0.2); color: #228b22; border-color: rgba(34, 139, 34, 0.3); }
.tribal-benjamin { background: rgba(255, 69, 0, 0.2); color: #ff4500; border-color: rgba(255, 69, 0, 0.3); }
.tribal-dan { background: rgba(220, 20, 60, 0.2); color: #dc143c; border-color: rgba(220, 20, 60, 0.3); }

/* Mining Stats */
.mining-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.stat-card {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(0, 212, 255, 0.1);
    border-radius: 12px;
    padding: 16px;
    text-align: center;
    transition: all 0.3s ease;
}

.stat-card:hover {
    border-color: rgba(0, 212, 255, 0.3);
    background: rgba(0, 0, 0, 0.5);
}

.stat-label {
    color: #a0a0a0;
    font-size: 0.875rem;
    margin-bottom: 8px;
    display: block;
}

.stat-value {
    color: #00d4ff;
    font-family: 'Orbitron', monospace;
    font-size: 1.5rem;
    font-weight: 600;
    text-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
}

/* Sabbath Status */
.sabbath-indicator {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 8px;
    font-weight: 500;
}

.sabbath-active {
    background: rgba(255, 215, 0, 0.2);
    color: #ffd700;
    border: 1px solid rgba(255, 215, 0, 0.3);
}

.sabbath-inactive {
    background: rgba(0, 212, 255, 0.2);
    color: #00d4ff;
    border: 1px solid rgba(0, 212, 255, 0.3);
}

.compliance-status {
    margin-left: 12px;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 0.875rem;
}

.compliant {
    background: rgba(0, 255, 0, 0.2);
    color: #00ff00;
}

.non-compliant {
    background: rgba(255, 0, 0, 0.2);
    color: #ff0000;
}

/* Network Status Indicators */
.consensus-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.status-healthy .status-dot {
    background: #00ff00;
    box-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
}

.status-stable .status-dot {
    background: #ffa500;
    box-shadow: 0 0 10px rgba(255, 165, 0, 0.5);
}

.status-slow .status-dot {
    background: #ff0000;
    box-shadow: 0 0 10px rgba(255, 0, 0, 0.5);
}

.status-unknown .status-dot {
    background: #a0a0a0;
}

.status-text {
    font-family: 'Orbitron', monospace;
    font-weight: 600;
    text-transform: uppercase;
}

.health-bar, .progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 4px;
    overflow: hidden;
    margin-top: 8px;
}

.health-fill {
    height: 100%;
    background: linear-gradient(90deg, #ff0000, #ffa500, #00ff00);
    transition: width 0.5s ease;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #00d4ff, #9a00ff);
    transition: width 0.5s ease;
}

.sync-indicator {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.sync-text {
    font-family: 'Orbitron', monospace;
    font-weight: 600;
    text-transform: uppercase;
}

.sync-progress {
    color: #00d4ff;
    font-weight: 600;
}

/* Tribal Distribution Charts */
.tribal-distribution-item {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
    padding: 8px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.tribal-distribution-item:hover {
    background: rgba(0, 0, 0, 0.4);
    transform: translateX(4px);
}

.tribe-info {
    min-width: 80px;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.tribe-name {
    font-weight: 600;
    color: #e0e0e0;
    font-size: 0.875rem;
}

.tribe-percentage {
    color: #00d4ff;
    font-size: 0.75rem;
    font-family: 'Orbitron', monospace;
}

.tribe-bar {
    flex: 1;
    height: 8px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 4px;
    overflow: hidden;
}

.tribe-fill {
    height: 100%;
    transition: width 0.5s ease;
    background: linear-gradient(90deg, #00d4ff, #9a00ff);
}

.tribe-count {
    min-width: 40px;
    text-align: right;
    color: #a0a0a0;
    font-size: 0.875rem;
    font-family: 'Orbitron', monospace;
}

/* Governance Activity */
.gov-metric {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(0, 212, 255, 0.1);
}

.gov-metric:last-child {
    border-bottom: none;
}

.metric-label {
    color: #a0a0a0;
    font-size: 0.875rem;
}

.metric-value {
    color: #e0e0e0;
    font-weight: 600;
    font-family: 'Orbitron', monospace;
}

.metric-value.positive {
    color: #00ff00;
}

.metric-value.negative {
    color: #ff0000;
}

/* Error States */
.error-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    padding: 32px;
    text-align: center;
    color: #a0a0a0;
}

.error-icon {
    font-size: 2rem;
}

.error-text {
    font-size: 1rem;
    margin-bottom: 8px;
}

.retry-button {
    background: rgba(0, 212, 255, 0.1);
    border: 1px solid rgba(0, 212, 255, 0.3);
    border-radius: 8px;
    padding: 12px 24px;
    color: #00d4ff;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    min-height: 44px;
}

.retry-button:hover {
    background: rgba(0, 212, 255, 0.2);
    border-color: rgba(0, 212, 255, 0.5);
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
}

/* Animations */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes fade-in {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slide-in {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.fade-in {
    animation: fade-in 0.5s ease forwards;
}

.slide-in {
    animation: slide-in 0.3s ease forwards;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .explorer-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .mining-stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 12px;
    }
}

@media (max-width: 768px) {
    .live-explorer {
        padding: 16px;
    }

    .explorer-section {
        padding: 16px;
    }

    .section-header {
        flex-direction: column;
        gap: 12px;
        align-items: flex-start;
    }

    .section-controls {
        width: 100%;
        justify-content: space-between;
    }

    .tx-flow {
        flex-direction: column;
        gap: 8px;
    }

    .tx-arrow {
        transform: rotate(90deg);
    }

    .block-stats, .tx-stats {
        flex-direction: column;
        gap: 8px;
    }

    .mining-stats-grid {
        grid-template-columns: 1fr;
    }

    .tribal-distribution-item {
        flex-direction: column;
        gap: 8px;
        align-items: stretch;
    }

    .tribe-info {
        flex-direction: row;
        justify-content: space-between;
        min-width: auto;
    }
}

@media (max-width: 480px) {
    .stat-value {
        font-size: 1.25rem;
    }

    .section-title {
        font-size: 1.1rem;
    }

    .block-header, .tx-header {
        flex-direction: column;
        gap: 8px;
        align-items: flex-start;
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .status-dot {
        animation: none;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .explorer-section {
        border-color: #00d4ff;
        background: rgba(0, 0, 0, 0.8);
    }

    .block-item, .transaction-item, .stat-card {
        border-color: #00d4ff;
        background: rgba(0, 0, 0, 0.6);
    }
}
