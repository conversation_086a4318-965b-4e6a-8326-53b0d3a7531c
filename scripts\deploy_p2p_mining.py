#!/usr/bin/env python3
"""
ONNYX P2P Mining Network Deployment Script
Deploys distributed mining nodes with biblical compliance
"""

import sys
import os
import asyncio
import json
import time
import logging
from typing import List, Dict

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from network.discovery.bootstrap import BootstrapNode
from network.nodes.tribal_elder_node import TribalElderNode, create_tribal_elder_configs
from network.nodes.mining_node import P2PMiningNode, create_mining_node_configs
from shared.db.db import db

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class P2PMiningDeployment:
    """Manages the deployment of the ONNYX P2P mining network"""
    
    def __init__(self):
        self.bootstrap_node = None
        self.tribal_elder_nodes: List[TribalElderNode] = []
        self.mining_nodes: List[P2PMiningNode] = []
        self.running = False
        
        # Network configuration
        self.bootstrap_port = 8766
        self.bootstrap_address = "127.0.0.1"
        
    async def deploy_mining_network(self) -> bool:
        """Deploy the complete ONNYX P2P mining network"""
        try:
            logger.info("🔥 DEPLOYING ONNYX P2P MINING NETWORK")
            logger.info("=" * 60)
            
            # Phase 1: Deploy Bootstrap Node
            logger.info("📡 Phase 1: Deploying Bootstrap Node...")
            if not await self._deploy_bootstrap_node():
                logger.error("Failed to deploy bootstrap node")
                return False
            
            # Phase 2: Deploy Tribal Elder Nodes (for validation)
            logger.info("👑 Phase 2: Deploying Tribal Elder Validators...")
            if not await self._deploy_tribal_elder_nodes():
                logger.error("Failed to deploy tribal elder nodes")
                return False
            
            # Phase 3: Deploy P2P Mining Nodes
            logger.info("⛏️ Phase 3: Deploying P2P Mining Nodes...")
            if not await self._deploy_mining_nodes():
                logger.error("Failed to deploy mining nodes")
                return False
            
            # Phase 4: Start Mining Coordination
            logger.info("🎯 Phase 4: Starting Mining Coordination...")
            if not await self._start_mining_coordination():
                logger.error("Failed to start mining coordination")
                return False
            
            # Phase 5: Verify Mining Network Health
            logger.info("🔍 Phase 5: Verifying Mining Network Health...")
            if not await self._verify_mining_network_health():
                logger.error("Mining network health verification failed")
                return False
            
            self.running = True
            logger.info("🎉 ONNYX P2P MINING NETWORK DEPLOYMENT COMPLETE!")
            return True
            
        except Exception as e:
            logger.error(f"Mining network deployment error: {e}")
            return False
    
    async def _deploy_bootstrap_node(self) -> bool:
        """Deploy the bootstrap node for peer discovery"""
        try:
            self.bootstrap_node = BootstrapNode(
                node_id="bootstrap_mining",
                port=self.bootstrap_port
            )
            
            await self.bootstrap_node.start()
            await asyncio.sleep(2)
            
            logger.info(f"✅ Bootstrap node deployed on port {self.bootstrap_port}")
            return True
            
        except Exception as e:
            logger.error(f"Bootstrap node deployment error: {e}")
            return False
    
    async def _deploy_tribal_elder_nodes(self) -> bool:
        """Deploy tribal elder nodes for mining validation"""
        try:
            # Deploy a subset of tribal elders (8 minimum for consensus)
            elder_configs = create_tribal_elder_configs()[:8]  # Deploy 8 elders
            
            for config in elder_configs:
                try:
                    elder_node = TribalElderNode(config)
                    
                    success = await elder_node.start()
                    
                    if success:
                        self.tribal_elder_nodes.append(elder_node)
                        logger.info(f"✅ Deployed {config.elder_name} ({config.tribal_code}) on port {config.port}")
                    else:
                        logger.error(f"❌ Failed to deploy {config.elder_name}")
                        return False
                    
                    await asyncio.sleep(1)
                    
                except Exception as e:
                    logger.error(f"Error deploying elder {config.tribal_code}: {e}")
                    return False
            
            logger.info(f"✅ Deployed {len(self.tribal_elder_nodes)}/8 tribal elder validators")
            
            # Wait for elders to connect
            await asyncio.sleep(5)
            
            return len(self.tribal_elder_nodes) >= 8
            
        except Exception as e:
            logger.error(f"Tribal elder deployment error: {e}")
            return False
    
    async def _deploy_mining_nodes(self) -> bool:
        """Deploy P2P mining nodes"""
        try:
            # Create mining node configurations
            mining_configs = create_mining_node_configs(count=5)
            
            for config in mining_configs:
                try:
                    mining_node = P2PMiningNode(config)
                    
                    success = await mining_node.start()
                    
                    if success:
                        self.mining_nodes.append(mining_node)
                        logger.info(f"✅ Deployed mining node: {config.miner_identity} ({config.tribal_code}) on port {config.port}")
                    else:
                        logger.warning(f"⚠️ Failed to deploy mining node: {config.miner_identity}")
                    
                    await asyncio.sleep(2)  # Longer pause for mining nodes
                    
                except Exception as e:
                    logger.error(f"Error deploying mining node {config.miner_identity}: {e}")
            
            logger.info(f"✅ Deployed {len(self.mining_nodes)} P2P mining nodes")
            return len(self.mining_nodes) > 0
            
        except Exception as e:
            logger.error(f"Mining node deployment error: {e}")
            return False
    
    async def _start_mining_coordination(self) -> bool:
        """Start mining coordination across the network"""
        try:
            # Wait for all nodes to connect and sync
            await asyncio.sleep(10)
            
            # Check that mining nodes have started mining
            active_miners = 0
            for mining_node in self.mining_nodes:
                status = mining_node.get_node_status()
                if status.get("mining_status") == "mining":
                    active_miners += 1
            
            logger.info(f"✅ {active_miners}/{len(self.mining_nodes)} mining nodes are actively mining")
            
            # Simulate some mining activity
            await self._simulate_mining_activity()
            
            return active_miners > 0
            
        except Exception as e:
            logger.error(f"Mining coordination error: {e}")
            return False
    
    async def _simulate_mining_activity(self):
        """Simulate mining activity for demonstration"""
        try:
            logger.info("🎯 Simulating mining activity...")
            
            # Let miners run for a short period
            await asyncio.sleep(30)
            
            # Check mining statistics
            total_proposals = 0
            total_blocks = 0
            
            for mining_node in self.mining_nodes:
                stats = mining_node.get_mining_statistics()
                total_proposals += stats.get("proposals_submitted", 0)
                total_blocks += stats.get("blocks_mined", 0)
                
                logger.info(f"Miner {stats['miner_identity']}: "
                          f"Proposals: {stats.get('proposals_submitted', 0)}, "
                          f"Blocks: {stats.get('blocks_mined', 0)}, "
                          f"Compliance: {stats.get('biblical_compliance_score', 0):.2f}")
            
            logger.info(f"📊 Network Mining Activity: {total_proposals} proposals, {total_blocks} blocks")
            
        except Exception as e:
            logger.error(f"Mining activity simulation error: {e}")
    
    async def _verify_mining_network_health(self) -> bool:
        """Verify the health of the mining network"""
        try:
            logger.info("🔍 Verifying mining network health...")
            
            # Check bootstrap node
            if not self.bootstrap_node:
                logger.error("Bootstrap node not running")
                return False
            
            bootstrap_status = self.bootstrap_node.get_network_status()
            logger.info(f"📡 Bootstrap Node: {bootstrap_status['stats']['total_peers']} peers registered")
            
            # Check tribal elder nodes
            healthy_elders = 0
            for elder in self.tribal_elder_nodes:
                status = elder.get_node_status()
                if status["running"]:
                    healthy_elders += 1
            
            logger.info(f"👑 Tribal Elders: {healthy_elders}/{len(self.tribal_elder_nodes)} healthy")
            
            # Check mining nodes
            healthy_miners = 0
            active_miners = 0
            
            for mining_node in self.mining_nodes:
                status = mining_node.get_node_status()
                if status["running"]:
                    healthy_miners += 1
                    if status.get("mining_status") in ["mining", "proposing"]:
                        active_miners += 1
            
            logger.info(f"⛏️ Mining Nodes: {healthy_miners}/{len(self.mining_nodes)} healthy, {active_miners} actively mining")
            
            # Network health criteria
            min_healthy_elders = 6  # 3/4 of 8 elders
            min_healthy_miners = max(1, len(self.mining_nodes) // 2)
            min_active_miners = 1
            
            network_healthy = (
                healthy_elders >= min_healthy_elders and
                healthy_miners >= min_healthy_miners and
                active_miners >= min_active_miners
            )
            
            if network_healthy:
                logger.info("✅ Mining network health verification PASSED")
                await self._display_mining_network_statistics()
                return True
            else:
                logger.error("❌ Mining network health verification FAILED")
                logger.error(f"   Required: {min_healthy_elders} elders, {min_healthy_miners} miners, {min_active_miners} active")
                logger.error(f"   Actual: {healthy_elders} elders, {healthy_miners} miners, {active_miners} active")
                return False
            
        except Exception as e:
            logger.error(f"Mining network health verification error: {e}")
            return False
    
    async def _display_mining_network_statistics(self):
        """Display comprehensive mining network statistics"""
        try:
            logger.info("\n📊 MINING NETWORK STATISTICS")
            logger.info("-" * 40)
            
            # Bootstrap statistics
            if self.bootstrap_node:
                bootstrap_stats = self.bootstrap_node.get_network_status()
                logger.info(f"📡 Bootstrap Node:")
                logger.info(f"   Total Registered Peers: {bootstrap_stats['stats']['total_peers']}")
                logger.info(f"   Mining Nodes: {len(self.mining_nodes)}")
                logger.info(f"   Validator Nodes: {len(self.tribal_elder_nodes)}")
            
            # Tribal elder statistics
            logger.info(f"\n👑 Tribal Elder Validators ({len(self.tribal_elder_nodes)}):")
            total_elder_connections = 0
            for elder in self.tribal_elder_nodes:
                status = elder.get_node_status()
                logger.info(f"   {status['tribal_code']}: {status['connected_peers']} peers, Weight: {status['voting_weight']}")
                total_elder_connections += status['connected_peers']
            
            avg_elder_connections = total_elder_connections / max(len(self.tribal_elder_nodes), 1)
            logger.info(f"   Average Connections: {avg_elder_connections:.1f}")
            
            # Mining node statistics
            logger.info(f"\n⛏️ P2P Mining Nodes ({len(self.mining_nodes)}):")
            total_mining_power = 0
            total_proposals = 0
            total_blocks = 0
            total_rewards = 0.0
            
            for mining_node in self.mining_nodes:
                status = mining_node.get_node_status()
                stats = mining_node.get_mining_statistics()
                
                total_mining_power += stats['mining_power']
                total_proposals += stats.get('proposals_submitted', 0)
                total_blocks += stats.get('blocks_mined', 0)
                total_rewards += stats.get('total_rewards', 0)
                
                logger.info(f"   {stats['miner_identity'][:12]}... ({stats['tribal_code']}): "
                          f"Power: {stats['mining_power']:.1f}, "
                          f"Status: {status.get('mining_status', 'unknown')}, "
                          f"Compliance: {stats.get('biblical_compliance_score', 0):.2f}")
            
            logger.info(f"   Total Mining Power: {total_mining_power:.1f}")
            logger.info(f"   Total Proposals: {total_proposals}")
            logger.info(f"   Total Blocks Mined: {total_blocks}")
            logger.info(f"   Total Rewards: {total_rewards:.2f} ONX")
            
            # Network-wide mining statistics
            logger.info(f"\n🌐 Network Mining Overview:")
            total_nodes = 1 + len(self.tribal_elder_nodes) + len(self.mining_nodes)
            logger.info(f"   Total Network Nodes: {total_nodes}")
            logger.info(f"   Mining Architecture: Distributed P2P with Tribal Validation")
            logger.info(f"   Consensus Mechanism: Proof-of-Covenant")
            logger.info(f"   Biblical Compliance: Enforced (Anti-usury, Sabbath, Gleaning)")
            logger.info(f"   Mining Coordination: Decentralized with Elder Validation")
            
            # Biblical compliance statistics
            logger.info(f"\n📜 Biblical Compliance:")
            compliant_miners = 0
            avg_compliance = 0
            
            for mining_node in self.mining_nodes:
                stats = mining_node.get_mining_statistics()
                compliance = stats.get('biblical_compliance_score', 0)
                avg_compliance += compliance
                if compliance >= 0.8:
                    compliant_miners += 1
            
            avg_compliance = avg_compliance / max(len(self.mining_nodes), 1)
            logger.info(f"   Compliant Miners: {compliant_miners}/{len(self.mining_nodes)}")
            logger.info(f"   Average Compliance Score: {avg_compliance:.2f}")
            logger.info(f"   Sabbath Enforcement: Active")
            logger.info(f"   Anti-Usury Enforcement: Active")
            logger.info(f"   Gleaning Pool Participation: Active")
            
        except Exception as e:
            logger.error(f"Mining network statistics display error: {e}")
    
    async def test_mining_consensus(self) -> bool:
        """Test mining consensus by simulating mining rounds"""
        try:
            logger.info("\n🧪 TESTING MINING CONSENSUS")
            logger.info("-" * 30)
            
            if not self.mining_nodes:
                logger.error("No mining nodes available for consensus testing")
                return False
            
            # Get a mining node with coordinator
            coordinator_node = None
            for mining_node in self.mining_nodes:
                if hasattr(mining_node, 'mining_coordinator') and mining_node.mining_coordinator:
                    coordinator_node = mining_node
                    break
            
            if coordinator_node:
                # Get network mining status
                network_status = coordinator_node.get_network_mining_status()
                logger.info(f"📊 Network Mining Status:")
                logger.info(f"   Status: {network_status.get('network_status', 'unknown')}")
                logger.info(f"   Active Miners: {network_status.get('active_miners', 0)}")
                logger.info(f"   Total Blocks Coordinated: {network_status.get('total_blocks_coordinated', 0)}")
                logger.info(f"   Average Block Time: {network_status.get('average_block_time', 0)} seconds")
                
                return True
            else:
                logger.warning("No mining coordinator found for consensus testing")
                return False
            
        except Exception as e:
            logger.error(f"Mining consensus test error: {e}")
            return False
    
    async def shutdown_mining_network(self):
        """Gracefully shutdown the mining network"""
        try:
            logger.info("🛑 Shutting down ONNYX P2P Mining Network...")
            
            # Stop mining nodes
            for mining_node in self.mining_nodes:
                await mining_node.stop()
            
            # Stop tribal elder nodes
            for elder in self.tribal_elder_nodes:
                await elder.stop()
            
            # Stop bootstrap node
            if self.bootstrap_node:
                await self.bootstrap_node.stop()
            
            self.running = False
            logger.info("✅ Mining network shutdown complete")
            
        except Exception as e:
            logger.error(f"Mining network shutdown error: {e}")
    
    def get_mining_network_status(self) -> dict:
        """Get comprehensive mining network status"""
        return {
            "running": self.running,
            "bootstrap_node": self.bootstrap_node is not None,
            "tribal_elder_nodes": len(self.tribal_elder_nodes),
            "mining_nodes": len(self.mining_nodes),
            "total_nodes": 1 + len(self.tribal_elder_nodes) + len(self.mining_nodes),
            "deployment_time": int(time.time())
        }

async def main():
    """Main function to deploy and test the ONNYX P2P mining network"""
    logger.info("🌟 ONNYX P2P MINING NETWORK DEPLOYMENT")
    logger.info("=" * 70)
    
    deployment = P2PMiningDeployment()
    
    try:
        # Deploy the mining network
        success = await deployment.deploy_mining_network()
        
        if success:
            logger.info("\n🎉 MINING NETWORK DEPLOYMENT SUCCESSFUL!")
            
            # Test mining consensus
            await deployment.test_mining_consensus()
            
            # Keep network running for demonstration
            logger.info("\n⏳ Mining network running... Press Ctrl+C to shutdown")
            
            try:
                while deployment.running:
                    await asyncio.sleep(60)
                    
                    # Periodic health check
                    status = deployment.get_mining_network_status()
                    logger.info(f"💓 Mining network heartbeat: {status['total_nodes']} nodes running")
                    
            except KeyboardInterrupt:
                logger.info("\n🛑 Shutdown requested by user")
        else:
            logger.error("\n❌ MINING NETWORK DEPLOYMENT FAILED!")
            
    except Exception as e:
        logger.error(f"Deployment error: {e}")
    finally:
        # Cleanup
        await deployment.shutdown_mining_network()

if __name__ == '__main__':
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Deployment interrupted by user")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)
