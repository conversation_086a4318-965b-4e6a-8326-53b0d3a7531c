# Biblical Tokenomics Production Deployment Guide

This guide provides step-by-step instructions for deploying the biblical tokenomics system to production.

## Prerequisites

- Existing Onnyx blockchain infrastructure
- Python 3.8+ environment
- Database access (SQLite/PostgreSQL)
- Administrative privileges for system configuration

## Pre-Deployment Checklist

### 1. Backup Current System
```bash
# Backup database
cp onnyx.db onnyx.db.backup.$(date +%Y%m%d_%H%M%S)

# Backup configuration
cp -r shared/config shared/config.backup.$(date +%Y%m%d_%H%M%S)
```

### 2. Verify System Requirements
```bash
# Check Python version
python --version  # Should be 3.8+

# Verify database connectivity
python -c "from shared.db.db import db; print('Database connection: OK')"

# Check existing blockchain state
python -c "from blockchain.consensus.miner import Miner; print('Mining system: OK')"
```

## Deployment Steps

### Step 1: Database Migration

**⚠️ CRITICAL: Perform during maintenance window**

```bash
# 1. Stop all blockchain services
sudo systemctl stop onnyx-node
sudo systemctl stop onnyx-api

# 2. Run the migration script
python scripts/migrate_tokenomics.py

# 3. Verify migration success
python -c "
from shared.db.db import db
tables = db.query('SELECT name FROM sqlite_master WHERE type=\"table\"')
tokenomics_tables = ['jubilee_pools', 'dormant_accounts', 'deeds_ledger', 'loans', 'token_classes', 'sabbath_periods']
for table in tokenomics_tables:
    if any(t['name'] == table for t in tables):
        print(f'✅ {table} table created')
    else:
        print(f'❌ {table} table missing')
"
```

### Step 2: Configuration Validation

```bash
# Verify tokenomics parameters
python -c "
from shared.config.chain_parameters import chain_parameters
required_params = [
    'min_block_reward', 'max_block_reward', 'gleaning_pool_percentage',
    'deed_score_multiplier', 'concentration_threshold', 'sabbath_start_day'
]
for param in required_params:
    value = chain_parameters.get(param)
    print(f'{param}: {value}')
"
```

### Step 3: System Integration Testing

```bash
# Run comprehensive test suite
python tests/test_tokenomics.py

# Expected output: All tests pass
# If any tests fail, DO NOT proceed to production
```

### Step 4: API Endpoint Verification

```bash
# Start the API server in test mode
python -m web.app &
API_PID=$!

# Test critical endpoints
curl -s http://localhost:5000/api/tokenomics/sabbath/status | jq .
curl -s http://localhost:5000/api/tokenomics/gleaning-pool | jq .

# Stop test server
kill $API_PID
```

### Step 5: Mining System Integration

```bash
# Test mining reward calculation
python -c "
from shared.models.tokenomics import biblical_tokenomics
from blockchain.consensus.miner import Miner

# Test reward calculation
miner = Miner()
rewards = miner.create_coinbase_tx('test_proposer_123', 1000)
print(f'Mining integration test: {len(rewards)} transactions created')
"
```

## Production Configuration

### Environment Variables
```bash
# Add to production environment
export ONNYX_TOKENOMICS_ENABLED=true
export ONNYX_SABBATH_ENFORCEMENT=true
export ONNYX_CONCENTRATION_MONITORING=true
```

### Chain Parameters Tuning

For production, consider adjusting these parameters:

```python
# In shared/config/chain_parameters.py
PRODUCTION_OVERRIDES = {
    # Reduce gleaning pool percentage for initial rollout
    "gleaning_pool_percentage": 0.01,  # 1% instead of 2%
    
    # Conservative concentration threshold
    "concentration_threshold": 500000,  # Lower threshold initially
    
    # Longer grace periods for loans
    "loan_grace_blocks": 21600,  # 15 days instead of 10
    
    # Reduced deed score impact
    "deed_score_multiplier": 0.05,  # 5% max bonus instead of 10%
}
```

### Monitoring Setup

```bash
# Create monitoring script
cat > scripts/monitor_tokenomics.py << 'EOF'
#!/usr/bin/env python3
import time
import logging
from shared.models.tokenomics import biblical_tokenomics
from shared.db.db import db

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("tokenomics.monitor")

def monitor_system():
    """Monitor biblical tokenomics system health."""
    
    # Check gleaning pool balance
    pool = db.query_one("SELECT total_amount FROM jubilee_pools WHERE pool_id = 'GLEANS_POOL'")
    pool_balance = pool['total_amount'] if pool else 0
    logger.info(f"Gleaning pool balance: {pool_balance}")
    
    # Check active loans
    loans = db.query("SELECT COUNT(*) as count FROM loans WHERE status = 'ACTIVE'")
    loan_count = loans[0]['count'] if loans else 0
    logger.info(f"Active loans: {loan_count}")
    
    # Check Sabbath status
    is_sabbath = biblical_tokenomics.is_sabbath_period()
    logger.info(f"Sabbath period: {is_sabbath}")
    
    # Check recent deeds
    recent_deeds = db.query("SELECT COUNT(*) as count FROM deeds_ledger WHERE timestamp > ?", 
                           (int(time.time()) - 86400,))
    deed_count = recent_deeds[0]['count'] if recent_deeds else 0
    logger.info(f"Deeds in last 24h: {deed_count}")

if __name__ == "__main__":
    monitor_system()
EOF

chmod +x scripts/monitor_tokenomics.py
```

## Post-Deployment Verification

### 1. System Health Check
```bash
# Run monitoring script
python scripts/monitor_tokenomics.py

# Check API endpoints
curl http://localhost:5000/api/tokenomics/sabbath/status
curl http://localhost:5000/api/tokenomics/gleaning-pool
```

### 2. Mining Verification
```bash
# Monitor mining logs for biblical tokenomics integration
tail -f /var/log/onnyx/mining.log | grep -i "tokenomics\|sabbath\|gleaning\|deed"
```

### 3. Database Integrity
```bash
# Verify data consistency
python -c "
from shared.db.db import db

# Check for orphaned records
orphaned_deeds = db.query('''
    SELECT COUNT(*) as count FROM deeds_ledger d 
    LEFT JOIN identities i ON d.identity_id = i.identity_id 
    WHERE i.identity_id IS NULL
''')
print(f'Orphaned deeds: {orphaned_deeds[0][\"count\"]}')

# Check pool integrity
pools = db.query('SELECT pool_id, total_amount FROM jubilee_pools')
for pool in pools:
    print(f'Pool {pool[\"pool_id\"]}: {pool[\"total_amount\"]} tokens')
"
```

## Rollback Procedure

If issues arise, follow this rollback procedure:

```bash
# 1. Stop services
sudo systemctl stop onnyx-node onnyx-api

# 2. Restore database backup
cp onnyx.db.backup.YYYYMMDD_HHMMSS onnyx.db

# 3. Restore configuration
rm -rf shared/config
cp -r shared/config.backup.YYYYMMDD_HHMMSS shared/config

# 4. Restart services
sudo systemctl start onnyx-node onnyx-api

# 5. Verify system is operational
python -c "from blockchain.consensus.miner import Miner; print('System restored')"
```

## Maintenance Tasks

### Daily
- Monitor gleaning pool balance
- Check for loan forgiveness opportunities
- Verify Sabbath enforcement

### Weekly
- Review deed scoring patterns
- Analyze concentration violations
- Update documentation if needed

### Monthly
- Evaluate parameter effectiveness
- Review system performance metrics
- Plan parameter adjustments if needed

## Troubleshooting

### Common Issues

**Issue: Mining rewards not calculating correctly**
```bash
# Check tokenomics module import
python -c "from shared.models.tokenomics import biblical_tokenomics; print('Import OK')"

# Verify deed score calculation
python -c "
from shared.models.tokenomics import biblical_tokenomics
score = biblical_tokenomics._get_deed_score('test_identity')
print(f'Deed score test: {score}')
"
```

**Issue: API endpoints returning errors**
```bash
# Check database connection
python -c "from shared.db.db import db; db.query('SELECT 1')"

# Verify chain parameters
python -c "from shared.config.chain_parameters import chain_parameters; print(chain_parameters.get('min_block_reward'))"
```

**Issue: Sabbath enforcement not working**
```bash
# Test Sabbath detection
python -c "
from shared.models.tokenomics import biblical_tokenomics
print(f'Sabbath status: {biblical_tokenomics.is_sabbath_period()}')
"
```

## Support and Documentation

- **Technical Documentation**: `docs/BIBLICAL_TOKENOMICS.md`
- **API Reference**: `docs/API_REFERENCE.md`
- **Test Suite**: `tests/test_tokenomics.py`
- **Demo Script**: `scripts/demo_tokenomics.py`

## Security Considerations

1. **Database Security**: Ensure proper access controls on tokenomics tables
2. **API Authentication**: Verify all tokenomics endpoints require proper authentication
3. **Parameter Validation**: All user inputs are validated before processing
4. **Audit Logging**: All tokenomics operations are logged for audit purposes

## Performance Optimization

1. **Database Indexing**: All tokenomics tables have appropriate indexes
2. **Caching**: Consider caching deed scores and concentration status
3. **Batch Processing**: Loan forgiveness and jubilee operations use batch processing
4. **Monitoring**: Set up alerts for performance degradation

---

**Deployment Complete! 🎉**

The biblical tokenomics system is now ready for production use. Monitor the system closely during the first few days and be prepared to adjust parameters based on real-world usage patterns.
