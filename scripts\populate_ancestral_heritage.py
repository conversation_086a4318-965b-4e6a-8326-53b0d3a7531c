#!/usr/bin/env python3
"""
Populate Ancestral Heritage Data for Witness Nations
Implements two-tier ancestral lineage system for Eden Mode Step 2
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

from shared.db.db import db
import time

def populate_ancestral_heritage():
    """Populate ancestral heritage data for witness nations."""
    print("🌍 POPULATING ANCESTRAL HERITAGE SYSTEM")
    print("=" * 50)
    print("Creating two-tier ancestral lineage for 35 witness nations")

    try:
        # Add ancestral columns if they don't exist
        try:
            db.execute('ALTER TABLE biblical_nations ADD COLUMN ancestral_group TEXT')
            print("✅ Added ancestral_group column")
        except:
            print("ℹ️  ancestral_group column already exists")

        try:
            db.execute('ALTER TABLE biblical_nations ADD COLUMN ancestral_description TEXT')
            print("✅ Added ancestral_description column")
        except:
            print("ℹ️  ancestral_description column already exists")

        try:
            db.execute('ALTER TABLE biblical_nations ADD COLUMN historical_connection TEXT')
            print("✅ Added historical_connection column")
        except:
            print("ℹ️  historical_connection column already exists")

        # Define ancestral heritage mappings
        ancestral_heritage = {
            # Sons of Japheth (European/Indo-European peoples)
            'GERMANY': {
                'ancestral_group': 'Germanic Tribes',
                'ancestral_description': 'Descendants of ancient Germanic peoples who spread across Northern Europe',
                'historical_connection': 'From the Teutonic tribes mentioned in ancient texts, builders of great kingdoms'
            },
            'AUSTRIA': {
                'ancestral_group': 'Germanic Tribes',
                'ancestral_description': 'Alpine Germanic peoples with Celtic influences',
                'historical_connection': 'Mountain dwellers who preserved ancient traditions and craftsmanship'
            },
            'SWITZERLAND': {
                'ancestral_group': 'Germanic Tribes',
                'ancestral_description': 'Alpine confederation of Germanic and Celtic tribes',
                'historical_connection': 'Mountain covenant peoples known for independence and precision'
            },
            'NETHERLANDS': {
                'ancestral_group': 'Germanic Tribes',
                'ancestral_description': 'Low country Germanic peoples, masters of water and trade',
                'historical_connection': 'Descendants of Frisian and Frankish tribes, maritime covenant keepers'
            },
            'DENMARK': {
                'ancestral_group': 'Nordic Peoples',
                'ancestral_description': 'Northern Germanic seafaring peoples',
                'historical_connection': 'Viking heritage, explorers and traders of the northern seas'
            },
            'NORWAY': {
                'ancestral_group': 'Nordic Peoples',
                'ancestral_description': 'Mountain and fjord dwelling Nordic peoples',
                'historical_connection': 'Ancient Norse traditions, keepers of northern wisdom'
            },
            'SWEDEN': {
                'ancestral_group': 'Nordic Peoples',
                'ancestral_description': 'Forest and lake dwelling Nordic peoples',
                'historical_connection': 'Svear and Götar tribes, northern kingdom builders'
            },
            'FINLAND': {
                'ancestral_group': 'Finno-Ugric Peoples',
                'ancestral_description': 'Unique Finno-Ugric heritage distinct from Indo-European roots',
                'historical_connection': 'Ancient Suomi peoples, forest dwellers with shamanic traditions'
            },

            # Celtic Peoples
            'IRELAND': {
                'ancestral_group': 'Celtic Peoples',
                'ancestral_description': 'Gaelic Celtic heritage with ancient druidic wisdom',
                'historical_connection': 'Isle of saints and scholars, preservers of ancient knowledge'
            },
            'SCOTLAND': {
                'ancestral_group': 'Celtic Peoples',
                'ancestral_description': 'Highland Celtic clans with Pictish influences',
                'historical_connection': 'Fierce highland warriors and keepers of clan traditions'
            },
            'BRITAIN': {
                'ancestral_group': 'Celtic-Anglo-Saxon',
                'ancestral_description': 'Mixed Celtic and Anglo-Saxon heritage',
                'historical_connection': 'Ancient Britons merged with Germanic settlers, island covenant people'
            },
            'FRANCE': {
                'ancestral_group': 'Celtic-Frankish',
                'ancestral_description': 'Gallic Celtic peoples merged with Frankish Germanic tribes',
                'historical_connection': 'Gaul transformed by Frankish kingdoms, eldest daughter of the church'
            },

            # Slavic Peoples
            'RUSSIA': {
                'ancestral_group': 'Slavic Peoples',
                'ancestral_description': 'Eastern Slavic peoples with Byzantine Christian heritage',
                'historical_connection': 'Rus peoples, Third Rome, keepers of Eastern Orthodox tradition'
            },
            'POLAND': {
                'ancestral_group': 'Slavic Peoples',
                'ancestral_description': 'Western Slavic peoples, defenders of Christendom',
                'historical_connection': 'Polanie tribes, bulwark against eastern invasions'
            },
            'CZECH': {
                'ancestral_group': 'Slavic Peoples',
                'ancestral_description': 'Bohemian Slavic peoples with Germanic influences',
                'historical_connection': 'Czech tribes of Bohemia, heart of the Holy Roman Empire'
            },
            'SLOVAKIA': {
                'ancestral_group': 'Slavic Peoples',
                'ancestral_description': 'Slovak peoples of the Carpathian region',
                'historical_connection': 'Mountain Slavs, crossroads of Central Europe'
            },
            'BULGARIA': {
                'ancestral_group': 'Slavic-Turkic',
                'ancestral_description': 'South Slavic peoples with Bulgar Turkic heritage',
                'historical_connection': 'First Bulgarian Empire, bridge between East and West'
            },
            'ROMANIA': {
                'ancestral_group': 'Daco-Roman',
                'ancestral_description': 'Dacian peoples Romanized under Trajan',
                'historical_connection': 'Ancient Dacia, Latin island in Slavic sea'
            },
            'HUNGARY': {
                'ancestral_group': 'Magyar Peoples',
                'ancestral_description': 'Unique Magyar heritage from the Eurasian steppes',
                'historical_connection': 'Nomadic Magyar tribes, defenders of Christian Europe'
            },

            # Mediterranean Peoples
            'ITALY': {
                'ancestral_group': 'Italic Peoples',
                'ancestral_description': 'Ancient Italic tribes including Romans, Latins, and Etruscans',
                'historical_connection': 'Heart of the Roman Empire, seat of Western Christianity'
            },
            'SPAIN': {
                'ancestral_group': 'Iberian-Visigothic',
                'ancestral_description': 'Iberian peoples with Visigothic and Moorish influences',
                'historical_connection': 'Ancient Hispania, reconquest and global exploration'
            },
            'GREECE': {
                'ancestral_group': 'Hellenic Peoples',
                'ancestral_description': 'Ancient Greek heritage, cradle of philosophy and democracy',
                'historical_connection': 'Birthplace of Western civilization and Eastern Christianity'
            },

            # Ancient Near Eastern Peoples
            'ISRAEL': {
                'ancestral_group': 'Abrahamic Covenant',
                'ancestral_description': 'Chosen people of the Abrahamic covenant, witnesses to the nations',
                'historical_connection': 'Exodus 19:6 - "You shall be to me a kingdom of priests and a holy nation"'
            },
            'ARMENIA': {
                'ancestral_group': 'Armenian Peoples',
                'ancestral_description': 'Ancient Armenian heritage, first Christian nation',
                'historical_connection': 'Descendants of Haik, first nation to adopt Christianity officially'
            },
            'PERSIA': {
                'ancestral_group': 'Persian Peoples',
                'ancestral_description': 'Ancient Persian empire builders and Zoroastrian heritage',
                'historical_connection': 'Cyrus the Great, liberator of exiles, mentioned in biblical prophecy'
            },
            'BABYLON': {
                'ancestral_group': 'Mesopotamian Peoples',
                'ancestral_description': 'Ancient Babylonian civilization, cradle of human civilization',
                'historical_connection': 'Land between rivers, birthplace of writing and law'
            },
            'ARABIA': {
                'ancestral_group': 'Semitic Peoples',
                'ancestral_description': 'Arabian Semitic peoples, desert covenant keepers',
                'historical_connection': 'Descendants of Ishmael, keepers of desert wisdom'
            },
            'EGYPT': {
                'ancestral_group': 'Hamitic Peoples',
                'ancestral_description': 'Ancient Egyptian civilization, sons of Ham',
                'historical_connection': 'Land of the Pharaohs, biblical Egypt, place of exile and exodus'
            },
            'ETHIOPIA': {
                'ancestral_group': 'Cushite Peoples',
                'ancestral_description': 'Ancient Cushite kingdom, biblical Ethiopia',
                'historical_connection': 'Kingdom of Cush, mentioned throughout biblical prophecy'
            },

            # Asian Peoples
            'CHINA': {
                'ancestral_group': 'Sinic Peoples',
                'ancestral_description': 'Ancient Chinese civilization, Middle Kingdom heritage',
                'historical_connection': 'Celestial Empire, keepers of ancient wisdom and harmony'
            },
            'JAPAN': {
                'ancestral_group': 'Yamato Peoples',
                'ancestral_description': 'Island nation with unique Yamato heritage',
                'historical_connection': 'Land of the Rising Sun, island covenant of honor and tradition'
            },
            'KOREA': {
                'ancestral_group': 'Korean Peoples',
                'ancestral_description': 'Peninsula people with distinct Korean heritage',
                'historical_connection': 'Hermit Kingdom, bridge between continental powers'
            },
            'MONGOLIA': {
                'ancestral_group': 'Mongolic Peoples',
                'ancestral_description': 'Nomadic steppe peoples, great empire builders',
                'historical_connection': 'Descendants of Genghis Khan, masters of the steppes'
            },
            'INDIA': {
                'ancestral_group': 'Indo-Aryan Peoples',
                'ancestral_description': 'Ancient Indus Valley and Vedic heritage',
                'historical_connection': 'Cradle of spirituality, land of ancient wisdom traditions'
            },

            # Classical Empires
            'ROME': {
                'ancestral_group': 'Roman Peoples',
                'ancestral_description': 'Eternal City, heart of the ancient Roman Empire',
                'historical_connection': 'Caput Mundi, center of law, governance, and early Christianity'
            }
        }

        # Update each witness nation with ancestral heritage
        updated_count = 0
        for nation_code, heritage in ancestral_heritage.items():
            try:
                # Check if nation exists first
                existing = db.query_one('SELECT nation_code FROM biblical_nations WHERE nation_code = ? AND nation_type = ?', [nation_code, 'witness'])

                if existing:
                    db.execute('''
                        UPDATE biblical_nations
                        SET ancestral_group = ?,
                            ancestral_description = ?,
                            historical_connection = ?
                        WHERE nation_code = ? AND nation_type = 'witness'
                    ''', (
                        heritage['ancestral_group'],
                        heritage['ancestral_description'],
                        heritage['historical_connection'],
                        nation_code
                    ))

                    print(f"   ✅ Updated {nation_code:12s} - {heritage['ancestral_group']}")
                    updated_count += 1
                else:
                    print(f"   ⚠️  Nation {nation_code} not found as witness nation")

            except Exception as e:
                print(f"   ❌ Error updating {nation_code}: {e}")

        # Verify the population
        ancestral_groups = db.query("""
            SELECT ancestral_group, COUNT(*) as count
            FROM biblical_nations
            WHERE nation_type = 'witness' AND ancestral_group IS NOT NULL
            GROUP BY ancestral_group
            ORDER BY ancestral_group
        """)

        print(f"\n🎉 Ancestral heritage populated!")
        print(f"📊 Updated nations: {updated_count}")
        print(f"📋 Ancestral groups created: {len(ancestral_groups)}")

        print("\n🌍 Ancestral Groups Summary:")
        for group in ancestral_groups:
            print(f"   {group['ancestral_group']:20s}: {group['count']} nations")

        return True

    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = populate_ancestral_heritage()
    if success:
        print("\n✅ Ancestral heritage system successfully populated!")
        print("🌟 Eden Mode Step 2 now supports two-tier ancestral selection.")
    else:
        print("\n❌ Population failed. Please check the error messages above.")
