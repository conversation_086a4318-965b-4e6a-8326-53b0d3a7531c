#!/usr/bin/env python3
"""
Labor System Migration Script
Creates all necessary tables for the Covenant Labor System and Biblical Tokenomics
"""

import os
import sys
import time
import sqlite3

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

def get_db_path():
    """Get the database path."""
    return os.path.join(os.path.dirname(__file__), '..', 'shared', 'db', 'onnyx.db')

def create_labor_system_tables():
    """Create all labor system tables."""
    db_path = get_db_path()
    
    if not os.path.exists(db_path):
        print(f"❌ Database not found at: {db_path}")
        return False
    
    try:
        print("🛠️ Creating Labor System tables...")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Labor records table
        print("📊 Creating labor_records table...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS labor_records (
                labor_id TEXT PRIMARY KEY,
                identity_id TEXT NOT NULL,
                sela_id TEXT,
                labor_type TEXT NOT NULL,
                description TEXT NOT NULL,
                value_estimate REAL DEFAULT 0.0,
                timestamp INTEGER NOT NULL,
                verified_by TEXT,
                verification_status TEXT DEFAULT 'pending',
                mikvah_eligible BOOLEAN DEFAULT 1,
                etzem_points INTEGER DEFAULT 0,
                season_period INTEGER,
                metadata TEXT DEFAULT '{}',
                created_at INTEGER NOT NULL,
                updated_at INTEGER NOT NULL,
                FOREIGN KEY (identity_id) REFERENCES identities(identity_id),
                FOREIGN KEY (sela_id) REFERENCES selas(sela_id),
                FOREIGN KEY (verified_by) REFERENCES identities(identity_id)
            )
        """)
        
        # Labor verifications table
        print("✅ Creating labor_verifications table...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS labor_verifications (
                verification_id TEXT PRIMARY KEY,
                labor_id TEXT NOT NULL,
                verifier_id TEXT NOT NULL,
                verification_type TEXT NOT NULL,
                verification_notes TEXT,
                etzem_adjustment INTEGER DEFAULT 0,
                timestamp INTEGER NOT NULL,
                FOREIGN KEY (labor_id) REFERENCES labor_records(labor_id),
                FOREIGN KEY (verifier_id) REFERENCES identities(identity_id)
            )
        """)
        
        # Mikvah transactions table
        print("🪙 Creating mikvah_transactions table...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS mikvah_transactions (
                transaction_id TEXT PRIMARY KEY,
                identity_id TEXT NOT NULL,
                labor_id TEXT,
                transaction_type TEXT NOT NULL,
                amount REAL NOT NULL,
                balance_before REAL DEFAULT 0.0,
                balance_after REAL DEFAULT 0.0,
                season_period INTEGER,
                yovel_cycle INTEGER,
                metadata TEXT DEFAULT '{}',
                timestamp INTEGER NOT NULL,
                FOREIGN KEY (identity_id) REFERENCES identities(identity_id),
                FOREIGN KEY (labor_id) REFERENCES labor_records(labor_id)
            )
        """)
        
        # Seasonal labor summaries table
        print("📈 Creating seasonal_labor_summaries table...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS seasonal_labor_summaries (
                summary_id TEXT PRIMARY KEY,
                identity_id TEXT NOT NULL,
                season_period INTEGER NOT NULL,
                season_start INTEGER NOT NULL,
                season_end INTEGER NOT NULL,
                total_labor_count INTEGER DEFAULT 0,
                total_value_estimate REAL DEFAULT 0.0,
                total_etzem_earned INTEGER DEFAULT 0,
                total_mikvah_earned REAL DEFAULT 0.0,
                verification_rate REAL DEFAULT 0.0,
                sabbath_compliance BOOLEAN DEFAULT 1,
                season_completed BOOLEAN DEFAULT 0,
                FOREIGN KEY (identity_id) REFERENCES identities(identity_id)
            )
        """)
        
        # Gleaning pool table
        print("🌾 Creating gleaning_pool table...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS gleaning_pool (
                pool_id TEXT PRIMARY KEY,
                season_period INTEGER NOT NULL,
                total_contributions REAL DEFAULT 0.0,
                total_distributions REAL DEFAULT 0.0,
                current_balance REAL DEFAULT 0.0,
                contributor_count INTEGER DEFAULT 0,
                recipient_count INTEGER DEFAULT 0,
                last_updated INTEGER NOT NULL
            )
        """)
        
        # Gleaning distributions table
        print("🤝 Creating gleaning_distributions table...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS gleaning_distributions (
                distribution_id TEXT PRIMARY KEY,
                identity_id TEXT NOT NULL,
                pool_id TEXT NOT NULL,
                amount REAL NOT NULL,
                reason TEXT,
                approved_by TEXT,
                timestamp INTEGER NOT NULL,
                FOREIGN KEY (identity_id) REFERENCES identities(identity_id),
                FOREIGN KEY (pool_id) REFERENCES gleaning_pool(pool_id),
                FOREIGN KEY (approved_by) REFERENCES identities(identity_id)
            )
        """)
        
        # Sabbath enforcement table
        print("🕯️ Creating sabbath_enforcement table...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS sabbath_enforcement (
                period_id TEXT PRIMARY KEY,
                start_timestamp INTEGER NOT NULL,
                end_timestamp INTEGER NOT NULL,
                period_type TEXT NOT NULL,
                active BOOLEAN DEFAULT 1,
                created_at INTEGER NOT NULL
            )
        """)
        
        # Create indexes
        print("🔍 Creating indexes...")
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_labor_records_identity ON labor_records(identity_id)",
            "CREATE INDEX IF NOT EXISTS idx_labor_records_sela ON labor_records(sela_id)",
            "CREATE INDEX IF NOT EXISTS idx_labor_records_type ON labor_records(labor_type)",
            "CREATE INDEX IF NOT EXISTS idx_labor_records_status ON labor_records(verification_status)",
            "CREATE INDEX IF NOT EXISTS idx_labor_records_timestamp ON labor_records(timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_labor_records_season ON labor_records(season_period)",
            "CREATE INDEX IF NOT EXISTS idx_labor_verifications_labor ON labor_verifications(labor_id)",
            "CREATE INDEX IF NOT EXISTS idx_labor_verifications_verifier ON labor_verifications(verifier_id)",
            "CREATE INDEX IF NOT EXISTS idx_mikvah_transactions_identity ON mikvah_transactions(identity_id)",
            "CREATE INDEX IF NOT EXISTS idx_mikvah_transactions_type ON mikvah_transactions(transaction_type)",
            "CREATE INDEX IF NOT EXISTS idx_mikvah_transactions_timestamp ON mikvah_transactions(timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_seasonal_summaries_identity ON seasonal_labor_summaries(identity_id)",
            "CREATE INDEX IF NOT EXISTS idx_seasonal_summaries_period ON seasonal_labor_summaries(season_period)",
            "CREATE INDEX IF NOT EXISTS idx_gleaning_distributions_identity ON gleaning_distributions(identity_id)",
            "CREATE INDEX IF NOT EXISTS idx_gleaning_distributions_timestamp ON gleaning_distributions(timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_sabbath_enforcement_timestamps ON sabbath_enforcement(start_timestamp, end_timestamp)"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        # Commit changes
        conn.commit()
        
        # Add Mikvah balance column to identities if not exists
        print("💰 Adding mikvah_balance column to identities...")
        try:
            cursor.execute("ALTER TABLE identities ADD COLUMN mikvah_balance REAL DEFAULT 0.0")
        except sqlite3.OperationalError as e:
            if "duplicate column name" in str(e):
                print("  Column already exists, skipping...")
            else:
                raise
        
        # Close connection
        conn.close()
        
        print("✅ Labor System tables created successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create Labor System tables: {e}")
        return False

def create_initial_sabbath_periods():
    """Create initial sabbath enforcement periods."""
    try:
        from shared.db.db import db
        
        print("🕯️ Creating initial sabbath periods...")
        
        current_time = int(time.time())
        
        # Create a weekly sabbath period (Friday evening to Saturday evening)
        # For demo purposes, create a short sabbath period
        sabbath_start = current_time + 3600  # 1 hour from now
        sabbath_end = sabbath_start + 7200   # 2 hours duration
        
        period_id = f"SABBATH_WEEKLY_{current_time}"
        
        db.execute("""
            INSERT INTO sabbath_enforcement 
            (period_id, start_timestamp, end_timestamp, period_type, active, created_at)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (period_id, sabbath_start, sabbath_end, 'weekly', True, current_time))
        
        print(f"✅ Created demo sabbath period: {period_id}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create sabbath periods: {e}")
        return False

def create_initial_gleaning_pool():
    """Create initial gleaning pool for current season."""
    try:
        from shared.db.db import db
        from blockchain.tokenomics.mikvah_tokens import MikvahTokenManager
        
        print("🌾 Creating initial gleaning pool...")
        
        mikvah_manager = MikvahTokenManager(db)
        current_season = mikvah_manager._get_current_season()
        current_time = int(time.time())
        
        pool_id = f"POOL_{current_season}_{current_time}"
        
        db.execute("""
            INSERT INTO gleaning_pool 
            (pool_id, season_period, total_contributions, current_balance, 
             contributor_count, last_updated)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (pool_id, current_season, 0.0, 0.0, 0, current_time))
        
        print(f"✅ Created initial gleaning pool: {pool_id}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create gleaning pool: {e}")
        return False

def update_existing_identities():
    """Update existing identities with default Mikvah balance."""
    try:
        from shared.db.db import db
        
        print("👥 Updating existing identities with Mikvah balance...")
        
        # Update all identities to have 0.0 Mikvah balance if NULL
        db.execute("""
            UPDATE identities 
            SET mikvah_balance = 0.0 
            WHERE mikvah_balance IS NULL
        """)
        
        # Get count of updated identities
        result = db.query_one("SELECT COUNT(*) as count FROM identities")
        count = result['count'] if result else 0
        
        print(f"✅ Updated {count} identities with Mikvah balance")
        return True
        
    except Exception as e:
        print(f"❌ Failed to update identities: {e}")
        return False

def main():
    """Main migration function."""
    print("🪙 Starting Labor System Migration...")
    print("=" * 50)
    
    # Create tables
    if not create_labor_system_tables():
        return False
    
    # Create initial data
    if not create_initial_sabbath_periods():
        return False
    
    if not create_initial_gleaning_pool():
        return False
    
    if not update_existing_identities():
        return False
    
    print("=" * 50)
    print("🎉 Labor System Migration completed successfully!")
    print()
    print("📋 Summary:")
    print("  ✅ Labor records table created")
    print("  ✅ Labor verifications table created")
    print("  ✅ Mikvah transactions table created")
    print("  ✅ Seasonal summaries table created")
    print("  ✅ Gleaning pool table created")
    print("  ✅ Gleaning distributions table created")
    print("  ✅ Sabbath enforcement table created")
    print("  ✅ All indexes created")
    print("  ✅ Initial sabbath periods created")
    print("  ✅ Initial gleaning pool created")
    print("  ✅ Existing identities updated")
    print()
    print("🚀 The Covenant Labor System is now ready!")
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
